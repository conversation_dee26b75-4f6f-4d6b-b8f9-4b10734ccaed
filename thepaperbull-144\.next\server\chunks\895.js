exports.id=895,exports.ids=[895],exports.modules={3143:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},6955:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,O:()=>o});var s=r(60687),i=r(43210);r(50227),r(80043);var a=r(95216);let n=(0,i.createContext)(void 0);function o({children:e}){let[t,r]=(0,i.useState)(null),[o,c]=(0,i.useState)(!0),u=async(e,t)=>{try{return await (0,a.Ru)(e,t)}catch(e){throw console.error("Sign in error:",e),e}},l=async()=>{try{await (0,a.signOutUser)()}catch(e){throw console.error("Sign out error:",e),e}},d=async()=>{try{return await (0,a.G6)()}catch(e){throw console.error("Google sign in error:",e),e}};return(0,s.jsx)(n.Provider,{value:{user:t,loading:o,signIn:u,signOut:l,signInWithGoogle:d},children:e})}function c(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},21643:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(83427),i=r(80043),a=r(50227),n=r(70695),o=r(38239);function c(e){return{id:e.id,email:e.email,name:e.name,createdAt:e.createdAt?.toMillis?.()||Date.now(),subscription:{type:e.subscription.type,status:e.subscription.status,startDate:e.subscription.startDate?.toMillis?.()||Date.now(),endDate:e.subscription.endDate?.toMillis?.(),features:e.subscription.features},balance:{current:e.balance.current,maximum:e.balance.maximum,currency:e.balance.currency,lastUpdated:e.balance.lastUpdated?.toMillis?.()||Date.now(),transactions:[]},settings:e.settings}}class u{constructor(){this.currentUser=null,this.firebaseUser=null,this.subscribers=[],this.unsubscribeAuth=null,this.unsubscribeProfile=null,this.transactions=[],this.balanceCache=null,this.BALANCE_CACHE_DURATION=5e3,this.initializeAuth()}initializeAuth(){this.unsubscribeAuth=(0,a.hg)(i.j2,async e=>{this.firebaseUser=e,e?await this.loadUserProfile(e.uid):(this.currentUser=null,this.transactions=[],this.unsubscribeProfile&&(this.unsubscribeProfile(),this.unsubscribeProfile=null),this.notifySubscribers())})}async loadUserProfile(e){try{let t=await s.b.getUserProfile(e);!t&&this.firebaseUser&&(t=await s.b.createUserProfile(e,this.firebaseUser.email||"",this.firebaseUser.displayName||"User"))&&await n.A.createWelcomeNotification(e),t&&(this.currentUser=c(t),this.transactions=await s.b.getUserTransactions(e),this.currentUser.balance.transactions=this.transactions.map(e=>({id:e.id,type:e.type,amount:e.amount,balance_before:e.balance_before,balance_after:e.balance_after,description:e.description,timestamp:e.timestamp?.toMillis?.()||Date.now()})),this.unsubscribeProfile&&this.unsubscribeProfile(),this.unsubscribeProfile=s.b.subscribeToUserProfile(e,e=>{e&&(this.currentUser=c(e),this.notifySubscribers())}),this.notifySubscribers())}catch(e){console.error("Error loading user profile:",e),"permission-denied"===e.code?(console.error("Permission denied - Firestore security rules may need to be updated"),this.firebaseUser&&(this.currentUser={id:this.firebaseUser.uid,email:this.firebaseUser.email||"",name:this.firebaseUser.displayName||"User",avatar:"bull-trader",balance:{current:1e4,maximum:5e4,currency:"USDT",lastUpdated:Date.now(),transactions:[]},subscription:{type:"free",status:"active"},settings:{},createdAt:Date.now(),lastLoginAt:Date.now()},this.notifySubscribers())):(this.currentUser=null,this.notifySubscribers())}}notifySubscribers(){this.balanceCache=null,this.subscribers.forEach(e=>e(this.currentUser))}getUser(){return this.currentUser}getUserBalance(){let e=Date.now();if(this.balanceCache&&e-this.balanceCache.timestamp<this.BALANCE_CACHE_DURATION)return this.balanceCache.value;let t=this.currentUser?.balance?.current||1e4;return this.balanceCache={value:t,timestamp:e},t}getMaxBalance(){return this.currentUser?.balance.maximum||o.sC}getAvailableFunds(){return this.currentUser?this.currentUser.balance.maximum-this.currentUser.balance.current:0}canAddFunds(e){return this.currentUser?e<=0?{canAdd:!1,reason:"Amount must be positive"}:this.currentUser.balance.current+e>this.currentUser.balance.maximum?{canAdd:!1,reason:`Would exceed maximum balance of ${this.currentUser.balance.maximum.toLocaleString()} USDT`}:{canAdd:!0}:{canAdd:!1,reason:"User not found"}}async addFunds(e){if(!this.currentUser||!this.firebaseUser)return{success:!1,new_balance:0,requires_subscription:!1,message:"User not found"};let t=this.canAddFunds(e.amount);if(!t.canAdd){let r=this.currentUser.balance.current+e.amount<=o.ND.premium.maxBalance;return{success:!1,new_balance:this.currentUser.balance.current,requires_subscription:r,message:t.reason||"Cannot add funds"}}try{let t=this.currentUser.balance.current,r=t+e.amount;return await s.b.updateUserBalance(this.firebaseUser.uid,r),await s.b.addTransaction(this.firebaseUser.uid,{type:"deposit",amount:e.amount,balance_before:t,balance_after:r,description:"subscription_upgrade"===e.method?"Funds added via subscription":"Virtual funds added"}),{success:!0,new_balance:r,requires_subscription:!1,message:`Successfully added ${e.amount.toLocaleString()} USDT to your account`}}catch(e){return console.error("Error adding funds:",e),{success:!1,new_balance:this.currentUser.balance.current,requires_subscription:!1,message:"Failed to add funds. Please try again."}}}async upgradeSubscription(e){if(!this.currentUser||!this.firebaseUser)return!1;try{let t=o.ND[e];return await s.b.updateUserProfile(this.firebaseUser.uid,{subscription:{type:e,status:"active",startDate:new Date,endDate:new Date(Date.now()+31536e6),features:t.features},balance:{...this.currentUser.balance,maximum:t.maxBalance}}),!0}catch(e){return console.error("Error upgrading subscription:",e),!1}}async updateBalance(e,t,r){if(this.currentUser&&this.firebaseUser)try{let i=this.currentUser.balance.current;await s.b.updateUserBalance(this.firebaseUser.uid,e),await s.b.addTransaction(this.firebaseUser.uid,{type:t,amount:e-i,balance_before:i,balance_after:e,description:r})}catch(e){console.error("Error updating balance:",e)}}subscribe(e){return this.subscribers.push(e),e(this.currentUser),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}getFirebaseUser(){return this.firebaseUser}destroy(){this.unsubscribeAuth&&this.unsubscribeAuth(),this.unsubscribeProfile&&this.unsubscribeProfile()}}let l=new u},25878:(e,t,r)=>{"use strict";r.d(t,{D:()=>c,N:()=>o});var s=r(60687),i=r(43210),a=r(29052);let n=(0,i.createContext)(void 0);function o({children:e}){let[t,r]=(0,i.useState)("light"),[o,c]=(0,i.useState)("emerald"),[u,l]=(0,i.useState)("light"),[d,h]=(0,i.useState)(!1);return d?(0,s.jsx)(n.Provider,{value:{theme:t,setTheme:e=>{r(e),a.A.updateTheme(e)},colorScheme:o,setColorScheme:e=>{c(e),a.A.updateColorScheme(e)},resolvedTheme:u},children:e}):(0,s.jsx)(s.Fragment,{children:e})}function c(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},29052:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(83427),i=r(21643);class a{constructor(){this.preferences={favoriteMarkets:[],theme:"light",colorScheme:"emerald"},this.subscribers=[],this.isInitialized=!1,i.A.subscribe(e=>{e?this.loadUserPreferences(e.id):this.resetPreferences()})}subscribe(e){return this.subscribers.push(e),this.isInitialized&&e(this.preferences),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.preferences))}async loadUserPreferences(e){try{let t=await s.b.getUserProfile(e);t?.preferences&&(this.preferences={...this.preferences,...t.preferences}),this.isInitialized=!0,this.notifySubscribers()}catch(e){console.error("Error loading user preferences:",e),this.isInitialized=!0,this.notifySubscribers()}}resetPreferences(){this.preferences={favoriteMarkets:[],theme:"light",colorScheme:"emerald"},this.isInitialized=!1}getPreferences(){return{...this.preferences}}async updateFavoriteMarkets(e){let t=i.A.getFirebaseUser();if(t)try{this.preferences.favoriteMarkets=e,await s.b.updateUserProfile(t.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating favorite markets:",e)}}async updateTheme(e){let t=i.A.getFirebaseUser();if(!t){this.preferences.theme=e,this.notifySubscribers();return}try{this.preferences.theme=e,await s.b.updateUserProfile(t.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating theme:",e)}}async updateColorScheme(e){let t=i.A.getFirebaseUser();if(!t){this.preferences.colorScheme=e,this.notifySubscribers();return}try{this.preferences.colorScheme=e,await s.b.updateUserProfile(t.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating color scheme:",e)}}async updateRememberedUsername(e){try{e?this.preferences.rememberedUsername=e:delete this.preferences.rememberedUsername,this.notifySubscribers()}catch(e){console.error("Error updating remembered username:",e)}}getRememberedUsername(){return this.preferences.rememberedUsername?this.preferences.rememberedUsername:null}async toggleFavoriteMarket(e){let t=this.preferences.favoriteMarkets,r=t.includes(e)?t.filter(t=>t!==e):[...t,e];await this.updateFavoriteMarkets(r)}isFavoriteMarket(e){return this.preferences.favoriteMarkets.includes(e)}getFavoriteMarkets(){return[...this.preferences.favoriteMarkets]}getTheme(){return this.preferences.theme}getColorScheme(){return this.preferences.colorScheme}}let n=new a},38239:(e,t,r)=>{"use strict";r.d(t,{ND:()=>s,sC:()=>i});let s={free:{type:"free",name:"Free",maxBalance:5e4,price:0,features:{maxBalance:5e4,advancedAnalytics:!1,prioritySupport:!1,customIndicators:!1,apiAccess:!1}},premium:{type:"premium",name:"Premium",maxBalance:5e5,price:29.99,features:{maxBalance:5e5,advancedAnalytics:!0,prioritySupport:!1,customIndicators:!0,apiAccess:!1}},pro:{type:"pro",name:"Pro",maxBalance:1e6,price:99.99,features:{maxBalance:1e6,advancedAnalytics:!0,prioritySupport:!0,customIndicators:!0,apiAccess:!0}}},i=5e4},55183:(e,t,r)=>{"use strict";r.d(t,{E$:()=>g,NotificationContainer:()=>y});var s=r(60687),i=r(43210),a=r(5336),n=r(93613),o=r(43649),c=r(96882),u=r(11860),l=r(96241);let d={success:a.A,error:n.A,warning:o.A,info:c.A},h={success:"border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100",error:"border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100",warning:"border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-100",info:"border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-100"},b={"top-right":"top-4 right-4","top-left":"top-4 left-4","bottom-right":"bottom-4 right-4","bottom-left":"bottom-4 left-4","top-center":"top-4 left-1/2 transform -translate-x-1/2","bottom-center":"bottom-4 left-1/2 transform -translate-x-1/2"};function p({id:e,type:t="info",title:r,message:a,duration:n=4e3,onClose:o,closable:c=!0,position:p="top-right"}){let[f,m]=(0,i.useState)(!0),[g,y]=(0,i.useState)(!1),v=d[t];return f?(0,s.jsx)("div",{className:(0,l.cn)("fixed z-50 w-full max-w-sm p-4 border rounded-lg shadow-lg backdrop-blur-sm","transition-all duration-300 ease-in-out",!g&&"animate-slideInFromTop",g&&"animate-slideOutToRight",h[t],b[p]),style:{animation:g?"slideOutToRight 0.3s ease-in":"slideInFromTop 0.3s ease-out"},role:"alert","aria-live":"polite",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(v,{className:"h-5 w-5 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[r&&(0,s.jsx)("div",{className:"font-medium text-sm mb-1",children:r}),(0,s.jsx)("div",{className:"text-sm",children:a})]}),c&&(0,s.jsx)("button",{onClick:()=>{y(!0),setTimeout(()=>{m(!1),o?.()},300)},className:(0,l.cn)("flex-shrink-0 p-1 rounded-md transition-colors duration-200","hover:bg-black/10 dark:hover:bg-white/10","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current","text-current/70 hover:text-current"),"aria-label":"Close notification",children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})]})}):null}class f{generateId(){return`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}notify(){this.listeners.forEach(e=>e())}show(e){let t=this.generateId(),r={...e,id:t,onClose:()=>this.remove(t)};return this.notifications.set(t,r),this.notify(),t}remove(e){this.notifications.delete(e),this.notify()}clear(){this.notifications.clear(),this.notify()}getAll(){return Array.from(this.notifications.values())}subscribe(e){return this.listeners.add(e),()=>this.listeners.delete(e)}success(e,t){return this.show({...t,type:"success",message:e})}error(e,t){return this.show({...t,type:"error",message:e})}warning(e,t){return this.show({...t,type:"warning",message:e})}info(e,t){return this.show({...t,type:"info",message:e})}constructor(){this.notifications=new Map,this.listeners=new Set}}let m=new f;function g(){let[e,t]=(0,i.useState)([]);return{notifications:e,show:m.show.bind(m),remove:m.remove.bind(m),clear:m.clear.bind(m),success:m.success.bind(m),error:m.error.bind(m),warning:m.warning.bind(m),info:m.info.bind(m)}}function y(){let{notifications:e}=g();return(0,s.jsx)(s.Fragment,{children:e.map(e=>(0,s.jsx)(p,{...e},e.id))})}},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>u});var s=r(37413);r(82704);var i=r(61421),a=r.n(i),n=r(80363),o=r(83317),c=r(88639);let u={title:"ThePaperBull - Crypto Trading Platform",description:"Advanced cryptocurrency futures trading platform with real-time data and professional trading tools",generator:"v0.dev",metadataBase:new URL("https://thepaperbull.com"),icons:{icon:[{url:"/favicon.svg",type:"image/svg+xml"},{url:"/favicon-16x16.svg",sizes:"16x16",type:"image/svg+xml"},{url:"/favicon-32x32.svg",sizes:"32x32",type:"image/svg+xml"}],apple:[{url:"/apple-touch-icon.svg",sizes:"180x180",type:"image/svg+xml"}],other:[{rel:"mask-icon",url:"/favicon.svg",color:"#10b981"}]},manifest:"/site.webmanifest",themeColor:"#10b981",openGraph:{title:"ThePaperBull - Advanced Crypto Trading Platform",description:"Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.",images:[{url:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png",width:1200,height:630,alt:"ThePaperBull Trading Platform Interface"}],type:"website"},twitter:{card:"summary_large_image",title:"ThePaperBull - Advanced Crypto Trading Platform",description:"Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.",images:["https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png"],creator:"@thepaperbull"}};function l({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsxs)(c.Providers,{children:[e,(0,s.jsx)(n.Toaster,{}),(0,s.jsx)(o.NotificationContainer,{})]})})})}},70695:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,l:()=>n});var s=r(75535),i=r(80043);class a{initialize(e){this.currentUser=e,this.unsubscribeFirestore&&(this.unsubscribeFirestore(),this.unsubscribeFirestore=null),e?this.subscribeToUserNotifications(e.uid):(this.notifications=[],this.notifySubscribers())}subscribeToUserNotifications(e){let t=(0,s.rJ)(i.db,"notifications"),r=(0,s.P)(t,(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));this.unsubscribeFirestore=(0,s.aQ)(r,e=>{let t=e.docs.map(e=>({id:e.id,...e.data()})),r=this.notifications.filter(e=>e.id.startsWith("temp_"));this.notifications=[...r,...t],this.notifySubscribers()},e=>{console.error("Error listening to notifications:",e)})}subscribe(e){return this.subscribers.push(e),e(this.notifications),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.notifications))}getNotifications(){return this.notifications}getUnreadCount(){return this.notifications.filter(e=>!e.read).length}async markAsRead(e){if(this.currentUser)try{let t=(0,s.H9)(i.db,"notifications",e);await (0,s.mZ)(t,{read:!0})}catch(e){console.error("Error marking notification as read:",e)}}async markAllAsRead(){if(this.currentUser)try{let e=this.notifications.filter(e=>!e.read).map(e=>this.markAsRead(e.id));await Promise.all(e)}catch(e){console.error("Error marking all notifications as read:",e)}}async createNotification(e,t,r,a="info",n){try{let o={id:`temp_${Date.now()}`,userId:e,title:t,message:r,type:a,read:!1,createdAt:new Date,data:n||null};this.notifications.unshift(o),this.notifySubscribers();let c=(0,s.rJ)(i.db,"notifications"),u=await (0,s.gS)(c,{userId:e,title:t,message:r,type:a,read:!1,createdAt:(0,s.O5)(),data:n||null}),l=this.notifications.findIndex(e=>e.id===o.id);-1!==l&&(this.notifications[l].id=u.id)}catch(e){console.error("Error creating notification:",e)}}async createTradeNotification(e,t,r){let s="",i="";switch(t){case"order_filled":s="Order Filled",i=`Your ${r.side} order for ${r.symbol} has been filled at $${r.price}`;break;case"position_opened":s="Position Opened",i=`New ${r.side} position opened for ${r.symbol} - Size: ${r.size}`;break;case"position_closed":s="Position Closed",i=`${r.symbol} position closed - P&L: ${r.pnl>0?"+":""}$${r.pnl.toFixed(2)}`;break;case"stop_loss":s="Stop Loss Triggered",i=`Stop loss triggered for ${r.symbol} at $${r.price}`;break;case"take_profit":s="Take Profit Hit",i=`Take profit reached for ${r.symbol} at $${r.price}`}await this.createNotification(e,s,i,"trade",r)}async createWelcomeNotification(e){await this.createNotification(e,"Welcome to ThePaperBull!","Start your paper trading journey with $10,000 virtual balance. Practice trading without risk!","success")}destroy(){this.unsubscribeFirestore&&this.unsubscribeFirestore(),this.subscribers=[],this.notifications=[]}constructor(){this.subscribers=[],this.unsubscribeFirestore=null,this.currentUser=null,this.notifications=[]}}let n=new a,o=n},73033:(e,t,r)=>{Promise.resolve().then(r.bind(r,85969)),Promise.resolve().then(r.bind(r,55183)),Promise.resolve().then(r.bind(r,94593))},78335:()=>{},80043:(e,t,r)=>{"use strict";r.d(t,{Ye:()=>l,db:()=>u,j2:()=>c});var s=r(67989),i=r(50227),a=r(75535),n=r(90555);r(10178);let o=0===(0,s.Dk)().length?(0,s.Wp)({apiKey:"AIzaSyBYTl5SiY2ARvKUCiMBxf8zNUBQRu3hg1s",authDomain:"thepaperbull-144.firebaseapp.com",databaseURL:"https://thepaperbull-144-default-rtdb.firebaseio.com/",projectId:"thepaperbull-144",storageBucket:"thepaperbull-144.firebasestorage.app",messagingSenderId:"540770032311",appId:"1:540770032311:web:54b0d4ec1715779408cb32",measurementId:"G-5KTY505WKQ"}):(0,s.Dk)()[0],c=(0,i.xI)(o),u=(0,a.aU)(o),l=(0,n.C3)(o)},80363:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\sonner.tsx","Toaster")},82704:()=>{},82761:(e,t,r)=>{Promise.resolve().then(r.bind(r,88639)),Promise.resolve().then(r.bind(r,83317)),Promise.resolve().then(r.bind(r,80363))},83317:(e,t,r)=>{"use strict";r.d(t,{NotificationContainer:()=>i});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx","Notification"),(0,s.registerClientReference)(function(){throw Error("Attempted to call notificationManager() from the server but notificationManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx","notificationManager"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx","useNotifications");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call NotificationContainer() from the server but NotificationContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx","NotificationContainer")},83427:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var s=r(75535),i=r(80043);class a{async createUserProfile(e,t,r){let a=(0,s.O5)(),n={email:t,name:r,avatar:"bull-trader",createdAt:a,subscription:{type:"free",status:"active",startDate:a,features:["basic_trading","paper_trading","basic_charts"]},balance:{current:1e4,maximum:5e4,currency:"USDT",lastUpdated:a},settings:{theme:"system",notifications:{email:!0,push:!0,trading_alerts:!0,price_alerts:!0,news_updates:!1},trading:{default_leverage:10,risk_management:!0,auto_close_positions:!1,confirmation_dialogs:!0}}};return await (0,s.BN)((0,s.H9)(i.db,"users",e),n),await this.addTransaction(e,{type:"deposit",amount:1e4,balance_before:0,balance_after:1e4,description:"Initial balance"}),{id:e,...n}}async getUserProfile(e){try{let t=await (0,s.x7)((0,s.H9)(i.db,"users",e));if(t.exists())return{id:e,...t.data()};return null}catch(e){return console.error("Error getting user profile:",e),null}}async updateUserProfile(e,t){try{let r={...t};t.balance&&(r["balance.lastUpdated"]=(0,s.O5)()),await (0,s.mZ)((0,s.H9)(i.db,"users",e),r)}catch(e){throw console.error("Error updating user profile:",e),e}}async updateUserBalance(e,t){try{await (0,s.mZ)((0,s.H9)(i.db,"users",e),{"balance.current":t,"balance.lastUpdated":(0,s.O5)()})}catch(e){throw console.error("Error updating user balance:",e),e}}async addTransaction(e,t){try{let r={...t,userId:e,timestamp:(0,s.O5)()};return(await (0,s.gS)((0,s.rJ)(i.db,"transactions"),r)).id}catch(e){throw console.error("Error adding transaction:",e),e}}async getUserTransactions(e,t=50){try{let r=(0,s.P)((0,s.rJ)(i.db,"transactions"),(0,s._M)("userId","==",e),(0,s.My)("timestamp","desc"));return new Promise((e,i)=>{(0,s.aQ)(r,r=>{let s=r.docs.map(e=>({id:e.id,...e.data()}));e(s.slice(0,t))},i)})}catch(e){return console.error("Error getting user transactions:",e),[]}}subscribeToUserProfile(e,t){return(0,s.aQ)((0,s.H9)(i.db,"users",e),r=>{r.exists()?t({id:e,...r.data()}):t(null)})}}let n=new a},85969:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>n});var s=r(60687),i=r(6955),a=r(25878);function n({children:e}){return(0,s.jsx)(i.O,{children:(0,s.jsx)(a.N,{children:e})})}},88639:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\providers.tsx","Providers")},94593:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});var s=r(60687),i=r(10218),a=r(52581);let n=({...e})=>{let{theme:t="system"}=(0,i.D)();return(0,s.jsx)(a.l$,{theme:t,className:"toaster group",closeButton:!0,duration:4e3,position:"top-right",expand:!0,richColors:!0,toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg group-[.toaster]:rounded-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",closeButton:"group-[.toast]:bg-background group-[.toast]:text-foreground group-[.toast]:border-border group-[.toast]:hover:bg-muted group-[.toast]:focus:ring-2 group-[.toast]:focus:ring-ring group-[.toast]:focus:ring-offset-2"}},...e})}},94895:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},95216:(e,t,r)=>{"use strict";r.d(t,{G6:()=>c,Ru:()=>o,bk:()=>n,signOutUser:()=>u});var s=r(50227),i=r(80043);let a=new s.HF;a.setCustomParameters({prompt:"select_account"});let n=async(e,t,r)=>{try{let a=await (0,s.eJ)(i.j2,e,t);return r&&a.user&&await (0,s.r7)(a.user,{displayName:r}),a.user}catch(e){throw console.error("Sign up error:",e),Error(l(e.code))}},o=async(e,t)=>{try{return(await (0,s.x9)(i.j2,e,t)).user}catch(e){throw console.error("Sign in error:",e),Error(l(e.code))}},c=async()=>{try{return(await (0,s.df)(i.j2,a)).user}catch(e){if(console.error("Google sign in error:",e),"auth/popup-closed-by-user"===e.code||"auth/cancelled-popup-request"===e.code)throw e;throw Error(l(e.code))}},u=async()=>{try{await (0,s.CI)(i.j2)}catch(e){throw console.error("Sign out error:",e),Error("Failed to sign out")}},l=e=>{switch(e){case"auth/user-not-found":return"No account found with this email address. Please check your email or sign up for a new account.";case"auth/wrong-password":return"Incorrect password. Please try again or reset your password.";case"auth/invalid-credential":return"Invalid email or password. Please check your credentials and try again.";case"auth/email-already-in-use":return"An account with this email already exists. Please sign in instead.";case"auth/weak-password":return"Password should be at least 6 characters long.";case"auth/invalid-email":return"Please enter a valid email address.";case"auth/user-disabled":return"This account has been disabled. Please contact support.";case"auth/too-many-requests":return"Too many failed attempts. Please try again later or reset your password.";case"auth/popup-closed-by-user":return"Sign-in popup was closed. Please try again.";case"auth/popup-blocked":return"Sign-in popup was blocked. Please allow popups and try again.";case"auth/network-request-failed":return"Network error. Please check your internet connection and try again.";case"auth/cancelled-popup-request":return"Sign-in was cancelled.";case"auth/account-exists-with-different-credential":return"An account already exists with the same email address but different sign-in credentials.";case"auth/operation-not-allowed":return"This sign-in method is not enabled. Please contact support.";case"auth/invalid-api-key":return"Invalid API key. Please check your Firebase configuration.";case"auth/app-deleted":return"Firebase app has been deleted. Please check your configuration.";case"auth/invalid-user-token":return"User token is invalid. Please sign in again.";case"auth/user-token-expired":return"User token has expired. Please sign in again.";default:return`Authentication error: ${e||"Unknown error"}`}}},96241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(49384),i=r(82348);function a(...e){return(0,i.QP)((0,s.$)(e))}},96487:()=>{},99766:(e,t,r)=>{"use strict";function s(){return null}r.r(t),r.d(t,{default:()=>s})}};