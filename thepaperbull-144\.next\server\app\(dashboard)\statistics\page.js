(()=>{var e={};e.id=24,e.ids=[24],e.modules={22:(e,t,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),c=r(45058);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):c(e)}},658:(e,t,r)=>{e.exports=r(41547)(r(85718),"Map")},1566:(e,t,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),c=r(17830),l=r(29395),s=r(12290),u="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=s(n),m=s(o),v=s(i),b=s(a),g=s(c),x=l;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=u||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=d)&&(x=function(e){var t=l(e),r="[object Object]"==t?e.constructor:void 0,n=r?s(r):"";if(n)switch(n){case y:return h;case m:return u;case v:return f;case b:return p;case g:return d}return t}),e.exports=x},1707:(e,t,r)=>{var n=r(35142),o=r(46436);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},1944:e=>{e.exports=function(){return!1}},2408:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}},2896:(e,t,r)=>{var n=r(81488),o=r(59467);e.exports=function(e,t){return null!=e&&o(e,t,n)}},2984:(e,t,r)=>{var n=r(49227);e.exports=function(e,t,r){for(var o=-1,i=e.length;++o<i;){var a=e[o],c=t(a);if(null!=c&&(void 0===l?c==c&&!n(c):r(c,l)))var l=c,s=a}return s}},3105:e=>{e.exports=function(e){return e.split("")}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>u,routeModule:()=>p,tree:()=>s});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),c=r(30893),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);r.d(t,l);let s={children:["",{children:["(dashboard)",{children:["statistics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,25035)),"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\statistics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,75870))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"D:\\step-by-step\\thepaperbull-144\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,99766)),"D:\\step-by-step\\thepaperbull-144\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,75870))).default(e)],manifest:void 0}}]}.children,u=["D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\statistics\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(dashboard)/statistics/page",pathname:"/statistics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},4999:(e,t,r)=>{e.exports=r(85718).Uint8Array},5231:(e,t,r)=>{var n=r(29395),o=r(55048);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},5359:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},5566:(e,t,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);e.exports=function(e){return function(t){var r=o(t=a(t))?i(t):void 0,c=r?r[0]:t.charAt(0),l=r?n(r,1).join(""):t.slice(1);return c[e]()+l}}},6053:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},6330:e=>{e.exports=function(){return[]}},7383:(e,t,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);e.exports=function(e,t,r){if(!a(r))return!1;var c=typeof t;return("number"==c?!!(o(r)&&i(t,r.length)):"string"==c&&t in r)&&n(r[t],e)}},7651:(e,t,r)=>{var n=r(82038),o=r(52931),i=r(32269);e.exports=function(e){return i(e)?n(e):o(e)}},8336:(e,t,r)=>{var n=r(45803);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},8852:(e,t,r)=>{var n=r(1707);e.exports=function(e){return function(t){return n(t,e)}}},10034:(e,t,r)=>{var n=r(2984),o=r(22),i=r(46063);e.exports=function(e,t){return e&&e.length?n(e,o(t,2),i):void 0}},10090:(e,t,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;e.exports=a?o(a):n},10653:(e,t,r)=>{var n=r(21456),o=r(63979),i=r(7651);e.exports=function(e){return n(e,i,o)}},10663:e=>{e.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],c]:e._events[l].push(c):(e._events[l]=c,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},c.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},c.prototype.emit=function(e,t,n,o,i,a){var c=r?r+e:e;if(!this._events[c])return!1;var l,s,u=this._events[c],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,i),!0;case 6:return u.fn.call(u.context,t,n,o,i,a),!0}for(s=1,l=Array(f-1);s<f;s++)l[s-1]=arguments[s];u.fn.apply(u.context,l)}else{var p,d=u.length;for(s=0;s<d;s++)switch(u[s].once&&this.removeListener(e,u[s].fn,void 0,!0),f){case 1:u[s].fn.call(u[s].context);break;case 2:u[s].fn.call(u[s].context,t);break;case 3:u[s].fn.call(u[s].context,t,n);break;case 4:u[s].fn.call(u[s].context,t,n,o);break;default:if(!l)for(p=1,l=Array(f-1);p<f;p++)l[p-1]=arguments[p];u[s].fn.apply(u[s].context,l)}}return!0},c.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},c.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},c.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==t||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var l=0,s=[],u=c.length;l<u;l++)(c[l].fn!==t||o&&!c[l].once||n&&c[l].context!==n)&&s.push(c[l]);s.length?this._events[i]=1===s.length?s[0]:s:a(this,i)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,e.exports=c},11424:(e,t,r)=>{var n=r(47603);e.exports=r(66400)(n)},11539:(e,t,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,s=/^0o[0-7]+$/i,u=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return a;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=l.test(e);return r||s.test(e)?u(e.slice(2),r?2:8):c.test(e)?a:+e}},12290:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},12344:(e,t,r)=>{e.exports=r(65984)()},14675:e=>{e.exports=function(e){return function(){return e}}},14985:e=>{"use strict";e.exports=require("dns")},15451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},15871:(e,t,r)=>{var n=r(36341),o=r(27467);e.exports=function e(t,r,i,a,c){return t===r||(null!=t&&null!=r&&(o(t)||o(r))?n(t,r,i,a,e,c):t!=t&&r!=r)}},15883:(e,t,r)=>{var n=r(2984),o=r(46063),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},15909:(e,t,r)=>{var n=r(87506),o=r(66930),i=r(658);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:e=>{e.exports=function(e){return this.__data__.has(e)}},17518:(e,t,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),c=r(43378),l=r(89624),s=r(65727),u=r(48169),f=r(40542);e.exports=function(e,t,r){t=t.length?n(t,function(e){return f(e)?function(t){return o(t,1===e.length?e[0]:e)}:e}):[u];var p=-1;return t=n(t,l(i)),c(a(e,function(e,r,o){return{criteria:n(t,function(t){return t(e)}),index:++p,value:e}}),function(e,t){return s(e,t,r)})}},17830:(e,t,r)=>{e.exports=r(41547)(r(85718),"WeakMap")},18234:(e,t,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;e.exports=function(e,t,r){var c=null==e?0:e.length;if(!c)return -1;var l=null==r?0:i(r);return l<0&&(l=a(c+l,0)),n(e,o(t,3),l)}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},19976:(e,t,r)=>{var n=r(8336);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=+(r.size!=o),this}},20540:(e,t,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,c=Math.min;e.exports=function(e,t,r){var l,s,u,f,p,d,h=0,y=!1,m=!1,v=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var r=l,n=s;return l=s=void 0,h=t,f=e.apply(n,r)}function g(e){var r=e-d,n=e-h;return void 0===d||r>=t||r<0||m&&n>=u}function x(){var e,r,n,i=o();if(g(i))return w(i);p=setTimeout(x,(e=i-d,r=i-h,n=t-e,m?c(n,u-r):n))}function w(e){return(p=void 0,v&&l)?b(e):(l=s=void 0,f)}function O(){var e,r=o(),n=g(r);if(l=arguments,s=this,d=r,n){if(void 0===p)return h=e=d,p=setTimeout(x,t),y?b(e):f;if(m)return clearTimeout(p),p=setTimeout(x,t),b(d)}return void 0===p&&(p=setTimeout(x,t)),f}return t=i(t)||0,n(r)&&(y=!!r.leading,u=(m="maxWait"in r)?a(i(r.maxWait)||0,t):u,v="trailing"in r?!!r.trailing:v),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,l=d=s=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},20623:(e,t,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),c=r(34883),l=r(41132),s=r(46436);e.exports=function(e,t){return a(e)&&c(t)?l(s(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},21367:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},21456:(e,t,r)=>{var n=r(41693),o=r(40542);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},21592:(e,t,r)=>{var n=r(42205),o=r(61837);e.exports=function(e,t){return n(o(e,t),1)}},21630:(e,t,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,c){var l=1&r,s=n(e),u=s.length;if(u!=n(t).length&&!l)return!1;for(var f=u;f--;){var p=s[f];if(!(l?p in t:o.call(t,p)))return!1}var d=c.get(e),h=c.get(t);if(d&&h)return d==t&&h==e;var y=!0;c.set(e,t),c.set(t,e);for(var m=l;++f<u;){var v=e[p=s[f]],b=t[p];if(i)var g=l?i(b,v,p,t,e,c):i(v,b,p,e,t,c);if(!(void 0===g?v===b||a(v,b,r,i,c):g)){y=!1;break}m||(m="constructor"==p)}if(y&&!m){var x=e.constructor,w=t.constructor;x!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(e),c.delete(t),y}},21820:e=>{"use strict";e.exports=require("os")},22964:(e,t,r)=>{e.exports=r(23729)(r(18234))},23729:(e,t,r)=>{var n=r(22),o=r(32269),i=r(7651);e.exports=function(e){return function(t,r,a){var c=Object(t);if(!o(t)){var l=n(r,3);t=i(t),r=function(e){return l(c[e],e,c)}}var s=e(t,r,a);return s>-1?c[l?t[s]:s]:void 0}}},23928:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25035:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\(dashboard)\\\\statistics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\statistics\\page.tsx","default")},25118:e=>{e.exports=function(e){return this.__data__.has(e)}},27006:(e,t,r)=>{var n=r(46328),o=r(99525),i=r(58276);e.exports=function(e,t,r,a,c,l){var s=1&r,u=e.length,f=t.length;if(u!=f&&!(s&&f>u))return!1;var p=l.get(e),d=l.get(t);if(p&&d)return p==t&&d==e;var h=-1,y=!0,m=2&r?new n:void 0;for(l.set(e,t),l.set(t,e);++h<u;){var v=e[h],b=t[h];if(a)var g=s?a(b,v,h,t,e,l):a(v,b,h,e,t,l);if(void 0!==g){if(g)continue;y=!1;break}if(m){if(!o(t,function(e,t){if(!i(m,t)&&(v===e||c(v,e,r,a,l)))return m.push(t)})){y=!1;break}}else if(!(v===b||c(v,b,r,a,l))){y=!1;break}}return l.delete(e),l.delete(t),y}},27235:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("ChartPie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]])},27467:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},27669:e=>{e.exports=function(){this.__data__=[],this.size=0}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28837:(e,t,r)=>{var n=r(57797),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},28977:(e,t,r)=>{var n=r(11539),o=1/0;e.exports=function(e){return e?(e=n(e))===o||e===-o?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},29021:e=>{"use strict";e.exports=require("fs")},29205:(e,t,r)=>{var n=r(8336);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=+!!t,t}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(e,t,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},29409:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("ChartNoAxesColumn",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]])},29508:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).get(e)}},29632:(e,t,r)=>{"use strict";e.exports=r(97668)},30316:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r=!0;return n(e,function(e,n,o){return r=!!t(e,n,o)}),r}},30401:(e,t,r)=>{e.exports=r(41547)(r(85718),"Promise")},30854:(e,t,r)=>{var n=r(66930),o=r(658),i=r(95746);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},32269:(e,t,r)=>{var n=r(5231),o=r(69619);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},33873:e=>{"use strict";e.exports=require("path")},34117:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34631:e=>{"use strict";e.exports=require("tls")},34746:e=>{e.exports=function(e){return this.__data__.get(e)}},34772:(e,t,r)=>{e.exports=r(41547)(r(85718),"Set")},34821:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},34883:(e,t,r)=>{var n=r(55048);e.exports=function(e){return e==e&&!n(e)}},34990:(e,t,r)=>{e.exports=r(87321)()},35142:(e,t,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},35163:(e,t,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;e.exports=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!c.call(e,"callee")}},35697:(e,t,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),c=r(59774),l=r(2408),s=n?n.prototype:void 0,u=s?s.valueOf:void 0;e.exports=function(e,t,r,n,s,f,p){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!f(new o(e),new o(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var d=c;case"[object Set]":var h=1&n;if(d||(d=l),e.size!=t.size&&!h)break;var y=p.get(e);if(y)return y==t;n|=2,p.set(e,t);var m=a(d(e),d(t),n,s,f,p);return p.delete(e),m;case"[object Symbol]":if(u)return u.call(e)==u.call(t)}return!1}},35800:(e,t,r)=>{var n=r(57797);e.exports=function(e){return n(this.__data__,e)>-1}},36315:(e,t,r)=>{var n=r(22),o=r(92662);e.exports=function(e,t){return e&&e.length?o(e,n(t,2)):[]}},36341:(e,t,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),c=r(1566),l=r(40542),s=r(80329),u=r(10090),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,m,v){var b=l(e),g=l(t),x=b?p:c(e),w=g?p:c(t);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,S=x==w;if(S&&s(e)){if(!s(t))return!1;b=!0,O=!1}if(S&&!O)return v||(v=new n),b||u(e)?o(e,t,r,y,m,v):i(e,t,x,r,y,m,v);if(!(1&r)){var A=O&&h.call(e,"__wrapped__"),P=j&&h.call(t,"__wrapped__");if(A||P){var k=A?e.value():e,E=P?t.value():t;return v||(v=new n),m(k,E,r,y,v)}}return!!S&&(v||(v=new n),a(e,t,r,y,m,v))}},36959:e=>{e.exports=function(){}},37456:e=>{e.exports=function(e){return null==e}},37575:(e,t,r)=>{var n=r(66930);e.exports=function(){this.__data__=new n,this.size=0}},37643:(e,t,r)=>{var n=r(6053),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},38404:(e,t,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,c=Function.prototype.toString,l=a.hasOwnProperty,s=c.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=l.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==s}},38428:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},38997:(e,t,r)=>{Promise.resolve().then(r.bind(r,68145))},39672:(e,t,r)=>{var n=r(58141);e.exports=function(e,t){var r=this.__data__;return this.size+=+!this.has(e),r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},39774:e=>{e.exports=function(e){return e!=e}},40491:(e,t,r)=>{var n=r(1707);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},40542:e=>{e.exports=Array.isArray},41011:(e,t,r)=>{var n=r(41353);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},41132:e=>{e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},41157:(e,t,r)=>{var n=r(91928);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},41353:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},41547:(e,t,r)=>{var n=r(61548),o=r(90851);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},41693:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},42082:e=>{e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},42205:(e,t,r)=>{var n=r(41693),o=r(85450);e.exports=function e(t,r,i,a,c){var l=-1,s=t.length;for(i||(i=o),c||(c=[]);++l<s;){var u=t[l];r>0&&i(u)?r>1?e(u,r-1,i,a,c):n(c,u):a||(c[c.length]=u)}return c}},42403:(e,t,r)=>{var n=r(80195);e.exports=function(e){return null==e?"":n(e)}},43378:e=>{e.exports=function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}},45058:(e,t,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);e.exports=function(e){return i(e)?n(a(e)):o(e)}},45603:(e,t,r)=>{var n=r(20540),o=r(55048);e.exports=function(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:i,maxWait:t,trailing:a})}},45803:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},46063:e=>{e.exports=function(e,t){return e<t}},46229:(e,t,r)=>{var n=r(48169),o=r(66354),i=r(11424);e.exports=function(e,t){return i(o(e,t,n),e+"")}},46328:(e,t,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},46436:(e,t,r)=>{var n=r(49227),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},47212:(e,t,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),c=r(7383);e.exports=function(e,t,r){var l=a(e)?n:o;return r&&c(e,t,r)&&(t=void 0),l(e,i(t,3))}},47282:(e,t,r)=>{e=r.nmd(e);var n=r(10663),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,c=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=c},47603:(e,t,r)=>{var n=r(14675),o=r(91928),i=r(48169);e.exports=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i},48169:e=>{e.exports=function(e){return e}},48385:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",l="[\\ufe0e\\ufe0f]?",s="(?:\\u200d(?:"+[o,i,a].join("|")+")"+l+c+")*",u=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+t+"]"].join("|"))+")"+(l+c+s),"g");e.exports=function(e){return e.match(u)||[]}},48725:(e,t,r)=>{Promise.resolve().then(r.bind(r,25035))},49227:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},51449:(e,t,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;e.exports=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t})},52599:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},52823:(e,t,r)=>{var n=r(85406),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},52931:(e,t,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},54765:(e,t,r)=>{var n=r(67554),o=r(32269);e.exports=function(e,t){var r=-1,i=o(e)?Array(e.length):[];return n(e,function(e,n,o){i[++r]=t(e,n,o)}),i}},55048:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},55511:e=>{"use strict";e.exports=require("crypto")},56506:(e,t,r)=>{var n=r(32269);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,c=Object(r);(t?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},57088:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(22);e.exports=function(e,t){return e&&e.length?n(e,i(t,2),o):void 0}},57797:(e,t,r)=>{var n=r(67009);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},58141:(e,t,r)=>{e.exports=r(41547)(Object,"create")},58276:e=>{e.exports=function(e,t){return e.has(t)}},58559:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},58744:(e,t,r)=>{var n=r(57797);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},59467:(e,t,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),c=r(69619),l=r(46436);e.exports=function(e,t,r){t=n(t,e);for(var s=-1,u=t.length,f=!1;++s<u;){var p=l(t[s]);if(!(f=null!=e&&r(e,p)))break;e=e[p]}return f||++s!=u?f:!!(u=null==e?0:e.length)&&c(u)&&a(p,u)&&(i(e)||o(e))}},59774:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}},59821:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var n=r(60687);r(43210);var o=r(24224),i=r(96241);let a=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,i.cn)(a({variant:t}),e),...r})}},61320:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).has(e)}},61548:(e,t,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,l=Object.prototype,s=Function.prototype.toString,u=l.hasOwnProperty,f=RegExp("^"+s.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:c).test(a(e))}},61837:(e,t,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);e.exports=function(e,t){return(a(e)?n:i)(e,o(t,3))}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(e,t,r)=>{var n=r(29395),o=r(40542),i=r(27467);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},63979:(e,t,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;e.exports=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65662:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},65727:(e,t,r)=>{var n=r(81957);e.exports=function(e,t,r){for(var o=-1,i=e.criteria,a=t.criteria,c=i.length,l=r.length;++o<c;){var s=n(i[o],a[o]);if(s){if(o>=l)return s;return s*("desc"==r[o]?-1:1)}}return e.index-t.index}},65932:(e,t,r)=>{e.exports=r(65662)(Object.getPrototypeOf,Object)},65984:e=>{e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),c=a.length;c--;){var l=a[e?c:++o];if(!1===r(i[l],l,i))break}return t}}},66354:(e,t,r)=>{var n=r(85244),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,c=o(i.length-t,0),l=Array(c);++a<c;)l[a]=i[t+a];a=-1;for(var s=Array(t+1);++a<t;)s[a]=i[a];return s[t]=r(l),n(e,this,s)}}},66400:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},66713:(e,t,r)=>{var n=r(3105),o=r(34117),i=r(48385);e.exports=function(e){return o(e)?i(e):n(e)}},66837:(e,t,r)=>{var n=r(58141);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(e,t,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),c=r(58744);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=c,e.exports=l},67009:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},67200:(e,t,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),c=r(25118),l=r(30854);function s(e){var t=this.__data__=new n(e);this.size=t.size}s.prototype.clear=o,s.prototype.delete=i,s.prototype.get=a,s.prototype.has=c,s.prototype.set=l,e.exports=s},67367:(e,t,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),c=r(7383);e.exports=function(e,t,r){var l=a(e)?n:i;return r&&c(e,t,r)&&(t=void 0),l(e,o(t,3))}},67554:(e,t,r)=>{var n=r(99114);e.exports=r(56506)(n)},67619:(e,t,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},68145:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>vf});var n={};r.r(n),r.d(n,{scaleBand:()=>nU,scaleDiverging:()=>function e(){var t=iM(cK()(iu));return t.copy=function(){return cU(t,e())},nR.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=iL(cK()).domain([.1,1,10]);return t.copy=function(){return cU(t,e()).base(t.base())},nR.apply(t,arguments)},scaleDivergingPow:()=>cV,scaleDivergingSqrt:()=>cH,scaleDivergingSymlog:()=>function e(){var t=i$(cK());return t.copy=function(){return cU(t,e()).constant(t.constant())},nR.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,il),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,il):[0,1],iM(n)},scaleImplicit:()=>nF,scaleLinear:()=>iT,scaleLog:()=>function e(){let t=iL(im()).domain([1,10]);return t.copy=()=>iy(t,e()).base(t.base()),nI.apply(t,arguments),t},scaleOrdinal:()=>n$,scalePoint:()=>nq,scalePow:()=>iV,scaleQuantile:()=>function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=oP){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(+r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e*=1)?t:n[oE(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(oO),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},nI.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function c(e){return null!=e&&e<=e?a[oE(i,e,0,o)]:t}function l(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return c}return c.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,l()):[r,n]},c.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,l()):a.slice()},c.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},c.unknown=function(e){return arguments.length&&(t=e),c},c.thresholds=function(){return i.slice()},c.copy=function(){return e().domain([r,n]).range(a).unknown(t)},nI.apply(iM(c),arguments)},scaleRadial:()=>function e(){var t,r=iv(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(iG(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,il)).map(iG)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},nI.apply(i,arguments),iM(i)},scaleSequential:()=>function e(){var t=iM(c$()(iu));return t.copy=function(){return cU(t,e())},nR.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=iL(c$()).domain([1,10]);return t.copy=function(){return cU(t,e()).base(t.base())},nR.apply(t,arguments)},scaleSequentialPow:()=>cq,scaleSequentialQuantile:()=>function e(){var t=[],r=iu;function n(e){if(null!=e&&!isNaN(e*=1))return r((oE(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(oO),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return iY(e);if(t>=1)return iX(e);var n,o=(n-1)*t,i=Math.floor(o),a=iX((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?iZ:function(e=oO){if(e===oO)return iZ;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,l=Math.log(a),s=.5*Math.exp(2*l/3),u=.5*Math.sqrt(l*s*(a-s)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*s/a+u)),p=Math.min(o,Math.floor(r+(a-c)*s/a+u));e(t,r,f,p,i)}let a=t[r],c=n,l=o;for(iJ(t,n,r),i(t[o],a)>0&&iJ(t,n,o);c<l;){for(iJ(t,c,l),++c,--l;0>i(t[c],a);)++c;for(;i(t[l],a)>0;)--l}0===i(t[n],a)?iJ(t,n,l):iJ(t,++l,o),l<=r&&(n=l+1),r<=l&&(o=l-1)}return t})(e,i).subarray(0,i+1));return a+(iY(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},nR.apply(n,arguments)},scaleSequentialSqrt:()=>cW,scaleSequentialSymlog:()=>function e(){var t=i$(c$());return t.copy=function(){return cU(t,e()).constant(t.constant())},nR.apply(t,arguments)},scaleSqrt:()=>iH,scaleSymlog:()=>function e(){var t=i$(im());return t.copy=function(){return iy(t,e()).constant(t.constant())},nI.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[oE(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},nI.apply(i,arguments)},scaleTime:()=>cz,scaleUtc:()=>cF,tickFormat:()=>iE});var o=r(60687),i=r(43210),a=r.n(i),c=r(23928),l=r(62688);let s=(0,l.A)("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);var u=r(99891);let f=(0,l.A)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),p=(0,l.A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),d=(0,l.A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]]),h=(0,l.A)("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var y=r(64398),m=r(29409),v=r(45583),b=r(13861),g=r(12597),x=r(31158),w=r(78122),O=r(25541),j=r(12640);let S=(0,l.A)("TrendingUpDown",[["path",{d:"M14.828 14.828 21 21",key:"ar5fw7"}],["path",{d:"M21 16v5h-5",key:"1ck2sf"}],["path",{d:"m21 3-9 9-4-4-6 6",key:"1h02xo"}],["path",{d:"M21 8V3h-5",key:"1qoq8a"}]]);var A=r(20835),P=r(53411),k=r(43649);let E=(0,l.A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var M=r(27235),T=r(58559),N=r(47155);r(21643);var _=r(49384),C=r(45603),D=r.n(C),I=r(63866),R=r.n(I),B=r(77822),L=r.n(B),z=r(40491),F=r.n(z),$=r(93490),U=r.n($),q=function(e){return 0===e?0:e>0?1:-1},W=function(e){return R()(e)&&e.indexOf("%")===e.length-1},K=function(e){return U()(e)&&!L()(e)},V=function(e){return K(e)||R()(e)},H=0,G=function(e){var t=++H;return"".concat(e||"").concat(t)},X=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!K(e)&&!R()(e))return n;if(W(e)){var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return L()(r)&&(r=n),o&&r>t&&(r=t),r},Y=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},Z=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},J=function(e,t){return K(e)&&K(t)?function(r){return e+r*(t-e)}:function(){return t}};function Q(e,t,r){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):F()(e,t))===r}):null}var ee=function(e){if(!e||!e.length)return null;for(var t=e.length,r=0,n=0,o=0,i=0,a=1/0,c=-1/0,l=0,s=0,u=0;u<t;u++)l=e[u].cx||0,s=e[u].cy||0,r+=l,n+=s,o+=l*s,i+=l*l,a=Math.min(a,l),c=Math.max(c,l);var f=t*i!=r*r?(t*o-r*n)/(t*i-r*r):0;return{xmin:a,xmax:c,a:f,b:(n-f*r)/t}},et=function(e,t){return K(e)&&K(t)?e-t:R()(e)&&R()(t)?e.localeCompare(t):e instanceof Date&&t instanceof Date?e.getTime()-t.getTime():String(e).localeCompare(String(t))},er=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},en=r(37456),eo=r.n(en),ei=r(5231),ea=r.n(ei),ec=r(55048),el=r.n(ec),es=r(29632);function eu(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function ef(e){return(ef="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var ep=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],ed=["points","pathLength"],eh={svg:["viewBox","children"],polygon:ed,polyline:ed},ey=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],em=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,i.isValidElement)(e)&&(r=e.props),!el()(r))return null;var n={};return Object.keys(r).forEach(function(e){ey.includes(e)&&(n[e]=t||function(t){return r[e](r,t)})}),n},ev=function(e,t,r){if(!el()(e)||"object"!==ef(e))return null;var n=null;return Object.keys(e).forEach(function(o){var i=e[o];ey.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(e){return i(t,r,e),null})}),n},eb=["children"],eg=["children"];function ex(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function ew(e){return(ew="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var eO={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},ej=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},eS=null,eA=null,eP=function e(t){if(t===eS&&Array.isArray(eA))return eA;var r=[];return i.Children.forEach(t,function(t){eo()(t)||((0,es.isFragment)(t)?r=r.concat(e(t.props.children)):r.push(t))}),eA=r,eS=t,r};function ek(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(function(e){return ej(e)}):[ej(t)],eP(e).forEach(function(e){var t=F()(e,"type.displayName")||F()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}function eE(e,t){var r=ek(e,t);return r&&r[0]}var eM=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!!K(r)&&!(r<=0)&&!!K(n)&&!(n<=0)},eT=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],eN=function(e,t,r,n){var o,i=null!==(o=null==eh?void 0:eh[n])&&void 0!==o?o:[];return t.startsWith("data-")||!ea()(e)&&(n&&i.includes(t)||ep.includes(t))||r&&ey.includes(t)},e_=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,i.isValidElement)(e)&&(n=e.props),!el()(n))return null;var o={};return Object.keys(n).forEach(function(e){var i;eN(null===(i=n)||void 0===i?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o},eC=function e(t,r){if(t===r)return!0;var n=i.Children.count(t);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return eD(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=t[o],c=r[o];if(Array.isArray(a)||Array.isArray(c)){if(!e(a,c))return!1}else if(!eD(a,c))return!1}return!0},eD=function(e,t){if(eo()(e)&&eo()(t))return!0;if(!eo()(e)&&!eo()(t)){var r=e.props||{},n=r.children,o=ex(r,eb),i=t.props||{},a=i.children,c=ex(i,eg);if(n&&a)return eu(o,c)&&eC(n,a);if(!n&&!a)return eu(o,c)}return!1},eI=function(e,t){var r=[],n={};return eP(e).forEach(function(e,o){var i;if((i=e)&&i.type&&R()(i.type)&&eT.indexOf(i.type)>=0)r.push(e);else if(e){var a=ej(e.type),c=t[a]||{},l=c.handler,s=c.once;if(l&&(!s||!n[a])){var u=l(e,a,o);r.push(u),n[a]=!0}}}),r},eR=function(e){var t=e&&e.type;return t&&eO[t]?eO[t]:null};function eB(e){return(eB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ez(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eL(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=eB(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eB(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eF(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var e$=(0,i.forwardRef)(function(e,t){var r,n=e.aspect,o=e.initialDimension,c=void 0===o?{width:-1,height:-1}:o,l=e.width,s=void 0===l?"100%":l,u=e.height,f=void 0===u?"100%":u,p=e.minWidth,d=void 0===p?0:p,h=e.minHeight,y=e.maxHeight,m=e.children,v=e.debounce,b=void 0===v?0:v,g=e.id,x=e.className,w=e.onResize,O=e.style,j=(0,i.useRef)(null),S=(0,i.useRef)();S.current=w,(0,i.useImperativeHandle)(t,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var A=function(e){if(Array.isArray(e))return e}(r=(0,i.useState)({containerWidth:c.width,containerHeight:c.height}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(r,2)||function(e,t){if(e){if("string"==typeof e)return eF(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eF(e,t)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),P=A[0],k=A[1],E=(0,i.useCallback)(function(e,t){k(function(r){var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,o=r.height;E(n,o),null===(t=S.current)||void 0===t||t.call(S,n,o)};b>0&&(e=D()(e,b,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=j.current.getBoundingClientRect();return E(r.width,r.height),t.observe(j.current),function(){t.disconnect()}},[E,b]);var M=(0,i.useMemo)(function(){var e=P.containerWidth,t=P.containerHeight;if(e<0||t<0)return null;er(W(s)||W(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",s,f),er(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=W(s)?e:s,o=W(f)?t:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),er(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,s,f,d,h,n);var c=!Array.isArray(m)&&ej(m.type).endsWith("Chart");return a().Children.map(m,function(e){return a().isValidElement(e)?(0,i.cloneElement)(e,ez({width:r,height:o},c?{style:ez({height:"100%",width:"100%",maxHeight:o,maxWidth:r},e.props.style)}:{})):e})},[n,m,f,y,h,d,P,s]);return a().createElement("div",{id:g?"".concat(g):void 0,className:(0,_.A)("recharts-responsive-container",x),style:ez(ez({},void 0===O?{}:O),{},{width:s,height:f,minWidth:d,minHeight:h,maxHeight:y}),ref:j},M)}),eU=r(34990),eq=r.n(eU),eW=r(85938),eK=r.n(eW);function eV(e,t){if(!e)throw Error("Invariant failed")}var eH=["children","width","height","viewBox","className","style","title","desc"];function eG(){return(eG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function eX(e){var t=e.children,r=e.width,n=e.height,o=e.viewBox,i=e.className,c=e.style,l=e.title,s=e.desc,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,eH),f=o||{width:r,height:n,x:0,y:0},p=(0,_.A)("recharts-surface",i);return a().createElement("svg",eG({},e_(u,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,l),a().createElement("desc",null,s),t)}var eY=["children","className"];function eZ(){return(eZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var eJ=a().forwardRef(function(e,t){var r=e.children,n=e.className,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,eY),i=(0,_.A)("recharts-layer",n);return a().createElement("g",eZ({className:i},e_(o,!0),{ref:t}),r)});function eQ(e){return(eQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function e0(){return(e0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function e1(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function e2(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function e5(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e2(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=eQ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eQ(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e2(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function e4(e){return Array.isArray(e)&&V(e[0])&&V(e[1])?e.join(" ~ "):e}var e3=function(e){var t=e.separator,r=void 0===t?" : ":t,n=e.contentStyle,o=e.itemStyle,i=void 0===o?{}:o,c=e.labelStyle,l=e.payload,s=e.formatter,u=e.itemSorter,f=e.wrapperClassName,p=e.labelClassName,d=e.label,h=e.labelFormatter,y=e.accessibilityLayer,m=e5({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),v=e5({margin:0},void 0===c?{}:c),b=!eo()(d),g=b?d:"",x=(0,_.A)("recharts-default-tooltip",f),w=(0,_.A)("recharts-tooltip-label",p);return b&&h&&null!=l&&(g=h(d,l)),a().createElement("div",e0({className:x,style:m},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:v},a().isValidElement(g)?g:"".concat(g)),function(){if(l&&l.length){var e=(u?eK()(l,u):l).map(function(e,t){if("none"===e.type)return null;var n=e5({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i),o=e.formatter||s||e4,c=e.value,u=e.name,f=c,p=u;if(o&&null!=f&&null!=p){var d=o(c,u,e,t,l);if(Array.isArray(d)){var h=function(e){if(Array.isArray(e))return e}(d)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(d,2)||function(e,t){if(e){if("string"==typeof e)return e1(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return e1(e,t)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],p=h[1]}else f=d}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:n},V(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,V(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function e6(e){return(e6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function e8(e,t,r){var n;return(n=function(e,t){if("object"!=e6(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=e6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==e6(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var e9="recharts-tooltip-wrapper",e7={visibility:"hidden"};function te(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,o=e.offsetTopLeft,i=e.position,a=e.reverseDirection,c=e.tooltipDimension,l=e.viewBox,s=e.viewBoxDimension;if(i&&K(i[n]))return i[n];var u=r[n]-c-o,f=r[n]+o;return t[n]?a[n]?u:f:a[n]?u<l[n]?Math.max(f,l[n]):Math.max(u,l[n]):f+c>l[n]+s?Math.max(u,l[n]):Math.max(f,l[n])}function tt(e){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tr(Object(r),!0).forEach(function(t){tc(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function to(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(to=function(){return!!e})()}function ti(e){return(ti=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ta(e,t){return(ta=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tc(e,t,r){return(t=tl(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tl(e){var t=function(e,t){if("object"!=tt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tt(t)?t:t+""}var ts=function(e){var t;function r(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r);for(var e,t,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=r,n=[].concat(i),t=ti(t),tc(e=function(e,t){if(t&&("object"===tt(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,to()?Reflect.construct(t,n||[],ti(this).constructor):t.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tc(e,"handleKeyDown",function(t){if("Escape"===t.key){var r,n,o,i;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=e.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=e.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ta(e,t)}(r,e),t=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e,t,r,n,o,i,c,l,s,u,f,p,d,h,y,m,v,b,g,x=this,w=this.props,O=w.active,j=w.allowEscapeViewBox,S=w.animationDuration,A=w.animationEasing,P=w.children,k=w.coordinate,E=w.hasPayload,M=w.isAnimationActive,T=w.offset,N=w.position,C=w.reverseDirection,D=w.useTranslate3d,I=w.viewBox,R=w.wrapperStyle,B=(p=(e={allowEscapeViewBox:j,coordinate:k,offsetTopLeft:T,position:N,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:D,viewBox:I}).allowEscapeViewBox,d=e.coordinate,h=e.offsetTopLeft,y=e.position,m=e.reverseDirection,v=e.tooltipBox,b=e.useTranslate3d,g=e.viewBox,v.height>0&&v.width>0&&d?(r=(t={translateX:u=te({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:h,position:y,reverseDirection:m,tooltipDimension:v.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=te({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:h,position:y,reverseDirection:m,tooltipDimension:v.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=t.translateY,s={transform:t.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):s=e7,{cssProperties:s,cssClasses:(i=(o={translateX:u,translateY:f,coordinate:d}).coordinate,c=o.translateX,l=o.translateY,(0,_.A)(e9,e8(e8(e8(e8({},"".concat(e9,"-right"),K(c)&&i&&K(i.x)&&c>=i.x),"".concat(e9,"-left"),K(c)&&i&&K(i.x)&&c<i.x),"".concat(e9,"-bottom"),K(l)&&i&&K(i.y)&&l>=i.y),"".concat(e9,"-top"),K(l)&&i&&K(i.y)&&l<i.y)))}),L=B.cssClasses,z=B.cssProperties,F=tn(tn({transition:M&&O?"transform ".concat(S,"ms ").concat(A):void 0},z),{},{pointerEvents:"none",visibility:!this.state.dismissed&&O&&E?"visible":"hidden",position:"absolute",top:0,left:0},R);return a().createElement("div",{tabIndex:-1,className:L,style:F,ref:function(e){x.wrapperNode=e}},P)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tl(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent),tu={isSsr:!0,get:function(e){return tu[e]},set:function(e,t){if("string"==typeof e)tu[e]=t;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(t){tu[t]=e[t]})}}},tf=r(36315),tp=r.n(tf);function td(e,t,r){return!0===t?tp()(e,r):ea()(t)?tp()(e,t):e}function th(e){return(th="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ty(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ty(Object(r),!0).forEach(function(t){tx(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ty(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function tv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(tv=function(){return!!e})()}function tb(e){return(tb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tg(e,t){return(tg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tx(e,t,r){return(t=tw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tw(e){var t=function(e,t){if("object"!=th(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=th(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==th(t)?t:t+""}function tO(e){return e.dataKey}var tj=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=tb(e),function(e,t){if(t&&("object"===th(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,tv()?Reflect.construct(e,t||[],tb(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tg(e,t)}(r,e),t=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,c=r.animationEasing,l=r.content,s=r.coordinate,u=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,m=r.reverseDirection,v=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=d?d:[];u&&x.length&&(x=td(d.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,tO));var w=x.length>0;return a().createElement(ts,{allowEscapeViewBox:o,animationDuration:i,animationEasing:c,isAnimationActive:f,active:n,coordinate:s,hasPayload:w,offset:p,position:y,reverseDirection:m,useTranslate3d:v,viewBox:b,wrapperStyle:g},(e=tm(tm({},this.props),{},{payload:x}),a().isValidElement(l)?a().cloneElement(l,e):"function"==typeof l?a().createElement(l,e):a().createElement(e3,e)))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tw(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);tx(tj,"displayName","Tooltip"),tx(tj,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!tu.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var tS=r(69433),tA=r.n(tS);let tP=Math.cos,tk=Math.sin,tE=Math.sqrt,tM=Math.PI,tT=2*tM,tN={draw(e,t){let r=tE(t/tM);e.moveTo(r,0),e.arc(0,0,r,0,tT)}},t_=tE(1/3),tC=2*t_,tD=tk(tM/10)/tk(7*tM/10),tI=tk(tT/10)*tD,tR=-tP(tT/10)*tD,tB=tE(3),tL=tE(3)/2,tz=1/tE(12),tF=(tz/2+1)*3;function t$(e){return function(){return e}}let tU=Math.PI,tq=2*tU,tW=tq-1e-6;function tK(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class tV{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?tK:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return tK;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,n,o){if(e*=1,t*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-e,l=n-t,s=i-e,u=a-t,f=s*s+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6){if(Math.abs(u*c-l*s)>1e-6&&o){let p=r-i,d=n-a,h=c*c+l*l,y=Math.sqrt(h),m=Math.sqrt(f),v=o*Math.tan((tU-Math.acos((h+f-(p*p+d*d))/(2*y*m)))/2),b=v/m,g=v/y;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*u}`,this._append`A${o},${o},0,0,${+(u*p>s*d)},${this._x1=e+g*c},${this._y1=t+g*l}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,n,o,i){if(e*=1,t*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),l=e+a,s=t+c,u=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${l},${s}`:(Math.abs(this._x1-l)>1e-6||Math.abs(this._y1-s)>1e-6)&&this._append`L${l},${s}`,r&&(f<0&&(f=f%tq+tq),f>tW?this._append`A${r},${r},0,1,${u},${e-a},${t-c}A${r},${r},0,1,${u},${this._x1=l},${this._y1=s}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=tU)},${u},${this._x1=e+r*Math.cos(o)},${this._y1=t+r*Math.sin(o)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function tH(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new tV(t)}function tG(e){return(tG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}tV.prototype,tE(3),tE(3);var tX=["type","size","sizeType"];function tY(){return(tY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function tZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tJ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tZ(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=tG(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tG(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var tQ={symbolCircle:tN,symbolCross:{draw(e,t){let r=tE(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=tE(t/tC),n=r*t_;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=tE(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=tE(.8908130915292852*t),n=tI*r,o=tR*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=tT*t/5,a=tP(i),c=tk(i);e.lineTo(c*r,-a*r),e.lineTo(a*n-c*o,c*n+a*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-tE(t/(3*tB));e.moveTo(0,2*r),e.lineTo(-tB*r,-r),e.lineTo(tB*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=tE(t/tF),n=r/2,o=r*tz,i=r*tz+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-tL*o,tL*n+-.5*o),e.lineTo(-.5*n-tL*i,tL*n+-.5*i),e.lineTo(-.5*a-tL*i,tL*a+-.5*i),e.lineTo(-.5*n+tL*o,-.5*o-tL*n),e.lineTo(-.5*n+tL*i,-.5*i-tL*n),e.lineTo(-.5*a+tL*i,-.5*i-tL*a),e.closePath()}}},t0=Math.PI/180,t1=function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*t0;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},t2=function(e){var t,r=e.type,n=void 0===r?"circle":r,o=e.size,i=void 0===o?64:o,c=e.sizeType,l=void 0===c?"area":c,s=tJ(tJ({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,tX)),{},{type:n,size:i,sizeType:l}),u=s.className,f=s.cx,p=s.cy,d=e_(s,!0);return f===+f&&p===+p&&i===+i?a().createElement("path",tY({},d,{className:(0,_.A)("recharts-symbols",u),transform:"translate(".concat(f,", ").concat(p,")"),d:(t=tQ["symbol".concat(tA()(n))]||tN,(function(e,t){let r=null,n=tH(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:t$(e||tN),t="function"==typeof t?t:t$(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:t$(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:t$(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(t1(i,l,n))())})):null};function t5(e){return(t5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function t4(){return(t4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function t3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}t2.registerSymbol=function(e,t){tQ["symbol".concat(tA()(e))]=t};function t6(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(t6=function(){return!!e})()}function t8(e){return(t8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function t9(e,t){return(t9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function t7(e,t,r){return(t=re(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function re(e){var t=function(e,t){if("object"!=t5(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=t5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==t5(t)?t:t+""}var rt=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=t8(e),function(e,t){if(t&&("object"===t5(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,t6()?Reflect.construct(e,t||[],t8(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&t9(e,t)}(r,e),t=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=32/6,n=32/3,o=e.inactive?t:e.color;if("plainline"===e.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(e.legendIcon)){var i=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?t3(Object(r),!0).forEach(function(t){t7(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete i.legendIcon,a().cloneElement(e.legendIcon,i)}return a().createElement(t2,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,n=t.iconSize,o=t.layout,i=t.formatter,c=t.inactiveColor,l={x:0,y:0,width:32,height:32},s={display:"horizontal"===o?"inline-block":"block",marginRight:10},u={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(t,r){var o=t.formatter||i,f=(0,_.A)(t7(t7({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var p=ea()(t.value)?null:t.value;er(!ea()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=t.inactive?c:t.color;return a().createElement("li",t4({className:f,style:s,key:"legend-item-".concat(r)},ev(e.props,t,r)),a().createElement(eX,{width:n,height:n,viewBox:l,style:u},e.renderIcon(t)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(p,t,r):p))})}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,n=e.align;return t&&t.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,re(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);function rr(e){return(rr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t7(rt,"displayName","Legend"),t7(rt,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var rn=["ref"];function ro(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ri(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ro(Object(r),!0).forEach(function(t){ru(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ro(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ra(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,rf(n.key),n)}}function rc(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(rc=function(){return!!e})()}function rl(e){return(rl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function rs(e,t){return(rs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ru(e,t,r){return(t=rf(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rf(e){var t=function(e,t){if("object"!=rr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=rr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==rr(t)?t:t+""}function rp(e){return e.value}var rd=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=rl(t),ru(e=function(e,t){if(t&&("object"===rr(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,rc()?Reflect.construct(t,r||[],rl(this).constructor):t.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&rs(e,t)}(n,e),t=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?ri({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,l=n.chartWidth,s=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===i&&"vertical"===o?{left:((l||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===a?{top:((s||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),ri(ri({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,n=t.width,o=t.height,i=t.wrapperStyle,c=t.payloadUniqBy,l=t.payload,s=ri(ri({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(t){e.wrapperNode=t}},function(e,t){if(a().isValidElement(e))return a().cloneElement(e,t);if("function"==typeof e)return a().createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,rn);return a().createElement(rt,r)}(r,ri(ri({},this.props),{},{payload:td(l,c,rp)})))}}],r=[{key:"getWithHeight",value:function(e,t){var r=ri(ri({},this.defaultProps),e.props).layout;return"vertical"===r&&K(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],t&&ra(n.prototype,t),r&&ra(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function rh(){return(rh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}ru(rd,"displayName","Legend"),ru(rd,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ry=function(e){var t=e.cx,r=e.cy,n=e.r,o=e.className,i=(0,_.A)("recharts-dot",o);return t===+t&&r===+r&&n===+n?a().createElement("circle",rh({},e_(e,!1),em(e),{className:i,cx:t,cy:r,r:n})):null},rm=r(87955),rv=r.n(rm),rb=Object.getOwnPropertyNames,rg=Object.getOwnPropertySymbols,rx=Object.prototype.hasOwnProperty;function rw(e,t){return function(r,n,o){return e(r,n,o)&&t(r,n,o)}}function rO(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var o=n.cache,i=o.get(t),a=o.get(r);if(i&&a)return i===r&&a===t;o.set(t,r),o.set(r,t);var c=e(t,r,n);return o.delete(t),o.delete(r),c}}function rj(e){return rb(e).concat(rg(e))}var rS=Object.hasOwn||function(e,t){return rx.call(e,t)};function rA(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var rP=Object.getOwnPropertyDescriptor,rk=Object.keys;function rE(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function rM(e,t){return rA(e.getTime(),t.getTime())}function rT(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function rN(e,t){return e===t}function r_(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),c=e.entries(),l=0;(n=c.next())&&!n.done;){for(var s=t.entries(),u=!1,f=0;(o=s.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],l,f,e,t,r)&&r.equals(p[1],d[1],p[0],d[0],e,t,r)){u=a[f]=!0;break}f++}if(!u)return!1;l++}return!0}function rC(e,t,r){var n=rk(e),o=n.length;if(rk(t).length!==o)return!1;for(;o-- >0;)if(!rF(e,t,r,n[o]))return!1;return!0}function rD(e,t,r){var n,o,i,a=rj(e),c=a.length;if(rj(t).length!==c)return!1;for(;c-- >0;)if(!rF(e,t,r,n=a[c])||(o=rP(e,n),i=rP(t,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rI(e,t){return rA(e.valueOf(),t.valueOf())}function rR(e,t){return e.source===t.source&&e.flags===t.flags}function rB(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),c=e.values();(n=c.next())&&!n.done;){for(var l=t.values(),s=!1,u=0;(o=l.next())&&!o.done;){if(!a[u]&&r.equals(n.value,o.value,n.value,o.value,e,t,r)){s=a[u]=!0;break}u++}if(!s)return!1}return!0}function rL(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function rz(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function rF(e,t,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!e.$$typeof||!!t.$$typeof)||rS(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var r$=Array.isArray,rU="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rq=Object.assign,rW=Object.prototype.toString.call.bind(Object.prototype.toString),rK=rV();function rV(e){void 0===e&&(e={});var t,r,n,o,i,a,c,l,s,u,f,p,d,h=e.circular,y=e.createInternalComparator,m=e.createState,v=e.strict,b=(r=(t=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,o={areArraysEqual:n?rD:rE,areDatesEqual:rM,areErrorsEqual:rT,areFunctionsEqual:rN,areMapsEqual:n?rw(r_,rD):r_,areNumbersEqual:rA,areObjectsEqual:n?rD:rC,arePrimitiveWrappersEqual:rI,areRegExpsEqual:rR,areSetsEqual:n?rw(rB,rD):rB,areTypedArraysEqual:n?rD:rL,areUrlsEqual:rz};if(r&&(o=rq({},o,r(o))),t){var i=rO(o.areArraysEqual),a=rO(o.areMapsEqual),c=rO(o.areObjectsEqual),l=rO(o.areSetsEqual);o=rq({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:l})}return o}(e)).areArraysEqual,n=t.areDatesEqual,o=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,c=t.areNumbersEqual,l=t.areObjectsEqual,s=t.arePrimitiveWrappersEqual,u=t.areRegExpsEqual,f=t.areSetsEqual,p=t.areTypedArraysEqual,d=t.areUrlsEqual,function(e,t,h){if(e===t)return!0;if(null==e||null==t)return!1;var y=typeof e;if(y!==typeof t)return!1;if("object"!==y)return"number"===y?c(e,t,h):"function"===y&&i(e,t,h);var m=e.constructor;if(m!==t.constructor)return!1;if(m===Object)return l(e,t,h);if(r$(e))return r(e,t,h);if(null!=rU&&rU(e))return p(e,t,h);if(m===Date)return n(e,t,h);if(m===RegExp)return u(e,t,h);if(m===Map)return a(e,t,h);if(m===Set)return f(e,t,h);var v=rW(e);return"[object Date]"===v?n(e,t,h):"[object RegExp]"===v?u(e,t,h):"[object Map]"===v?a(e,t,h):"[object Set]"===v?f(e,t,h):"[object Object]"===v?"function"!=typeof e.then&&"function"!=typeof t.then&&l(e,t,h):"[object URL]"===v?d(e,t,h):"[object Error]"===v?o(e,t,h):"[object Arguments]"===v?l(e,t,h):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&s(e,t,h)}),g=y?y(b):function(e,t,r,n,o,i,a){return b(e,t,a)};return function(e){var t=e.circular,r=e.comparator,n=e.createState,o=e.equals,i=e.strict;if(n)return function(e,a){var c=n(),l=c.cache;return r(e,a,{cache:void 0===l?t?new WeakMap:void 0:l,equals:o,meta:c.meta,strict:i})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(e,t){return r(e,t,a)}}({circular:void 0!==h&&h,comparator:b,createState:m,equals:g,strict:void 0!==v&&v})}function rH(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>t)e(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rG(e){return(rG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rX(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function rY(e){return(rY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rJ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rZ(Object(r),!0).forEach(function(t){rQ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rQ(e,t,r){var n;return(n=function(e,t){if("object"!==rY(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==rY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===rY(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}rV({strict:!0}),rV({circular:!0}),rV({circular:!0,strict:!0}),rV({createInternalComparator:function(){return rA}}),rV({strict:!0,createInternalComparator:function(){return rA}}),rV({circular:!0,createInternalComparator:function(){return rA}}),rV({circular:!0,createInternalComparator:function(){return rA},strict:!0});var r0=function(e){return e},r1=function(e,t){return Object.keys(t).reduce(function(r,n){return rJ(rJ({},r),{},rQ({},n,e(n,t[n])))},{})},r2=function(e,t,r){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(r)}).join(",")},r5=function(e,t,r,n,o,i,a,c){};function r4(e,t){if(e){if("string"==typeof e)return r3(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r3(e,t)}}function r3(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var r6=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},r8=function(e,t){return e.map(function(e,r){return e*Math.pow(t,r)}).reduce(function(e,t){return e+t})},r9=function(e,t){return function(r){return r8(r6(e,t),r)}},r7=function(){for(var e,t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],l=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,l=1;break;case"ease":i=.25,a=.1,c=.25,l=1;break;case"ease-in":i=.42,a=0,c=1,l=1;break;case"ease-out":i=.42,a=0,c=.58,l=1;break;case"ease-in-out":i=0,a=0,c=.58,l=1;break;default:var s=n[0].split("(");if("cubic-bezier"===s[0]&&4===s[1].split(")")[0].split(",").length){var u,f=function(e){if(Array.isArray(e))return e}(u=s[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(u,4)||r4(u,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],l=f[3]}else r5(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}r5([i,c,a,l].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=r9(i,c),d=r9(a,l),h=(e=i,t=c,function(r){var n;return r8([].concat(function(e){if(Array.isArray(e))return r3(e)}(n=r6(e,t).map(function(e,t){return e*t}).slice(1))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||r4(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(e){for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o,i=p(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},ne=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,o=void 0===n?8:n,i=e.dt,a=void 0===i?17:i,c=function(e,t,n){var i=n+(-(e-t)*r-n*o)*a/1e3,c=n*a/1e3+e;return 1e-4>Math.abs(c-t)&&1e-4>Math.abs(i)?[t,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},nt=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return r7(n);case"spring":return ne();default:if("cubic-bezier"===n.split("(")[0])return r7(n);r5(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof n?n:(r5(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function nr(e){return(nr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nn(e){return function(e){if(Array.isArray(e))return nl(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||nc(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function no(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ni(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?no(Object(r),!0).forEach(function(t){na(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):no(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function na(e,t,r){var n;return(n=function(e,t){if("object"!==nr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==nr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===nr(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nc(e,t){if(e){if("string"==typeof e)return nl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nl(e,t)}}function nl(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ns=function(e,t,r){return e+(t-e)*r},nu=function(e){return e.from!==e.to},nf=function e(t,r,n){var o=r1(function(e,r){if(nu(r)){var n,o=function(e){if(Array.isArray(e))return e}(n=t(r.from,r.to,r.velocity))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(n,2)||nc(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return ni(ni({},r),{},{from:i,velocity:a})}return r},r);return n<1?r1(function(e,t){return nu(t)?ni(ni({},t),{},{velocity:ns(t.velocity,o[e].velocity,n),from:ns(t.from,o[e].from,n)}):t},r):e(t,o,n-1)};let np=function(e,t,r,n,o){var i,a,c=[Object.keys(e),Object.keys(t)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})}),l=c.reduce(function(r,n){return ni(ni({},r),{},na({},n,[e[n],t[n]]))},{}),s=c.reduce(function(r,n){return ni(ni({},r),{},na({},n,{from:e[n],velocity:0,to:t[n]}))},{}),u=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;s=nf(r,s,a),o(ni(ni(ni({},e),t),r1(function(e,t){return t.from},s))),i=n,Object.values(s).filter(nu).length&&(u=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,s=r1(function(e,t){return ns.apply(void 0,nn(t).concat([r(c)]))},l);if(o(ni(ni(ni({},e),t),s)),c<1)u=requestAnimationFrame(f);else{var p=r1(function(e,t){return ns.apply(void 0,nn(t).concat([r(1)]))},l);o(ni(ni(ni({},e),t),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(u)}}};function nd(e){return(nd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var nh=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function ny(e){return function(e){if(Array.isArray(e))return nm(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return nm(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nm(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nm(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nv(Object(r),!0).forEach(function(t){ng(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nv(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ng(e,t,r){return(t=nx(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nx(e){var t=function(e,t){if("object"!==nd(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==nd(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===nd(t)?t:String(t)}function nw(e,t){return(nw=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function nO(e,t){if(t&&("object"===nd(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return nj(e)}function nj(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function nS(e){return(nS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var nA=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nw(e,t)}(o,e);var t,r,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=nS(o);return e=t?Reflect.construct(r,arguments,nS(this).constructor):r.apply(this,arguments),nO(this,e)});function o(e,t){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,o);var r=n.call(this,e,t),i=r.props,a=i.isActive,c=i.attributeName,l=i.from,s=i.to,u=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nj(r)),r.changeStyle=r.changeStyle.bind(nj(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:s}),nO(r);if(u&&u.length)r.state={style:u[0].style};else if(l){if("function"==typeof f)return r.state={style:l},nO(r);r.state={style:c?ng({},c,l):l}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,o=t.attributeName,i=t.shouldReAnimate,a=t.to,c=t.from,l=this.state.style;if(n){if(!r){var s={style:o?ng({},o,a):a};this.state&&l&&(o&&l[o]!==a||!o&&l!==a)&&this.setState(s);return}if(!rK(e.to,a)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=u||i?c:e.to;if(this.state&&l){var p={style:o?ng({},o,f):f};(o&&l[o]!==f||!o&&l!==f)&&this.setState(p)}this.runAnimation(nb(nb({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,r=e.from,n=e.to,o=e.duration,i=e.easing,a=e.begin,c=e.onAnimationEnd,l=e.onAnimationStart,s=np(r,n,nt(i),o,this.changeStyle);this.manager.start([l,a,function(){t.stopJSAnimation=s()},o,c])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,o=e.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(ny(r.reduce(function(e,n,o){if(0===o)return e;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,l=n.style,s=n.properties,u=n.onAnimationEnd,f=o>0?r[o-1]:n,p=s||Object.keys(l);if("function"==typeof c||"spring"===c)return[].concat(ny(e),[t.runJSAnimation.bind(t,{from:f.style,to:l,duration:i,easing:c}),i]);var d=r2(p,i,c),h=nb(nb(nb({},f.style),l),{},{transition:d});return[].concat(ny(e),[h,i,u]).filter(r0)},[a,Math.max(void 0===c?0:c,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){if(!this.manager){var t,r,n,o;this.manager=(r=function(){return null},n=!1,o=function e(t){if(!n){if(Array.isArray(t)){if(!t.length)return;var o=function(e){if(Array.isArray(e))return e}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return rX(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rX(e,t)}}(t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){rH(e.bind(null,a),i);return}e(i),rH(e.bind(null,a));return}"object"===rG(t)&&r(t),"function"==typeof t&&t()}},{stop:function(){n=!0},start:function(e){n=!1,o(e)},subscribe:function(e){return r=e,function(){r=function(){return null}}}})}var i=e.begin,a=e.duration,c=e.attributeName,l=e.to,s=e.easing,u=e.onAnimationStart,f=e.onAnimationEnd,p=e.steps,d=e.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof s||"function"==typeof d||"spring"===s){this.runJSAnimation(e);return}if(p.length>1){this.runStepAnimation(e);return}var y=c?ng({},c,l):l,m=r2(Object.keys(y),a,s);h.start([u,i,nb(nb({},y),{},{transition:m}),a,f])}},{key:"render",value:function(){var e=this.props,t=e.children,r=(e.begin,e.duration),n=(e.attributeName,e.easing,e.isActive),o=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,nh)),c=i.Children.count(t),l=this.state.style;if("function"==typeof t)return t(l);if(!n||0===c||r<=0)return t;var s=function(e){var t=e.props,r=t.style,n=t.className;return(0,i.cloneElement)(e,nb(nb({},o),{},{style:nb(nb({},void 0===r?{}:r),l),className:n}))};return 1===c?s(i.Children.only(t)):a().createElement("div",null,i.Children.map(t,function(e){return s(e)}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nx(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(i.PureComponent);function nP(e){return(nP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nk(){return(nk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function nE(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nM(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=nP(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nP(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}nA.displayName="Animate",nA.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nA.propTypes={from:rv().oneOfType([rv().object,rv().string]),to:rv().oneOfType([rv().object,rv().string]),attributeName:rv().string,duration:rv().number,begin:rv().number,easing:rv().oneOfType([rv().string,rv().func]),steps:rv().arrayOf(rv().shape({duration:rv().number.isRequired,style:rv().object.isRequired,easing:rv().oneOfType([rv().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),rv().func]),properties:rv().arrayOf("string"),onAnimationEnd:rv().func})),children:rv().oneOfType([rv().node,rv().func]),isActive:rv().bool,canBegin:rv().bool,onAnimationEnd:rv().func,shouldReAnimate:rv().bool,onAnimationStart:rv().func,onAnimationReStart:rv().func};var nN=function(e,t,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,l=r>=0?1:-1,s=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=o[f]>a?a:o[f];i="M".concat(e,",").concat(t+c*u[0]),u[0]>0&&(i+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(s,",").concat(e+l*u[0],",").concat(t)),i+="L ".concat(e+r-l*u[1],",").concat(t),u[1]>0&&(i+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+c*u[1])),i+="L ".concat(e+r,",").concat(t+n-c*u[2]),u[2]>0&&(i+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(s,",\n        ").concat(e+r-l*u[2],",").concat(t+n)),i+="L ".concat(e+l*u[3],",").concat(t+n),u[3]>0&&(i+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-c*u[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(e,",").concat(t+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(e+l*p,",").concat(t,"\n            L ").concat(e+r-l*p,",").concat(t,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(e+r,",").concat(t+c*p,"\n            L ").concat(e+r,",").concat(t+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(e+r-l*p,",").concat(t+n,"\n            L ").concat(e+l*p,",").concat(t+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(e,",").concat(t+n-c*p," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},n_=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,o=t.x,i=t.y,a=t.width,c=t.height;if(Math.abs(a)>0&&Math.abs(c)>0){var l=Math.min(o,o+a),s=Math.max(o,o+a),u=Math.min(i,i+c),f=Math.max(i,i+c);return r>=l&&r<=s&&n>=u&&n<=f}return!1},nC={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nD=function(e){var t,r=nT(nT({},nC),e),n=(0,i.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,i.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,2)||function(e,t){if(e){if("string"==typeof e)return nE(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nE(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],l=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&l(e)}catch(e){}},[]);var s=r.x,u=r.y,f=r.width,p=r.height,d=r.radius,h=r.className,y=r.animationEasing,m=r.animationDuration,v=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(s!==+s||u!==+u||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,_.A)("recharts-rectangle",h);return g?a().createElement(nA,{canBegin:c>0,from:{width:f,height:p,x:s,y:u},to:{width:f,height:p,x:s,y:u},duration:m,animationEasing:y,isActive:g},function(e){var t=e.width,o=e.height,i=e.x,l=e.y;return a().createElement(nA,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:m,isActive:b,easing:y},a().createElement("path",nk({},e_(r,!0),{className:x,d:nN(i,l,t,o,d),ref:n})))}):a().createElement("path",nk({},e_(r,!0),{className:x,d:nN(s,u,f,p,d)}))};function nI(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function nR(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class nB extends Map{constructor(e,t=nz){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(nL(this,e))}has(e){return super.has(nL(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function nL({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function nz(e){return null!==e&&"object"==typeof e?e.valueOf():e}let nF=Symbol("implicit");function n$(){var e=new nB,t=[],r=[],n=nF;function o(o){let i=e.get(o);if(void 0===i){if(n!==nF)return n;e.set(o,i=t.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new nB,r))e.has(n)||e.set(n,t.push(n)-1);return o},o.range=function(e){return arguments.length?(r=Array.from(e),o):r.slice()},o.unknown=function(e){return arguments.length?(n=e,o):n},o.copy=function(){return n$(t,r).unknown(n)},nI.apply(o,arguments),o}function nU(){var e,t,r=n$().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,l=0,s=0,u=.5;function f(){var r=n().length,f=a<i,p=f?a:i,d=f?i:a;e=(d-p)/Math.max(1,r-l+2*s),c&&(e=Math.floor(e)),p+=(d-p-e*(r-l))*u,t=e*(1-l),c&&(p=Math.round(p),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return p+e*t});return o(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([i,a]=e,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(e){return[i,a]=e,i*=1,a*=1,c=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(c=!!e,f()):c},r.padding=function(e){return arguments.length?(l=Math.min(1,s=+e),f()):l},r.paddingInner=function(e){return arguments.length?(l=Math.min(1,e),f()):l},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return nU(n(),[i,a]).round(c).paddingInner(l).paddingOuter(s).align(u)},nI.apply(f(),arguments)}function nq(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(nU.apply(null,arguments).paddingInner(1))}function nW(e){return(nW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nV(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nK(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=nW(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nW(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nH(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var nG={widthCache:{},cacheCount:0},nX={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nY="recharts_measurement_span",nZ=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||tu.isSsr)return{width:0,height:0};var n=(Object.keys(t=nV({},r)).forEach(function(e){t[e]||delete t[e]}),t),o=JSON.stringify({text:e,copyStyle:n});if(nG.widthCache[o])return nG.widthCache[o];try{var i=document.getElementById(nY);i||((i=document.createElement("span")).setAttribute("id",nY),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nV(nV({},nX),n);Object.assign(i.style,a),i.textContent="".concat(e);var c=i.getBoundingClientRect(),l={width:c.width,height:c.height};return nG.widthCache[o]=l,++nG.cacheCount>2e3&&(nG.cacheCount=0,nG.widthCache={}),l}catch(e){return{width:0,height:0}}};function nJ(e){return(nJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nQ(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return n0(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n0(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n0(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function n1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,function(e){var t=function(e,t){if("object"!=nJ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nJ(t)?t:t+""}(n.key),n)}}var n2=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,n5=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,n4=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,n3=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,n6={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},n8=Object.keys(n6),n9=function(){var e,t;function r(e,t){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||n4.test(t)||(this.num=NaN,this.unit=""),n8.includes(t)&&(this.num=e*n6[t],this.unit="px")}return e=[{key:"add",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],t=[{key:"parse",value:function(e){var t,n=nQ(null!==(t=n3.exec(e))&&void 0!==t?t:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],e&&n1(r.prototype,e),t&&n1(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function n7(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,n=nQ(null!==(r=n2.exec(t))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=n9.parse(null!=o?o:""),l=n9.parse(null!=a?a:""),s="*"===i?c.multiply(l):c.divide(l);if(s.isNaN())return"NaN";t=t.replace(n2,s.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,f=nQ(null!==(u=n5.exec(t))&&void 0!==u?u:[],4),p=f[1],d=f[2],h=f[3],y=n9.parse(null!=p?p:""),m=n9.parse(null!=h?h:""),v="+"===d?y.add(m):y.subtract(m);if(v.isNaN())return"NaN";t=t.replace(n5,v.toString())}return t}var oe=/\(([^()]*)\)/;function ot(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t=e;t.includes("(");){var r=nQ(oe.exec(t),2)[1];t=t.replace(oe,n7(r))}return t}(t),t=n7(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var or=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],on=["dx","dy","angle","className","breakAll"];function oo(){return(oo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function oi(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function oa(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return oc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oc(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ol=/[ \f\n\r\t\v\u2028\u2029]+/,os=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var o=[];eo()(t)||(o=r?t.toString().split(""):t.toString().split(ol));var i=o.map(function(e){return{word:e,width:nZ(e,n).width}}),a=r?0:nZ("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(e){return null}},ou=function(e,t,r,n,o){var i,a=e.maxLines,c=e.children,l=e.style,s=e.breakAll,u=K(a),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var i=t.word,a=t.width,c=e[e.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):e.push({words:[i],width:a}),e},[])},p=f(t);if(!u)return p;for(var d=function(e){var t=f(os({breakAll:s,style:l,children:c.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>a||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(n),t]},h=0,y=c.length-1,m=0;h<=y&&m<=c.length-1;){var v=Math.floor((h+y)/2),b=oa(d(v-1),2),g=b[0],x=b[1],w=oa(d(v),1)[0];if(g||w||(h=v+1),g&&w&&(y=v-1),!g&&w){i=x;break}m++}return i||p},of=function(e){return[{words:eo()(e)?[]:e.toString().split(ol)}]},op=function(e){var t=e.width,r=e.scaleToFit,n=e.children,o=e.style,i=e.breakAll,a=e.maxLines;if((t||r)&&!tu.isSsr){var c=os({breakAll:i,children:n,style:o});if(!c)return of(n);var l=c.wordsWithComputedWidth,s=c.spaceWidth;return ou({breakAll:i,children:n,maxLines:a,style:o},l,s,t,r)}return of(n)},od="#808080",oh=function(e){var t,r=e.x,n=void 0===r?0:r,o=e.y,c=void 0===o?0:o,l=e.lineHeight,s=void 0===l?"1em":l,u=e.capHeight,f=void 0===u?"0.71em":u,p=e.scaleToFit,d=void 0!==p&&p,h=e.textAnchor,y=e.verticalAnchor,m=e.fill,v=void 0===m?od:m,b=oi(e,or),g=(0,i.useMemo)(function(){return op({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:d,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,d,b.style,b.width]),x=b.dx,w=b.dy,O=b.angle,j=b.className,S=b.breakAll,A=oi(b,on);if(!V(n)||!V(c))return null;var P=n+(K(x)?x:0),k=c+(K(w)?w:0);switch(void 0===y?"end":y){case"start":t=ot("calc(".concat(f,")"));break;case"middle":t=ot("calc(".concat((g.length-1)/2," * -").concat(s," + (").concat(f," / 2))"));break;default:t=ot("calc(".concat(g.length-1," * -").concat(s,")"))}var E=[];if(d){var M=g[0].width,T=b.width;E.push("scale(".concat((K(T)?T/M:1)/M,")"))}return O&&E.push("rotate(".concat(O,", ").concat(P,", ").concat(k,")")),E.length&&(A.transform=E.join(" ")),a().createElement("text",oo({},e_(A,!0),{x:P,y:k,className:(0,_.A)("recharts-text",j),textAnchor:void 0===h?"start":h,fill:v.includes("url")?od:v}),g.map(function(e,r){var n=e.words.join(S?"":" ");return a().createElement("tspan",{x:P,dy:0===r?t:s,key:"".concat(n,"-").concat(r)},n)}))};let oy=Math.sqrt(50),om=Math.sqrt(10),ov=Math.sqrt(2);function ob(e,t,r){let n,o,i;let a=(t-e)/Math.max(0,r),c=Math.floor(Math.log10(a)),l=a/Math.pow(10,c),s=l>=oy?10:l>=om?5:l>=ov?2:1;return(c<0?(n=Math.round(e*(i=Math.pow(10,-c)/s)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,c)*s)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?ob(e,t,2*r):[n,o,i]}function og(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?ob(t,e,r):ob(e,t,r);if(!(i>=o))return[];let c=i-o+1,l=Array(c);if(n){if(a<0)for(let e=0;e<c;++e)l[e]=-((i-e)/a);else for(let e=0;e<c;++e)l[e]=(i-e)*a}else if(a<0)for(let e=0;e<c;++e)l[e]=-((o+e)/a);else for(let e=0;e<c;++e)l[e]=(o+e)*a;return l}function ox(e,t,r){return ob(e*=1,t*=1,r*=1)[2]}function ow(e,t,r){t*=1,e*=1,r*=1;let n=t<e,o=n?ox(t,e,r):ox(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function oO(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function oj(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function oS(e){let t,r,n;function o(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=oO,r=(t,r)=>oO(e(t),r),n=(t,r)=>e(t)-r):(t=e===oO||e===oj?e:oA,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function oA(){return 0}function oP(e){return null===e?NaN:+e}let ok=oS(oO),oE=ok.right;function oM(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function oT(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function oN(){}ok.left,oS(oP).center;var o_="\\s*([+-]?\\d+)\\s*",oC="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",oD="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",oI=/^#([0-9a-f]{3,8})$/,oR=RegExp(`^rgb\\(${o_},${o_},${o_}\\)$`),oB=RegExp(`^rgb\\(${oD},${oD},${oD}\\)$`),oL=RegExp(`^rgba\\(${o_},${o_},${o_},${oC}\\)$`),oz=RegExp(`^rgba\\(${oD},${oD},${oD},${oC}\\)$`),oF=RegExp(`^hsl\\(${oC},${oD},${oD}\\)$`),o$=RegExp(`^hsla\\(${oC},${oD},${oD},${oC}\\)$`),oU={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function oq(){return this.rgb().formatHex()}function oW(){return this.rgb().formatRgb()}function oK(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=oI.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?oV(t):3===r?new oX(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?oH(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?oH(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=oR.exec(e))?new oX(t[1],t[2],t[3],1):(t=oB.exec(e))?new oX(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=oL.exec(e))?oH(t[1],t[2],t[3],t[4]):(t=oz.exec(e))?oH(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=oF.exec(e))?o1(t[1],t[2]/100,t[3]/100,1):(t=o$.exec(e))?o1(t[1],t[2]/100,t[3]/100,t[4]):oU.hasOwnProperty(e)?oV(oU[e]):"transparent"===e?new oX(NaN,NaN,NaN,0):null}function oV(e){return new oX(e>>16&255,e>>8&255,255&e,1)}function oH(e,t,r,n){return n<=0&&(e=t=r=NaN),new oX(e,t,r,n)}function oG(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof oN||(o=oK(o)),o)?new oX((o=o.rgb()).r,o.g,o.b,o.opacity):new oX:new oX(e,t,r,null==n?1:n)}function oX(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function oY(){return`#${o0(this.r)}${o0(this.g)}${o0(this.b)}`}function oZ(){let e=oJ(this.opacity);return`${1===e?"rgb(":"rgba("}${oQ(this.r)}, ${oQ(this.g)}, ${oQ(this.b)}${1===e?")":`, ${e})`}`}function oJ(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function oQ(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function o0(e){return((e=oQ(e))<16?"0":"")+e.toString(16)}function o1(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new o5(e,t,r,n)}function o2(e){if(e instanceof o5)return new o5(e.h,e.s,e.l,e.opacity);if(e instanceof oN||(e=oK(e)),!e)return new o5;if(e instanceof o5)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,c=i-o,l=(i+o)/2;return c?(a=t===i?(r-n)/c+(r<n)*6:r===i?(n-t)/c+2:(t-r)/c+4,c/=l<.5?i+o:2-i-o,a*=60):c=l>0&&l<1?0:a,new o5(a,c,l,e.opacity)}function o5(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function o4(e){return(e=(e||0)%360)<0?e+360:e}function o3(e){return Math.max(0,Math.min(1,e||0))}function o6(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function o8(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}oM(oN,oK,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:oq,formatHex:oq,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return o2(this).formatHsl()},formatRgb:oW,toString:oW}),oM(oX,oG,oT(oN,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new oX(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new oX(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new oX(oQ(this.r),oQ(this.g),oQ(this.b),oJ(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:oY,formatHex:oY,formatHex8:function(){return`#${o0(this.r)}${o0(this.g)}${o0(this.b)}${o0((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oZ,toString:oZ})),oM(o5,function(e,t,r,n){return 1==arguments.length?o2(e):new o5(e,t,r,null==n?1:n)},oT(oN,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new o5(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new o5(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new oX(o6(e>=240?e-240:e+120,o,n),o6(e,o,n),o6(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new o5(o4(this.h),o3(this.s),o3(this.l),oJ(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=oJ(this.opacity);return`${1===e?"hsl(":"hsla("}${o4(this.h)}, ${100*o3(this.s)}%, ${100*o3(this.l)}%${1===e?")":`, ${e})`}`}}));let o9=e=>()=>e;function o7(e,t){var r,n,o=t-e;return o?(r=e,n=o,function(e){return r+e*n}):o9(isNaN(e)?t:e)}let ie=function e(t){var r,n=1==(r=+t)?o7:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):o9(isNaN(e)?t:e)};function o(e,t){var r=n((e=oG(e)).r,(t=oG(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=o7(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function it(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oG(t[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=e(i),a=e(a),c=e(c),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=c(e),n+""}}}it(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,c=n<t-1?e[n+2]:2*i-o;return o8((r-n/t)*t,a,o,i,c)}}),it(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],c=e[(n+2)%t];return o8((r-n/t)*t,o,i,a,c)}});function ir(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var io=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ii=RegExp(io.source,"g");function ia(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?o9(t):("number"===o?ir:"string"===o?(n=oK(t))?(t=n,ie):function(e,t){var r,n,o,i,a,c=io.lastIndex=ii.lastIndex=0,l=-1,s=[],u=[];for(e+="",t+="";(o=io.exec(e))&&(i=ii.exec(t));)(a=i.index)>c&&(a=t.slice(c,a),s[l]?s[l]+=a:s[++l]=a),(o=o[0])===(i=i[0])?s[l]?s[l]+=i:s[++l]=i:(s[++l]=null,u.push({i:l,x:ir(o,i)})),c=ii.lastIndex;return c<t.length&&(a=t.slice(c),s[l]?s[l]+=a:s[++l]=a),s.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)s[(r=u[n]).i]=r.x(e);return s.join("")})}:t instanceof oK?ie:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=ia(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ia(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:ir:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function ic(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function il(e){return+e}var is=[0,1];function iu(e){return e}function ip(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function id(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=ip(o,n),i=r(a,i)):(n=ip(n,o),i=r(i,a)),function(e){return i(n(e))}}function ih(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=ip(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=oE(e,t,1,n)-1;return i[r](o[r](t))}}function iy(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function im(){var e,t,r,n,o,i,a=is,c=is,l=ia,s=iu;function u(){var e,t,r,l=Math.min(a.length,c.length);return s!==iu&&(e=a[0],t=a[l-1],e>t&&(r=e,e=t,t=r),s=function(r){return Math.max(e,Math.min(t,r))}),n=l>2?ih:id,o=i=null,f}function f(t){return null==t||isNaN(t*=1)?r:(o||(o=n(a.map(e),c,l)))(e(s(t)))}return f.invert=function(r){return s(t((i||(i=n(c,a.map(e),ir)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,il),u()):a.slice()},f.range=function(e){return arguments.length?(c=Array.from(e),u()):c.slice()},f.rangeRound=function(e){return c=Array.from(e),l=ic,u()},f.clamp=function(e){return arguments.length?(s=!!e||iu,u()):s!==iu},f.interpolate=function(e){return arguments.length?(l=e,u()):l},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function iv(){return im()(iu,iu)}var ib=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ig(e){var t;if(!(t=ib.exec(e)))throw Error("invalid format: "+e);return new ix({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ix(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function iw(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function iO(e){return(e=iw(Math.abs(e)))?e[1]:NaN}function ij(e,t){var r=iw(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}ig.prototype=ix.prototype,ix.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let iS={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>ij(100*e,t),r:ij,s:function(e,t){var r=iw(e,t);if(!r)return e+"";var n=r[0],o=r[1],i=o-(cQ=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+iw(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function iA(e){return e}var iP=Array.prototype.map,ik=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function iE(e,t,r,n){var o,i,a,c=ow(e,t,r);switch((n=ig(null==n?",f":n)).type){case"s":var l=Math.max(Math.abs(e),Math.abs(t));return null==n.precision&&!isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(iO(l)/3)))-iO(Math.abs(c))))&&(n.precision=a),c2(n,l);case"":case"e":case"g":case"p":case"r":null==n.precision&&!isNaN(a=Math.max(0,iO(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=c)))-iO(o))+1)&&(n.precision=a-("e"===n.type));break;case"f":case"%":null==n.precision&&!isNaN(a=Math.max(0,-iO(Math.abs(c))))&&(n.precision=a-("%"===n.type)*2)}return c1(n)}function iM(e){var t=e.domain;return e.ticks=function(e){var r=t();return og(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return iE(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,c=i.length-1,l=i[a],s=i[c],u=10;for(s<l&&(o=l,l=s,s=o,o=a,a=c,c=o);u-- >0;){if((o=ox(l,s,r))===n)return i[a]=l,i[c]=s,t(i);if(o>0)l=Math.floor(l/o)*o,s=Math.ceil(s/o)*o;else if(o<0)l=Math.ceil(l*o)/o,s=Math.floor(s*o)/o;else break;n=o}return e},e}function iT(){var e=iv();return e.copy=function(){return iy(e,iT())},nI.apply(e,arguments),iM(e)}function iN(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function i_(e){return Math.log(e)}function iC(e){return Math.exp(e)}function iD(e){return-Math.log(-e)}function iI(e){return-Math.exp(-e)}function iR(e){return isFinite(e)?+("1e"+e):e<0?0:e}function iB(e){return(t,r)=>-e(-t,r)}function iL(e){let t,r;let n=e(i_,iC),o=n.domain,i=10;function a(){var a,c;return t=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(c=i)?iR:c===Math.E?Math.exp:e=>Math.pow(c,e),o()[0]<0?(t=iB(t),r=iB(r),e(iD,iI)):e(i_,iC),n}return n.base=function(e){return arguments.length?(i=+e,a()):i},n.domain=function(e){return arguments.length?(o(e),a()):o()},n.ticks=e=>{let n,a;let c=o(),l=c[0],s=c[c.length-1],u=s<l;u&&([l,s]=[s,l]);let f=t(l),p=t(s),d=null==e?10:+e,h=[];if(!(i%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),l>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<l)){if(a>s)break;h.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<l)){if(a>s)break;h.push(a)}2*h.length<d&&(h=og(l,s,d))}else h=og(f,p,Math.min(p-f,d)).map(r);return u?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=ig(o)).precision||(o.trim=!0),o=c1(o)),e===1/0)return o;let a=Math.max(1,i*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*i<i-.5&&(n*=i),n<=a?o(e):""}},n.nice=()=>o(iN(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function iz(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function iF(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function i$(e){var t=1,r=e(iz(1),iF(t));return r.constant=function(r){return arguments.length?e(iz(t=+r),iF(t)):t},iM(r)}function iU(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function iq(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function iW(e){return e<0?-e*e:e*e}function iK(e){var t=e(iu,iu),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(iu,iu):.5===r?e(iq,iW):e(iU(r),iU(1/r)):r},iM(t)}function iV(){var e=iK(im());return e.copy=function(){return iy(e,iV()).exponent(e.exponent())},nI.apply(e,arguments),e}function iH(){return iV.apply(null,arguments).exponent(.5)}function iG(e){return Math.sign(e)*e*e}function iX(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function iY(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}c1=(c0=function(e){var t,r,n,o=void 0===e.grouping||void 0===e.thousands?iA:(t=iP.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,c=t[0],l=0;o>0&&c>0&&(l+c+1>n&&(c=Math.max(1,n-l)),i.push(e.substring(o-=c,o+c)),!((l+=c+1)>n));)c=t[a=(a+1)%t.length];return i.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",c=void 0===e.decimal?".":e.decimal+"",l=void 0===e.numerals?iA:(n=iP.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),s=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=ig(e)).fill,r=e.align,n=e.sign,p=e.symbol,d=e.zero,h=e.width,y=e.comma,m=e.precision,v=e.trim,b=e.type;"n"===b?(y=!0,b="g"):iS[b]||(void 0===m&&(m=12),v=!0,b="g"),(d||"0"===t&&"="===r)&&(d=!0,t="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?s:"",w=iS[b],O=/[defgprs%]/.test(b);function j(e){var i,a,s,p=g,j=x;if("c"===b)j=w(e)+j,e="";else{var S=(e*=1)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),m),v&&(e=function(e){e:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==n&&(S=!1),p=(S?"("===n?n:u:"-"===n||"("===n?"":n)+p,j=("s"===b?ik[8+cQ/3]:"")+j+(S&&"("===n?")":""),O){for(i=-1,a=e.length;++i<a;)if(48>(s=e.charCodeAt(i))||s>57){j=(46===s?c+e.slice(i+1):e.slice(i))+j,e=e.slice(0,i);break}}}y&&!d&&(e=o(e,1/0));var A=p.length+e.length+j.length,P=A<h?Array(h-A+1).join(t):"";switch(y&&d&&(e=o(P+e,P.length?h-j.length:1/0),P=""),r){case"<":e=p+e+j+P;break;case"=":e=p+P+e+j;break;case"^":e=P.slice(0,A=P.length>>1)+p+e+j+P.slice(A);break;default:e=P+p+e+j}return l(e)}return m=void 0===m?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),j.toString=function(){return e+""},j}return{format:p,formatPrefix:function(e,t){var r=p(((e=ig(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(iO(t)/3))),o=Math.pow(10,-n),i=ik[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,c2=c0.formatPrefix;function iZ(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function iJ(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let iQ=new Date,i0=new Date;function i1(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a;let c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return c},o.filter=r=>i1(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(o.count=(t,n)=>(iQ.setTime(+t),i0.setTime(+n),e(iQ),e(i0),Math.floor(r(iQ,i0))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let i2=i1(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);i2.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i1(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):i2:null,i2.range;let i5=i1(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());i5.range;let i4=i1(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());i4.range;let i3=i1(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());i3.range;let i6=i1(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());i6.range;let i8=i1(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());i8.range;let i9=i1(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);i9.range;let i7=i1(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);i7.range;let ae=i1(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function at(e){return i1(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}ae.range;let ar=at(0),an=at(1),ao=at(2),ai=at(3),aa=at(4),ac=at(5),al=at(6);function as(e){return i1(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}ar.range,an.range,ao.range,ai.range,aa.range,ac.range,al.range;let au=as(0),af=as(1),ap=as(2),ad=as(3),ah=as(4),ay=as(5),am=as(6);au.range,af.range,ap.range,ad.range,ah.range,ay.range,am.range;let av=i1(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());av.range;let ab=i1(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());ab.range;let ag=i1(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());ag.every=e=>isFinite(e=Math.floor(e))&&e>0?i1(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,ag.range;let ax=i1(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function aw(e,t,r,n,o,i){let a=[[i5,1,1e3],[i5,5,5e3],[i5,15,15e3],[i5,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function c(t,r,n){let o=Math.abs(r-t)/n,i=oS(([,,e])=>e).right(a,o);if(i===a.length)return e.every(ow(t/31536e6,r/31536e6,n));if(0===i)return i2.every(Math.max(ow(t,r,n),1));let[c,l]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(l)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:c(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},c]}ax.every=e=>isFinite(e=Math.floor(e))&&e>0?i1(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,ax.range;let[aO,aj]=aw(ax,ab,au,ae,i8,i3),[aS,aA]=aw(ag,av,ar,i9,i6,i4);function aP(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function ak(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function aE(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var aM={"-":"",_:" ",0:"0"},aT=/^\s*\d+/,aN=/^%/,a_=/[\\^$*+?|[\]().{}]/g;function aC(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function aD(e){return e.replace(a_,"\\$&")}function aI(e){return RegExp("^(?:"+e.map(aD).join("|")+")","i")}function aR(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function aB(e,t,r){var n=aT.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function aL(e,t,r){var n=aT.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function az(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function aF(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function a$(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function aU(e,t,r){var n=aT.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function aq(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aW(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aK(e,t,r){var n=aT.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function aV(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function aH(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function aG(e,t,r){var n=aT.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function aX(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function aY(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function aZ(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function aJ(e,t,r){var n=aT.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function aQ(e,t,r){var n=aT.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function a0(e,t,r){var n=aN.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function a1(e,t,r){var n=aT.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function a2(e,t,r){var n=aT.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function a5(e,t){return aC(e.getDate(),t,2)}function a4(e,t){return aC(e.getHours(),t,2)}function a3(e,t){return aC(e.getHours()%12||12,t,2)}function a6(e,t){return aC(1+i9.count(ag(e),e),t,3)}function a8(e,t){return aC(e.getMilliseconds(),t,3)}function a9(e,t){return a8(e,t)+"000"}function a7(e,t){return aC(e.getMonth()+1,t,2)}function ce(e,t){return aC(e.getMinutes(),t,2)}function ct(e,t){return aC(e.getSeconds(),t,2)}function cr(e){var t=e.getDay();return 0===t?7:t}function cn(e,t){return aC(ar.count(ag(e)-1,e),t,2)}function co(e){var t=e.getDay();return t>=4||0===t?aa(e):aa.ceil(e)}function ci(e,t){return e=co(e),aC(aa.count(ag(e),e)+(4===ag(e).getDay()),t,2)}function ca(e){return e.getDay()}function cc(e,t){return aC(an.count(ag(e)-1,e),t,2)}function cl(e,t){return aC(e.getFullYear()%100,t,2)}function cs(e,t){return aC((e=co(e)).getFullYear()%100,t,2)}function cu(e,t){return aC(e.getFullYear()%1e4,t,4)}function cf(e,t){var r=e.getDay();return aC((e=r>=4||0===r?aa(e):aa.ceil(e)).getFullYear()%1e4,t,4)}function cp(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+aC(t/60|0,"0",2)+aC(t%60,"0",2)}function cd(e,t){return aC(e.getUTCDate(),t,2)}function ch(e,t){return aC(e.getUTCHours(),t,2)}function cy(e,t){return aC(e.getUTCHours()%12||12,t,2)}function cm(e,t){return aC(1+i7.count(ax(e),e),t,3)}function cv(e,t){return aC(e.getUTCMilliseconds(),t,3)}function cb(e,t){return cv(e,t)+"000"}function cg(e,t){return aC(e.getUTCMonth()+1,t,2)}function cx(e,t){return aC(e.getUTCMinutes(),t,2)}function cw(e,t){return aC(e.getUTCSeconds(),t,2)}function cO(e){var t=e.getUTCDay();return 0===t?7:t}function cj(e,t){return aC(au.count(ax(e)-1,e),t,2)}function cS(e){var t=e.getUTCDay();return t>=4||0===t?ah(e):ah.ceil(e)}function cA(e,t){return e=cS(e),aC(ah.count(ax(e),e)+(4===ax(e).getUTCDay()),t,2)}function cP(e){return e.getUTCDay()}function ck(e,t){return aC(af.count(ax(e)-1,e),t,2)}function cE(e,t){return aC(e.getUTCFullYear()%100,t,2)}function cM(e,t){return aC((e=cS(e)).getUTCFullYear()%100,t,2)}function cT(e,t){return aC(e.getUTCFullYear()%1e4,t,4)}function cN(e,t){var r=e.getUTCDay();return aC((e=r>=4||0===r?ah(e):ah.ceil(e)).getUTCFullYear()%1e4,t,4)}function c_(){return"+0000"}function cC(){return"%"}function cD(e){return+e}function cI(e){return Math.floor(+e/1e3)}function cR(e){return new Date(e)}function cB(e){return e instanceof Date?+e:+new Date(+e)}function cL(e,t,r,n,o,i,a,c,l,s){var u=iv(),f=u.invert,p=u.domain,d=s(".%L"),h=s(":%S"),y=s("%I:%M"),m=s("%I %p"),v=s("%a %d"),b=s("%b %d"),g=s("%B"),x=s("%Y");function w(e){return(l(e)<e?d:c(e)<e?h:a(e)<e?y:i(e)<e?m:n(e)<e?o(e)<e?v:b:r(e)<e?g:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?p(Array.from(e,cB)):p().map(cR)},u.ticks=function(t){var r=p();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:s(t)},u.nice=function(e){var r=p();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?p(iN(r,e)):u},u.copy=function(){return iy(u,cL(e,t,r,n,o,i,a,c,l,s))},u}function cz(){return nI.apply(cL(aS,aA,ag,av,ar,i9,i6,i4,i5,c4).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cF(){return nI.apply(cL(aO,aj,ax,ab,au,i7,i8,i3,i5,c3).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function c$(){var e,t,r,n,o,i=0,a=1,c=iu,l=!1;function s(t){return null==t||isNaN(t*=1)?o:c(0===r?.5:(t=(n(t)-e)*r,l?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,c=e(r,n),s):[c(0),c(1)]}}return s.domain=function(o){return arguments.length?([i,a]=o,e=n(i*=1),t=n(a*=1),r=e===t?0:1/(t-e),s):[i,a]},s.clamp=function(e){return arguments.length?(l=!!e,s):l},s.interpolator=function(e){return arguments.length?(c=e,s):c},s.range=u(ia),s.rangeRound=u(ic),s.unknown=function(e){return arguments.length?(o=e,s):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),s}}function cU(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function cq(){var e=iK(c$());return e.copy=function(){return cU(e,cq()).exponent(e.exponent())},nR.apply(e,arguments)}function cW(){return cq.apply(null,arguments).exponent(.5)}function cK(){var e,t,r,n,o,i,a,c=0,l=.5,s=1,u=1,f=iu,p=!1;function d(e){return isNaN(e*=1)?a:(e=.5+((e=+i(e))-t)*(u*e<u*t?n:o),f(p?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,f=function(e,t){void 0===t&&(t=e,e=ia);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([c,l,s]=a,e=i(c*=1),t=i(l*=1),r=i(s*=1),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,d):[c,l,s]},d.clamp=function(e){return arguments.length?(p=!!e,d):p},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=h(ia),d.rangeRound=h(ic),d.unknown=function(e){return arguments.length?(a=e,d):a},function(a){return i=a,e=a(c),t=a(l),r=a(s),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,d}}function cV(){var e=iK(cK());return e.copy=function(){return cU(e,cV()).exponent(e.exponent())},nR.apply(e,arguments)}function cH(){return cV.apply(null,arguments).exponent(.5)}function cG(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],c=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cX(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function cY(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function cZ(e,t){return e[t]}function cJ(e){let t=[];return t.key=e,t}c4=(c5=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,c=e.months,l=e.shortMonths,s=aI(o),u=aR(o),f=aI(i),p=aR(i),d=aI(a),h=aR(a),y=aI(c),m=aR(c),v=aI(l),b=aR(l),g={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return l[e.getMonth()]},B:function(e){return c[e.getMonth()]},c:null,d:a5,e:a5,f:a9,g:cs,G:cf,H:a4,I:a3,j:a6,L:a8,m:a7,M:ce,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:cD,s:cI,S:ct,u:cr,U:cn,V:ci,w:ca,W:cc,x:null,X:null,y:cl,Y:cu,Z:cp,"%":cC},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return l[e.getUTCMonth()]},B:function(e){return c[e.getUTCMonth()]},c:null,d:cd,e:cd,f:cb,g:cM,G:cN,H:ch,I:cy,j:cm,L:cv,m:cg,M:cx,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:cD,s:cI,S:cw,u:cO,U:cj,V:cA,w:cP,W:ck,x:null,X:null,y:cE,Y:cT,Z:c_,"%":cC},w={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:aH,e:aH,f:aQ,g:aq,G:aU,H:aX,I:aX,j:aG,L:aJ,m:aV,M:aY,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:aK,Q:a1,s:a2,S:aZ,u:aL,U:az,V:aF,w:aB,W:a$,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:aq,Y:aU,Z:aW,"%":a0};function O(e,t){return function(r){var n,o,i,a=[],c=-1,l=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++c<s;)37===e.charCodeAt(c)&&(a.push(e.slice(l,c)),null!=(o=aM[n=e.charAt(++c)])?n=e.charAt(++c):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),l=c+1);return a.push(e.slice(l,c)),a.join("")}}function j(e,t){return function(r){var n,o,i=aE(1900,void 0,1);if(S(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ak(aE(i.y,0,1))).getUTCDay())>4||0===o?af.ceil(n):af(n),n=i7.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=aP(aE(i.y,0,1))).getDay())>4||0===o?an.ceil(n):an(n),n=i9.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?ak(aE(i.y,0,1)).getUTCDay():aP(aE(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ak(i)):aP(i)}}function S(e,t,r,n){for(var o,i,a=0,c=t.length,l=r.length;a<c;){if(n>=l)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=w[(o=t.charAt(a++))in aM?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(t,g),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",g);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,c5.parse,c3=c5.utcFormat,c5.utcParse,Array.prototype.slice;var cQ,c0,c1,c2,c5,c4,c3,c6,c8,c9=r(90453),c7=r.n(c9),le=r(15883),lt=r.n(le),lr=r(21592),ln=r.n(lr),lo=r(71967),li=r.n(lo),la=!0,lc="[DecimalError] ",ll=lc+"Invalid argument: ",ls=lc+"Exponent out of range: ",lu=Math.floor,lf=Math.pow,lp=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ld=lu(1286742750677284.5),lh={};function ly(e,t){var r,n,o,i,a,c,l,s,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),la?lA(t,f):t;if(l=e.d,s=t.d,a=e.e,o=t.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,c=s.length):(n=s,o=a,c=l.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/1e7|0,l[i]%=1e7;for(r&&(l.unshift(r),++o),c=l.length;0==l[--c];)l.pop();return t.d=l,t.e=o,la?lA(t,f):t}function lm(e,t,r){if(e!==~~e||e<t||e>r)throw Error(ll+e)}function lv(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=lO(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=lO(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}lh.absoluteValue=lh.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},lh.comparedTo=lh.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},lh.decimalPlaces=lh.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},lh.dividedBy=lh.div=function(e){return lb(this,new this.constructor(e))},lh.dividedToIntegerBy=lh.idiv=function(e){var t=this.constructor;return lA(lb(this,new t(e),0,1),t.precision)},lh.equals=lh.eq=function(e){return!this.cmp(e)},lh.exponent=function(){return lx(this)},lh.greaterThan=lh.gt=function(e){return this.cmp(e)>0},lh.greaterThanOrEqualTo=lh.gte=function(e){return this.cmp(e)>=0},lh.isInteger=lh.isint=function(){return this.e>this.d.length-2},lh.isNegative=lh.isneg=function(){return this.s<0},lh.isPositive=lh.ispos=function(){return this.s>0},lh.isZero=function(){return 0===this.s},lh.lessThan=lh.lt=function(e){return 0>this.cmp(e)},lh.lessThanOrEqualTo=lh.lte=function(e){return 1>this.cmp(e)},lh.logarithm=lh.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(c8))throw Error(lc+"NaN");if(this.s<1)throw Error(lc+(this.s?"NaN":"-Infinity"));return this.eq(c8)?new r(0):(la=!1,t=lb(lj(this,o),lj(e,o),o),la=!0,lA(t,n))},lh.minus=lh.sub=function(e){return e=new this.constructor(e),this.s==e.s?lP(this,e):ly(this,(e.s=-e.s,e))},lh.modulo=lh.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(lc+"NaN");return this.s?(la=!1,t=lb(this,e,0,1).times(e),la=!0,this.minus(t)):lA(new r(this),n)},lh.naturalExponential=lh.exp=function(){return lg(this)},lh.naturalLogarithm=lh.ln=function(){return lj(this)},lh.negated=lh.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},lh.plus=lh.add=function(e){return e=new this.constructor(e),this.s==e.s?ly(this,e):lP(this,(e.s=-e.s,e))},lh.precision=lh.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(ll+e);if(t=lx(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},lh.squareRoot=lh.sqrt=function(){var e,t,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(lc+"NaN")}for(e=lx(this),la=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=lv(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=lu((e+1)/2)-(e<0||e%2),n=new c(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(lb(this,i,a+2)).times(.5),lv(i.d).slice(0,a)===(t=lv(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(lA(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return la=!0,lA(n,r)},lh.times=lh.mul=function(e){var t,r,n,o,i,a,c,l,s,u=this.constructor,f=this.d,p=(e=new u(e)).d;if(!this.s||!e.s)return new u(0);for(e.s*=this.s,r=this.e+e.e,(l=f.length)<(s=p.length)&&(i=f,f=p,p=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(t=0,o=l+n;o>n;)c=i[o]+p[n]*f[o-n-1]+t,i[o--]=c%1e7|0,t=c/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,la?lA(e,u.precision):e},lh.toDecimalPlaces=lh.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(lm(e,0,1e9),void 0===t?t=n.rounding:lm(t,0,8),lA(r,e+lx(r)+1,t))},lh.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=lk(n,!0):(lm(e,0,1e9),void 0===t?t=o.rounding:lm(t,0,8),r=lk(n=lA(new o(n),e+1,t),!0,e+1)),r},lh.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?lk(this):(lm(e,0,1e9),void 0===t?t=o.rounding:lm(t,0,8),r=lk((n=lA(new o(this),e+lx(this)+1,t)).abs(),!1,e+lx(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},lh.toInteger=lh.toint=function(){var e=this.constructor;return lA(new e(this),lx(this)+1,e.rounding)},lh.toNumber=function(){return+this},lh.toPower=lh.pow=function(e){var t,r,n,o,i,a,c=this,l=c.constructor,s=+(e=new l(e));if(!e.s)return new l(c8);if(!(c=new l(c)).s){if(e.s<1)throw Error(lc+"Infinity");return c}if(c.eq(c8))return c;if(n=l.precision,e.eq(c8))return lA(c,n);if(a=(t=e.e)>=(r=e.d.length-1),i=c.s,a){if((r=s<0?-s:s)<=0x1fffffffffffff){for(o=new l(c8),t=Math.ceil(n/7+4),la=!1;r%2&&lE((o=o.times(c)).d,t),0!==(r=lu(r/2));)lE((c=c.times(c)).d,t);return la=!0,e.s<0?new l(c8).div(o):lA(o,n)}}else if(i<0)throw Error(lc+"NaN");return i=i<0&&1&e.d[Math.max(t,r)]?-1:1,c.s=1,la=!1,o=e.times(lj(c,n+12)),la=!0,(o=lg(o)).s=i,o},lh.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=lx(o),n=lk(o,r<=i.toExpNeg||r>=i.toExpPos)):(lm(e,1,1e9),void 0===t?t=i.rounding:lm(t,0,8),r=lx(o=lA(new i(o),e,t)),n=lk(o,e<=r||r<=i.toExpNeg,e)),n},lh.toSignificantDigits=lh.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(lm(e,1,1e9),void 0===t?t=r.rounding:lm(t,0,8)),lA(new r(this),e,t)},lh.toString=lh.valueOf=lh.val=lh.toJSON=lh[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=lx(this),t=this.constructor;return lk(this,e<=t.toExpNeg||e>=t.toExpPos)};var lb=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var c,l,s,u,f,p,d,h,y,m,v,b,g,x,w,O,j,S,A=n.constructor,P=n.s==o.s?1:-1,k=n.d,E=o.d;if(!n.s)return new A(n);if(!o.s)throw Error(lc+"Division by zero");for(s=0,l=n.e-o.e,j=E.length,w=k.length,h=(d=new A(P)).d=[];E[s]==(k[s]||0);)++s;if(E[s]>(k[s]||0)&&--l,(b=null==i?i=A.precision:a?i+(lx(n)-lx(o))+1:i)<0)return new A(0);if(b=b/7+2|0,s=0,1==j)for(u=0,E=E[0],b++;(s<w||u)&&b--;s++)g=1e7*u+(k[s]||0),h[s]=g/E|0,u=g%E|0;else{for((u=1e7/(E[0]+1)|0)>1&&(E=e(E,u),k=e(k,u),j=E.length,w=k.length),x=j,m=(y=k.slice(0,j)).length;m<j;)y[m++]=0;(S=E.slice()).unshift(0),O=E[0],E[1]>=1e7/2&&++O;do u=0,(c=t(E,y,j,m))<0?(v=y[0],j!=m&&(v=1e7*v+(y[1]||0)),(u=v/O|0)>1?(u>=1e7&&(u=1e7-1),p=(f=e(E,u)).length,m=y.length,1==(c=t(f,y,p,m))&&(u--,r(f,j<p?S:E,p))):(0==u&&(c=u=1),f=E.slice()),(p=f.length)<m&&f.unshift(0),r(y,f,m),-1==c&&(m=y.length,(c=t(E,y,j,m))<1&&(u++,r(y,j<m?S:E,m))),m=y.length):0===c&&(u++,y=[0]),h[s++]=u,c&&y[0]?y[m++]=k[x]||0:(y=[k[x]],m=1);while((x++<w||void 0!==y[0])&&b--)}return h[0]||h.shift(),d.e=l,lA(d,a?i+lx(d)+1:i)}}();function lg(e,t){var r,n,o,i,a,c=0,l=0,s=e.constructor,u=s.precision;if(lx(e)>16)throw Error(ls+lx(e));if(!e.s)return new s(c8);for(null==t?(la=!1,a=u):a=t,i=new s(.03125);e.abs().gte(.1);)e=e.times(i),l+=5;for(a+=Math.log(lf(2,l))/Math.LN10*2+5|0,r=n=o=new s(c8),s.precision=a;;){if(n=lA(n.times(e),a),r=r.times(++c),lv((i=o.plus(lb(n,r,a))).d).slice(0,a)===lv(o.d).slice(0,a)){for(;l--;)o=lA(o.times(o),a);return s.precision=u,null==t?(la=!0,lA(o,u)):o}o=i}}function lx(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function lw(e,t,r){if(t>e.LN10.sd())throw la=!0,r&&(e.precision=r),Error(lc+"LN10 precision limit exceeded");return lA(new e(e.LN10),t)}function lO(e){for(var t="";e--;)t+="0";return t}function lj(e,t){var r,n,o,i,a,c,l,s,u,f=1,p=e,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(lc+(p.s?"NaN":"-Infinity"));if(p.eq(c8))return new h(0);if(null==t?(la=!1,s=y):s=t,p.eq(10))return null==t&&(la=!0),lw(h,s);if(h.precision=s+=10,n=(r=lv(d)).charAt(0),!(15e14>Math.abs(i=lx(p))))return l=lw(h,s+2,y).times(i+""),p=lj(new h(n+"."+r.slice(1)),s-10).plus(l),h.precision=y,null==t?(la=!0,lA(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=lv((p=p.times(e)).d)).charAt(0),f++;for(i=lx(p),n>1?(p=new h("0."+r),i++):p=new h(n+"."+r.slice(1)),c=a=p=lb(p.minus(c8),p.plus(c8),s),u=lA(p.times(p),s),o=3;;){if(a=lA(a.times(u),s),lv((l=c.plus(lb(a,new h(o),s))).d).slice(0,s)===lv(c.d).slice(0,s))return c=c.times(2),0!==i&&(c=c.plus(lw(h,s+2,y).times(i+""))),c=lb(c,new h(f),s),h.precision=y,null==t?(la=!0,lA(c,y)):c;c=l,o+=2}}function lS(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,e.e=lu((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),la&&(e.e>ld||e.e<-ld))throw Error(ls+r)}else e.s=0,e.e=0,e.d=[0];return e}function lA(e,t,r){var n,o,i,a,c,l,s,u,f=e.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,s=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(i=f.length))return e;for(a=1,s=i=f[u];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=s/(i=lf(10,a-o-1))%10|0,l=t<0||void 0!==f[u+1]||s%i,l=r<4?(c||l)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?o>0?s/lf(10,a-o):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return l?(i=lx(e),f.length=1,t=t-i-1,f[0]=lf(10,(7-t%7)%7),e.e=lu(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,i=1,u--):(f.length=u+1,i=lf(10,7-n),f[u]=o>0?(s/lf(10,a-o)%lf(10,o)|0)*i:0),l)for(;;){if(0==u){1e7==(f[0]+=i)&&(f[0]=1,++e.e);break}if(f[u]+=i,1e7!=f[u])break;f[u--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(la&&(e.e>ld||e.e<-ld))throw Error(ls+lx(e));return e}function lP(e,t){var r,n,o,i,a,c,l,s,u,f,p=e.constructor,d=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),la?lA(t,d):t;if(l=e.d,f=t.d,n=t.e,s=e.e,l=l.slice(),a=s-n){for((u=a<0)?(r=l,a=-a,c=f.length):(r=f,n=s,c=l.length),a>(o=Math.max(Math.ceil(d/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((u=(o=l.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(l[o]!=f[o]){u=l[o]<f[o];break}a=0}for(u&&(r=l,l=f,f=r,t.s=-t.s),c=l.length,o=f.length-c;o>0;--o)l[c++]=0;for(o=f.length;o>a;){if(l[--o]<f[o]){for(i=o;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[o]+=1e7}l[o]-=f[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(t.d=l,t.e=n,la?lA(t,d):t):new p(0)}function lk(e,t,r){var n,o=lx(e),i=lv(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+lO(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+lO(-o-1)+i,r&&(n=r-a)>0&&(i+=lO(n))):o>=a?(i+=lO(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+lO(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=lO(n))),e.s<0?"-"+i:i}function lE(e,t){if(e.length>t)return e.length=t,!0}function lM(e){if(!e||"object"!=typeof e)throw Error(lc+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]])){if(lu(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(ll+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(ll+r+": "+n)}return this}var c6=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(ll+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return lS(this,e.toString())}if("string"!=typeof e)throw Error(ll+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,lp.test(e))lS(this,e);else throw Error(ll+e)}if(i.prototype=lh,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=lM,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});c8=new c6(1);let lT=c6;function lN(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var l_=function(e){return e},lC={},lD=function(e){return e===lC},lI=function(e){return function t(){return 0==arguments.length||1==arguments.length&&lD(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},lR=function(e){return function e(t,r){return 1===t?r:lI(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==lC}).length;return a>=t?r.apply(void 0,o):e(t-a,lI(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=o.map(function(e){return lD(e)?t.shift():e});return r.apply(void 0,((function(e){if(Array.isArray(e))return lN(e)})(i)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return lN(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lN(e,t)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))}))})}(e.length,e)},lB=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},lL=lR(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),lz=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return l_;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(e,t){return t(e)},o.apply(void 0,arguments))}},lF=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},l$=function(e){var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every(function(e,r){return e===t[r]})?r:(t=o,r=e.apply(void 0,o))}};lR(function(e,t,r){var n=+e;return n+r*(+t-n)}),lR(function(e,t,r){var n=t-+e;return(r-e)/(n=n||1/0)}),lR(function(e,t,r){var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});let lU={rangeStep:function(e,t,r){for(var n=new lT(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(e){var t;return 0===e?1:Math.floor(new lT(e).abs().log(10).toNumber())+1}};function lq(e){return function(e){if(Array.isArray(e))return lV(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||lK(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lW(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(e,t)||lK(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lK(e,t){if(e){if("string"==typeof e)return lV(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lV(e,t)}}function lV(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function lH(e){var t=lW(e,2),r=t[0],n=t[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function lG(e,t,r){if(e.lte(0))return new lT(0);var n=lU.getDigitCount(e.toNumber()),o=new lT(10).pow(n),i=e.div(o),a=1!==n?.05:.1,c=new lT(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return t?c:new lT(Math.ceil(c))}function lX(e,t,r){var n=1,o=new lT(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new lT(10).pow(lU.getDigitCount(e)-1),o=new lT(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new lT(Math.floor(e)))}else 0===e?o=new lT(Math.floor((t-1)/2)):r||(o=new lT(Math.floor(e)));var a=Math.floor((t-1)/2);return lz(lL(function(e){return o.add(new lT(e-a).mul(n)).toNumber()}),lB)(0,t)}var lY=l$(function(e){var t=lW(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=lW(lH([r,n]),2),l=c[0],s=c[1];if(l===-1/0||s===1/0){var u=s===1/0?[l].concat(lq(lB(0,o-1).map(function(){return 1/0}))):[].concat(lq(lB(0,o-1).map(function(){return-1/0})),[s]);return r>n?lF(u):u}if(l===s)return lX(l,o,i);var f=function e(t,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new lT(0),tickMin:new lT(0),tickMax:new lT(0)};var c=lG(new lT(r).sub(t).div(n-1),o,a),l=Math.ceil((i=t<=0&&r>=0?new lT(0):(i=new lT(t).add(r).div(2)).sub(new lT(i).mod(c))).sub(t).div(c).toNumber()),s=Math.ceil(new lT(r).sub(i).div(c).toNumber()),u=l+s+1;return u>n?e(t,r,n,o,a+1):(u<n&&(s=r>0?s+(n-u):s,l=r>0?l:l+(n-u)),{step:c,tickMin:i.sub(new lT(l).mul(c)),tickMax:i.add(new lT(s).mul(c))})}(l,s,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=lU.rangeStep(d,h.add(new lT(.1).mul(p)),p);return r>n?lF(y):y});l$(function(e){var t=lW(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=lW(lH([r,n]),2),l=c[0],s=c[1];if(l===-1/0||s===1/0)return[r,n];if(l===s)return lX(l,o,i);var u=lG(new lT(s).sub(l).div(a-1),i,0),f=lz(lL(function(e){return new lT(l).add(new lT(e).mul(u)).toNumber()}),lB)(0,a).filter(function(e){return e>=l&&e<=s});return r>n?lF(f):f});var lZ=l$(function(e,t){var r=lW(e,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=lW(lH([n,o]),2),c=a[0],l=a[1];if(c===-1/0||l===1/0)return[n,o];if(c===l)return[c];var s=Math.max(t,2),u=lG(new lT(l).sub(c).div(s-1),i,0),f=[].concat(lq(lU.rangeStep(new lT(c),new lT(l).sub(new lT(.99).mul(u)),u)),[l]);return n>o?lF(f):f}),lJ=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function lQ(e){return(lQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l0(){return(l0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function l1(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(l2=function(){return!!e})()}function l5(e){return(l5=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l4(e,t){return(l4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function l3(e,t,r){return(t=l6(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l6(e){var t=function(e,t){if("object"!=lQ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=lQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==lQ(t)?t:t+""}var l8=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=l5(e),function(e,t){if(t&&("object"===lQ(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,l2()?Reflect.construct(e,t||[],l5(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l4(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,n=e.width,o=e.dataKey,i=e.data,c=e.dataPointFormatter,l=e.xAxis,s=e.yAxis,u=e_(function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,lJ),!1);"x"===this.props.direction&&"number"!==l.type&&eV(!1);var f=i.map(function(e){var i,f,p=c(e,o),d=p.x,h=p.y,y=p.value,m=p.errorVal;if(!m)return null;var v=[];if(Array.isArray(m)){var b=function(e){if(Array.isArray(e))return e}(m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(m,2)||function(e,t){if(e){if("string"==typeof e)return l1(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l1(e,t)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=m;if("vertical"===r){var g=l.scale,x=h+t,w=x+n,O=x-n,j=g(y-i),S=g(y+f);v.push({x1:S,y1:w,x2:S,y2:O}),v.push({x1:j,y1:x,x2:S,y2:x}),v.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var A=s.scale,P=d+t,k=P-n,E=P+n,M=A(y-i),T=A(y+f);v.push({x1:k,y1:T,x2:E,y2:T}),v.push({x1:P,y1:M,x2:P,y2:T}),v.push({x1:k,y1:M,x2:E,y2:M})}return a().createElement(eJ,l0({className:"recharts-errorBar",key:"bar-".concat(v.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},u),v.map(function(e){return a().createElement("line",l0({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return a().createElement(eJ,{className:"recharts-errorBars"},f)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,l6(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function l9(e){return(l9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function se(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l7(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=l9(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=l9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l9(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}l3(l8,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),l3(l8,"displayName","ErrorBar");var st=function(e){var t,r=e.children,n=e.formattedGraphicalItems,o=e.legendWidth,i=e.legendContent,a=eE(r,rd);if(!a)return null;var c=rd.defaultProps,l=void 0!==c?se(se({},c),a.props):{};return t=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(e,t){var r=t.item,n=t.props,o=n.sectors||n.data||[];return e.concat(o.map(function(e){return{type:a.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(n||[]).map(function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?se(se({},r),t.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:l.iconType||a||"square",color:sf(t),value:i||o,payload:n}}),se(se(se({},l),rd.getWithHeight(a,o)),{},{payload:t,item:a})};function sr(e){return(sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sn(e){return function(e){if(Array.isArray(e))return so(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return so(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return so(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function so(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function si(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?si(Object(r),!0).forEach(function(t){sc(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):si(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sc(e,t,r){var n;return(n=function(e,t){if("object"!=sr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==sr(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sl(e,t,r){return eo()(e)||eo()(t)?r:V(t)?F()(e,t,r):ea()(t)?t(e):r}function ss(e,t,r,n){var o=ln()(e,function(e){return sl(e,t)});if("number"===r){var i=o.filter(function(e){return K(e)||parseFloat(e)});return i.length?[lt()(i),c7()(i)]:[1/0,-1/0]}return(n?o.filter(function(e){return!eo()(e)}):o).map(function(e){return V(e)||e instanceof Date?e:""})}var su=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(t=null==r?void 0:r.length)&&void 0!==t?t:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,l=0;l<a;l++){var s=l>0?n[l-1].coordinate:n[a-1].coordinate,u=n[l].coordinate,f=l>=a-1?n[0].coordinate:n[l+1].coordinate,p=void 0;if(q(u-s)!==q(f-u)){var d=[];if(q(f-u)===q(c[1]-c[0])){p=f;var h=u+c[1]-c[0];d[0]=Math.min(h,(h+s)/2),d[1]=Math.max(h,(h+s)/2)}else{p=s;var y=f+c[1]-c[0];d[0]=Math.min(u,(y+u)/2),d[1]=Math.max(u,(y+u)/2)}var m=[Math.min(u,(p+u)/2),Math.max(u,(p+u)/2)];if(e>m[0]&&e<=m[1]||e>=d[0]&&e<=d[1]){i=n[l].index;break}}else{var v=Math.min(s,f),b=Math.max(s,f);if(e>(v+u)/2&&e<=(b+u)/2){i=n[l].index;break}}}else for(var g=0;g<a;g++)if(0===g&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},sf=function(e){var t,r,n=e.type.displayName,o=null!==(t=e.type)&&void 0!==t&&t.defaultProps?sa(sa({},e.type.defaultProps),e.props):e.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},sp=function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,l=a.length;c<l;c++)for(var s=o[a[c]].stackGroups,u=Object.keys(s),f=0,p=u.length;f<p;f++){var d=s[u[f]],h=d.items,y=d.cateAxisId,m=h.filter(function(e){return ej(e.type).indexOf("Bar")>=0});if(m&&m.length){var v=m[0].type.defaultProps,b=void 0!==v?sa(sa({},v),m[0].props):m[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=eo()(g)?t:g;i[x].push({item:m[0],stackList:m.slice(1),barSize:eo()(w)?void 0:X(w,r,0)})}}return i},sd=function(e){var t,r=e.barGap,n=e.barCategoryGap,o=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,c=e.maxBarSize,l=a.length;if(l<1)return null;var s=X(r,o,0,!0),u=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/l,d=a.reduce(function(e,t){return e+t.barSize||0},0);(d+=(l-1)*s)>=o&&(d-=(l-1)*s,s=0),d>=o&&p>0&&(f=!0,p*=.9,d=l*p);var h={offset:((o-d)/2>>0)-s,size:0};t=a.reduce(function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+s,size:f?p:t.barSize}},n=[].concat(sn(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:h})}),n},u)}else{var y=X(n,o,0,!0);o-2*y-(l-1)*s<=0&&(s=0);var m=(o-2*y-(l-1)*s)/l;m>1&&(m>>=0);var v=c===+c?Math.min(m,c):m;t=a.reduce(function(e,t,r){var n=[].concat(sn(e),[{item:t.item,position:{offset:y+(m+s)*r+(m-v)/2,size:v}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:n[n.length-1].position})}),n},u)}return t},sh=function(e,t,r,n){var o=r.children,i=r.width,a=r.margin,c=st({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var l=n||{},s=l.width,u=l.height,f=c.align,p=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&K(e[f]))return sa(sa({},e),{},sc({},f,e[f]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&K(e[p]))return sa(sa({},e),{},sc({},p,e[p]+(u||0)))}return e},sy=function(e,t,r,n,o){var i=ek(t.props.children,l8).filter(function(e){var t;return t=e.props.direction,!!eo()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===t?"xAxis"===o:"y"!==t||"yAxis"===o)});if(i&&i.length){var a=i.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var n=sl(t,r);if(eo()(n))return e;var o=Array.isArray(n)?[lt()(n),c7()(n)]:[n,n],i=a.reduce(function(e,r){var n=sl(t,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,e[0]),Math.max(a,e[1])]},[1/0,-1/0]);return[Math.min(i[0],e[0]),Math.max(i[1],e[1])]},[1/0,-1/0])}return null},sm=function(e,t,r,n,o){var i=t.map(function(t){return sy(e,t,r,o,n)}).filter(function(e){return!eo()(e)});return i&&i.length?i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},sv=function(e,t,r,n,o){var i=t.map(function(t){var i=t.props.dataKey;return"number"===r&&i&&sy(e,t,i,n)||ss(e,i,r,o)});if("number"===r)return i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var a={};return i.reduce(function(e,t){for(var r=0,n=t.length;r<n;r++)a[t[r]]||(a[t[r]]=!0,e.push(t[r]));return e},[])},sb=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},sg=function(e,t,r,n){if(n)return e.map(function(e){return e.coordinate});var o,i,a=e.map(function(e){return e.coordinate===t&&(o=!0),e.coordinate===r&&(i=!0),e.coordinate});return o||a.push(t),i||a.push(r),a},sx=function(e,t,r){if(!e)return null;var n=e.scale,o=e.duplicateDomain,i=e.type,a=e.range,c="scaleBand"===e.realScaleType?n.bandwidth()/2:2,l=(t||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(l="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*q(a[0]-a[1])*l:l,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:n(o?o.indexOf(e):e)+l,value:e,offset:l}}).filter(function(e){return!L()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:n(e)+l,value:e,index:t,offset:l}}):n.ticks&&!r?n.ticks(e.tickCount).map(function(e){return{coordinate:n(e)+l,value:e,offset:l}}):n.domain().map(function(e,t){return{coordinate:n(e)+l,value:o?o[e]:e,index:t,offset:l}})},sw=new WeakMap,sO=function(e,t){if("function"!=typeof t)return e;sw.has(e)||sw.set(e,new WeakMap);var r=sw.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},sj=function(e,t,r){var o=e.scale,i=e.type,a=e.layout,c=e.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nU(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:iT(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:nq(),realScaleType:"point"}:"category"===i?{scale:nU(),realScaleType:"band"}:{scale:iT(),realScaleType:"linear"};if(R()(o)){var l="scale".concat(tA()(o));return{scale:(n[l]||nq)(),realScaleType:n[l]?l:"point"}}return ea()(o)?{scale:o}:{scale:nq(),realScaleType:"point"}},sS=function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),c=e(t[r-1]);(a<o||a>i||c<o||c>i)&&e.domain([t[0],t[r-1]])}},sA=function(e,t){if(!e)return null;for(var r=0,n=e.length;r<n;r++)if(e[r].item===t)return e[r].position;return null},sP=function(e,t){if(!t||2!==t.length||!K(t[0])||!K(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),o=[e[0],e[1]];return(!K(e[0])||e[0]<r)&&(o[0]=r),(!K(e[1])||e[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},sk={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var c=L()(e[a][r][1])?e[a][r][0]:e[a][r][1];c>=0?(e[a][r][0]=o,e[a][r][1]=o+c,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+c,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}cG(e,t)}},none:cG,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=e[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cG(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,l=0,s=0;c<o;++c){for(var u=e[t[c]],f=u[a][1]||0,p=(f-(u[a-1][1]||0))/2,d=0;d<c;++d){var h=e[t[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}l+=f,s+=p*f}r[a-1][1]+=r[a-1][0]=i,l&&(i-=s/l)}r[a-1][1]+=r[a-1][0]=i,cG(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=L()(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},sE=function(e,t,r){var n=t.map(function(e){return e.props.dataKey}),o=sk[r];return(function(){var e=t$([]),t=cY,r=cG,n=cZ;function o(o){var i,a,c=Array.from(e.apply(this,arguments),cJ),l=c.length,s=-1;for(let e of o)for(i=0,++s;i<l;++i)(c[i][s]=[0,+n(e,c[i].key,s,o)]).data=e;for(i=0,a=cX(t(c));i<l;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:t$(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:t$(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?cY:"function"==typeof e?e:t$(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?cG:e,o):r},o})().keys(n).value(function(e,t){return+sl(e,t,0)}).order(cY).offset(o)(e)},sM=function(e,t,r,n,o,i){if(!e)return null;var a=(i?t.reverse():t).reduce(function(e,t){var o,i=null!==(o=t.type)&&void 0!==o&&o.defaultProps?sa(sa({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(i.hide)return e;var c=i[r],l=e[c]||{hasStack:!1,stackGroups:{}};if(V(a)){var s=l.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};s.items.push(t),l.hasStack=!0,l.stackGroups[a]=s}else l.stackGroups[G("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return sa(sa({},e),{},sc({},c,l))},{});return Object.keys(a).reduce(function(t,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(t,i){var a=c.stackGroups[i];return sa(sa({},t),{},sc({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:sE(e,a.items,o)}))},{})),sa(sa({},t),{},sc({},i,c))},{})},sT=function(e,t){var r=t.realScaleType,n=t.type,o=t.tickCount,i=t.originalDomain,a=t.allowDecimals,c=r||t.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var l=e.domain();if(!l.length)return null;var s=lY(l,o,a);return e.domain([lt()(s),c7()(s)]),{niceTicks:s}}return o&&"number"===n?{niceTicks:lZ(e.domain(),o,a)}:null};function sN(e){var t=e.axis,r=e.ticks,n=e.bandSize,o=e.entry,i=e.index,a=e.dataKey;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!eo()(o[t.dataKey])){var c=Q(r,"value",o[t.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var l=sl(o,eo()(a)?t.dataKey:a);return eo()(l)?null:t.scale(l)}var s_=function(e){var t=e.axis,r=e.ticks,n=e.offset,o=e.bandSize,i=e.entry,a=e.index;if("category"===t.type)return r[a]?r[a].coordinate+n:null;var c=sl(i,t.dataKey,t.domain[a]);return eo()(c)?null:t.scale(c)-o/2+n},sC=function(e){var t=e.numericAxis,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},sD=function(e,t){var r,n=(null!==(r=e.type)&&void 0!==r&&r.defaultProps?sa(sa({},e.type.defaultProps),e.props):e.props).stackId;if(V(n)){var o=t[n];if(o){var i=o.items.indexOf(e);return i>=0?o.stackedData[i]:null}}return null},sI=function(e,t,r){return Object.keys(e).reduce(function(n,o){var i=e[o].stackedData.reduce(function(e,n){var o=n.slice(t,r+1).reduce(function(e,t){return[lt()(t.concat([e[0]]).filter(K)),c7()(t.concat([e[1]]).filter(K))]},[1/0,-1/0]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},sR=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,sB=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,sL=function(e,t,r){if(ea()(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if(K(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(sR.test(e[0])){var o=+sR.exec(e[0])[1];n[0]=t[0]-o}else ea()(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if(K(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(sB.test(e[1])){var i=+sB.exec(e[1])[1];n[1]=t[1]+i}else ea()(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},sz=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=eK()(t,function(e){return e.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var l=o[a],s=o[a-1];i=Math.min((l.coordinate||0)-(s.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},sF=function(e,t,r){return!e||!e.length||li()(e,F()(r,"type.defaultProps.domain"))?t:e},s$=function(e,t){var r=e.type.defaultProps?sa(sa({},e.type.defaultProps),e.props):e.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,l=r.chartType,s=r.hide;return sa(sa({},e_(e,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:sf(e),value:sl(t,n),type:c,payload:t,chartType:l,hide:s})};function sU(e){return(sU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sW(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sq(Object(r),!0).forEach(function(t){sK(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sK(e,t,r){var n;return(n=function(e,t){if("object"!=sU(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==sU(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var sV=["Webkit","Moz","O","ms"],sH=function(e,t){if(!e)return null;var r=e.replace(/(\w)/,function(e){return e.toUpperCase()}),n=sV.reduce(function(e,n){return sW(sW({},e),{},sK({},n+r,t))},{});return n[e]=t,n};function sG(e){return(sG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sX(){return(sX=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function sY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sZ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sY(Object(r),!0).forEach(function(t){s2(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sJ(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s5(n.key),n)}}function sQ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(sQ=function(){return!!e})()}function s0(e){return(s0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function s1(e,t){return(s1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function s2(e,t,r){return(t=s5(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s5(e){var t=function(e,t){if("object"!=sG(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sG(t)?t:t+""}var s4=function(e){var t=e.data,r=e.startIndex,n=e.endIndex,o=e.x,i=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var c=t.length,l=nq().domain(eq()(0,c)).range([o,o+i-a]),s=l.domain().map(function(e){return l(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:l(r),endX:l(n),scale:l,scaleValues:s}},s3=function(e){return e.changedTouches&&!!e.changedTouches.length},s6=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=s0(r),s2(t=function(e,t){if(t&&("object"===sG(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,sQ()?Reflect.construct(r,o||[],s0(this).constructor):r.apply(this,o)),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),s2(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),s2(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,r=e.endIndex,n=e.onDragEnd,o=e.startIndex;null==n||n({endIndex:r,startIndex:o})}),t.detachDragEndListener()}),s2(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),s2(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),s2(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),s2(t,"handleSlideDragStart",function(e){var r=s3(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&s1(e,t)}(n,e),t=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,r=e.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,l=Math.min(t,r),s=Math.max(t,r),u=n.getIndexInRange(o,l),f=n.getIndexInRange(o,s);return{startIndex:u-u%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,o=t.dataKey,i=sl(r[e],o,e);return ea()(n)?n(i,e):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,o=t.endX,i=this.props,a=i.x,c=i.width,l=i.travellerWidth,s=i.startIndex,u=i.endIndex,f=i.onChange,p=e.pageX-r;p>0?p=Math.min(p,a+c-l-o,a+c-l-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==s||d.endIndex!==u)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=s3(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,o=t.endX,i=t.startX,a=this.state[n],c=this.props,l=c.x,s=c.width,u=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-r;y>0?y=Math.min(y,l+s-u-a):y<0&&(y=Math.max(y,l-a)),h[n]=a+y;var m=this.getIndex(h),v=m.startIndex,b=m.endIndex,g=function(){var e=d.length-1;return"startX"===n&&(o>i?v%p==0:b%p==0)||!!(o<i)&&b===e||"endX"===n&&(o>i?b%p==0:v%p==0)||!!(o>i)&&b===e};this.setState(s2(s2({},n,a+y),"brushMoveStartX",e.pageX),function(){f&&g()&&f(m)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[t],l=o.indexOf(c);if(-1!==l){var s=l+e;if(-1!==s&&!(s>=o.length)){var u=o[s];("startX"!==t||!(u>=a))&&("endX"!==t||!(u<=i))&&this.setState(s2({},t,u),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.fill,c=e.stroke;return a().createElement("rect",{stroke:c,fill:i,x:t,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,c=e.data,l=e.children,s=e.padding,u=i.Children.only(l);return u?a().cloneElement(u,{x:t,y:r,width:n,height:o,margin:s,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(e,t){var r,o,i=this,c=this.props,l=c.y,s=c.travellerWidth,u=c.height,f=c.traveller,p=c.ariaLabel,d=c.data,h=c.startIndex,y=c.endIndex,m=Math.max(e,this.props.x),v=sZ(sZ({},e_(this.props,!1)),{},{x:m,y:l,width:s,height:u}),b=p||"Min value: ".concat(null===(r=d[h])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=d[y])||void 0===o?void 0:o.name);return a().createElement(eJ,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,v))}},{key:"renderSlide",value:function(e,t){var r=this.props,n=r.y,o=r.height,i=r.stroke,c=r.travellerWidth,l=Math.min(e,t)+c,s=Math.max(Math.abs(t-e)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:l,y:n,width:s,height:o})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,n=e.y,o=e.height,i=e.travellerWidth,c=e.stroke,l=this.state,s=l.startX,u=l.endX,f={pointerEvents:"none",fill:c};return a().createElement(eJ,{className:"recharts-brush-texts"},a().createElement(oh,sX({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,u)-5,y:n+o/2},f),this.getTextOfTick(t)),a().createElement(oh,sX({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,u)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,n=e.children,o=e.x,i=e.y,c=e.width,l=e.height,s=e.alwaysShowText,u=this.state,f=u.startX,p=u.endX,d=u.isTextActive,h=u.isSlideMoving,y=u.isTravellerMoving,m=u.isTravellerFocused;if(!t||!t.length||!K(o)||!K(i)||!K(c)||!K(l)||c<=0||l<=0)return null;var v=(0,_.A)("recharts-brush",r),b=1===a().Children.count(n),g=sH("userSelect","none");return a().createElement(eJ,{className:v,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||m||s)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,n=e.width,o=e.height,i=e.stroke,c=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:t,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:t+1,y1:c,x2:t+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:t+1,y1:c+2,x2:t+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):ea()(e)?e(t):n.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,o=e.x,i=e.travellerWidth,a=e.updateId,c=e.startIndex,l=e.endIndex;if(r!==t.prevData||a!==t.prevUpdateId)return sZ({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?s4({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:l}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||o!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([o,o+n-i]);var s=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:s}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=e.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);e[i]>t?o=i:n=i}return t>=e[o]?o:n}}],t&&sJ(n.prototype,t),r&&sJ(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function s8(e){return(s8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s9(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s9(Object(r),!0).forEach(function(t){ue(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s9(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ue(e,t,r){var n;return(n=function(e,t){if("object"!=s8(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==s8(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ut(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}s2(s6,"displayName","Brush"),s2(s6,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var ur=Math.PI/180,un=function(e,t,r,n){return{x:e+Math.cos(-ur*n)*r,y:t+Math.sin(-ur*n)*r}},uo=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},ui=function(e,t){var r=e.x,n=e.y;return Math.sqrt(Math.pow(r-t.x,2)+Math.pow(n-t.y,2))},ua=function(e,t){var r=e.x,n=e.y,o=t.cx,i=t.cy,a=ui({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},uc=function(e){var t=e.startAngle,r=e.endAngle,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},ul=function(e,t){var r,n=ua({x:e.x,y:e.y},t),o=n.radius,i=n.angle,a=t.innerRadius,c=t.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var l=uc(t),s=l.startAngle,u=l.endAngle,f=i;if(s<=u){for(;f>u;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=u}else{for(;f>s;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=s}return r?s7(s7({},t),{},{radius:o,angle:f+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null},us=function(e){return(0,i.isValidElement)(e)||ea()(e)||"boolean"==typeof e?"":e.className};function uu(e){return(uu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var uf=["offset"];function up(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function ud(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ud(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=uu(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=uu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==uu(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ud(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function uy(){return(uy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var um=function(e){var t=e.value,r=e.formatter,n=eo()(e.children)?t:e.children;return ea()(r)?r(n):n},uv=function(e,t,r){var n,o,i=e.position,c=e.viewBox,l=e.offset,s=e.className,u=c.cx,f=c.cy,p=c.innerRadius,d=c.outerRadius,h=c.startAngle,y=c.endAngle,m=c.clockWise,v=(p+d)/2,b=q(y-h)*Math.min(Math.abs(y-h),360),g=b>=0?1:-1;"insideStart"===i?(n=h+g*l,o=m):"insideEnd"===i?(n=y-g*l,o=!m):"end"===i&&(n=y+g*l,o=m),o=b<=0?o:!o;var x=un(u,f,v,n),w=un(u,f,v,n+(o?1:-1)*359),O="M".concat(x.x,",").concat(x.y,"\n    A").concat(v,",").concat(v,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),j=eo()(e.id)?G("recharts-radial-line-"):e.id;return a().createElement("text",uy({},r,{dominantBaseline:"central",className:(0,_.A)("recharts-radial-bar-label",s)}),a().createElement("defs",null,a().createElement("path",{id:j,d:O})),a().createElement("textPath",{xlinkHref:"#".concat(j)},t))},ub=function(e){var t=e.viewBox,r=e.offset,n=e.position,o=t.cx,i=t.cy,a=t.innerRadius,c=t.outerRadius,l=(t.startAngle+t.endAngle)/2;if("outside"===n){var s=un(o,i,c+r,l),u=s.x;return{x:u,y:s.y,textAnchor:u>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=un(o,i,(a+c)/2,l);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},ug=function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,o=e.position,i=t.x,a=t.y,c=t.width,l=t.height,s=l>=0?1:-1,u=s*n,f=s>0?"end":"start",p=s>0?"start":"end",d=c>=0?1:-1,h=d*n,y=d>0?"end":"start",m=d>0?"start":"end";if("top"===o)return uh(uh({},{x:i+c/2,y:a-s*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return uh(uh({},{x:i+c/2,y:a+l+u,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+l),0),width:c}:{});if("left"===o){var v={x:i-h,y:a+l/2,textAnchor:y,verticalAnchor:"middle"};return uh(uh({},v),r?{width:Math.max(v.x-r.x,0),height:l}:{})}if("right"===o){var b={x:i+c+h,y:a+l/2,textAnchor:m,verticalAnchor:"middle"};return uh(uh({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:l}:{})}var g=r?{width:c,height:l}:{};return"insideLeft"===o?uh({x:i+h,y:a+l/2,textAnchor:m,verticalAnchor:"middle"},g):"insideRight"===o?uh({x:i+c-h,y:a+l/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?uh({x:i+c/2,y:a+u,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?uh({x:i+c/2,y:a+l-u,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?uh({x:i+h,y:a+u,textAnchor:m,verticalAnchor:p},g):"insideTopRight"===o?uh({x:i+c-h,y:a+u,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?uh({x:i+h,y:a+l-u,textAnchor:m,verticalAnchor:f},g):"insideBottomRight"===o?uh({x:i+c-h,y:a+l-u,textAnchor:y,verticalAnchor:f},g):el()(o)&&(K(o.x)||W(o.x))&&(K(o.y)||W(o.y))?uh({x:i+X(o.x,c),y:a+X(o.y,l),textAnchor:"end",verticalAnchor:"end"},g):uh({x:i+c/2,y:a+l/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function ux(e){var t,r=e.offset,n=uh({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,uf)),o=n.viewBox,c=n.position,l=n.value,s=n.children,u=n.content,f=n.className,p=n.textBreakAll;if(!o||eo()(l)&&eo()(s)&&!(0,i.isValidElement)(u)&&!ea()(u))return null;if((0,i.isValidElement)(u))return(0,i.cloneElement)(u,n);if(ea()(u)){if(t=(0,i.createElement)(u,n),(0,i.isValidElement)(t))return t}else t=um(n);var d="cx"in o&&K(o.cx),h=e_(n,!0);if(d&&("insideStart"===c||"insideEnd"===c||"end"===c))return uv(n,t,h);var y=d?ub(n):ug(n);return a().createElement(oh,uy({className:(0,_.A)("recharts-label",void 0===f?"":f)},h,y,{breakAll:p}),t)}ux.displayName="Label";var uw=function(e){var t=e.cx,r=e.cy,n=e.angle,o=e.startAngle,i=e.endAngle,a=e.r,c=e.radius,l=e.innerRadius,s=e.outerRadius,u=e.x,f=e.y,p=e.top,d=e.left,h=e.width,y=e.height,m=e.clockWise,v=e.labelViewBox;if(v)return v;if(K(h)&&K(y)){if(K(u)&&K(f))return{x:u,y:f,width:h,height:y};if(K(p)&&K(d))return{x:p,y:d,width:h,height:y}}return K(u)&&K(f)?{x:u,y:f,width:0,height:0}:K(t)&&K(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:l||0,outerRadius:s||c||a||0,clockWise:m}:e.viewBox?e.viewBox:{}};ux.parseViewBox=uw,ux.renderCallByParent=function(e,t){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var c=e.children,l=uw(e),s=ek(c,ux).map(function(e,r){return(0,i.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)})});if(!o)return s;return[(r=e.label,n=t||l,r?!0===r?a().createElement(ux,{key:"label-implicit",viewBox:n}):V(r)?a().createElement(ux,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===ux?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(ux,{key:"label-implicit",content:r,viewBox:n}):ea()(r)?a().createElement(ux,{key:"label-implicit",content:r,viewBox:n}):el()(r)?a().createElement(ux,uy({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(e){if(Array.isArray(e))return up(e)}(s)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(s)||function(e,t){if(e){if("string"==typeof e)return up(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return up(e,t)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var uO=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},uj=r(69691),uS=r.n(uj),uA=r(47212),uP=r.n(uA),uk=function(e){return null};uk.displayName="Cell";var uE=r(5359),uM=r.n(uE);function uT(e){return(uT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var uN=["valueAccessor"],u_=["data","dataKey","clockWise","id","textBreakAll"];function uC(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function uD(){return(uD=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function uI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uI(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=uT(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=uT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==uT(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function uB(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var uL=function(e){return Array.isArray(e.value)?uM()(e.value):e.value};function uz(e){var t=e.valueAccessor,r=void 0===t?uL:t,n=uB(e,uN),o=n.data,i=n.dataKey,c=n.clockWise,l=n.id,s=n.textBreakAll,u=uB(n,u_);return o&&o.length?a().createElement(eJ,{className:"recharts-label-list"},o.map(function(e,t){var n=eo()(i)?r(e,t):sl(e&&e.payload,i),o=eo()(l)?{}:{id:"".concat(l,"-").concat(t)};return a().createElement(ux,uD({},e_(e,!0),u,o,{parentViewBox:e.parentViewBox,value:n,textBreakAll:s,viewBox:ux.parseViewBox(eo()(c)?e:uR(uR({},e),{},{clockWise:c})),key:"label-".concat(t),index:t}))})):null}uz.displayName="LabelList",uz.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var o=ek(e.children,uz).map(function(e,r){return(0,i.cloneElement)(e,{data:t,key:"labelList-".concat(r)})});return n?[(r=e.label)?!0===r?a().createElement(uz,{key:"labelList-implicit",data:t}):a().isValidElement(r)||ea()(r)?a().createElement(uz,{key:"labelList-implicit",data:t,content:r}):el()(r)?a().createElement(uz,uD({data:t},r,{key:"labelList-implicit"})):null:null].concat(function(e){if(Array.isArray(e))return uC(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||function(e,t){if(e){if("string"==typeof e)return uC(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uC(e,t)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var uF=r(38404),u$=r.n(uF),uU=r(98451),uq=r.n(uU);function uW(e){return(uW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function uK(){return(uK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function uV(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function uH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uH(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=uW(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=uW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==uW(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var uX=function(e,t,r,n,o){var i,a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+o)+"L ".concat(e+r-a/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},uY={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},uZ=function(e){var t,r=uG(uG({},uY),e),n=(0,i.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,i.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,2)||function(e,t){if(e){if("string"==typeof e)return uV(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uV(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],l=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&l(e)}catch(e){}},[]);var s=r.x,u=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,h=r.className,y=r.animationEasing,m=r.animationDuration,v=r.animationBegin,b=r.isUpdateAnimationActive;if(s!==+s||u!==+u||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var g=(0,_.A)("recharts-trapezoid",h);return b?a().createElement(nA,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:d,x:s,y:u},to:{upperWidth:f,lowerWidth:p,height:d,x:s,y:u},duration:m,animationEasing:y,isActive:b},function(e){var t=e.upperWidth,o=e.lowerWidth,i=e.height,l=e.x,s=e.y;return a().createElement(nA,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:m,easing:y},a().createElement("path",uK({},e_(r,!0),{className:g,d:uX(l,s,t,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",uK({},e_(r,!0),{className:g,d:uX(s,u,f,p,d)})))};function uJ(e){return(uJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function uQ(){return(uQ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function u0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u0(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=uJ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=uJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==uJ(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u0(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var u2=function(e){var t=e.cx,r=e.cy,n=e.radius,o=e.angle,i=e.sign,a=e.isExternal,c=e.cornerRadius,l=e.cornerIsExternal,s=c*(a?1:-1)+n,u=Math.asin(c/s)/ur,f=l?o:o+i*u;return{center:un(t,r,s,f),circleTangency:un(t,r,n,f),lineTangency:un(t,r,s*Math.cos(u*ur),l?o-i*u:o),theta:u}},u5=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.startAngle,a=e.endAngle,c=q(a-i)*Math.min(Math.abs(a-i),359.999),l=i+c,s=un(t,r,o,i),u=un(t,r,o,l),f="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>l),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var p=un(t,r,n,i),d=un(t,r,n,l);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=l),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},u4=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.cornerRadius,a=e.forceCornerRadius,c=e.cornerIsExternal,l=e.startAngle,s=e.endAngle,u=q(s-l),f=u2({cx:t,cy:r,radius:o,angle:l,sign:u,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=u2({cx:t,cy:r,radius:o,angle:s,sign:-u,cornerRadius:i,cornerIsExternal:c}),m=y.circleTangency,v=y.lineTangency,b=y.theta,g=c?Math.abs(l-s):Math.abs(l-s)-h-b;if(g<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):u5({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(m.x,",").concat(m.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(v.x,",").concat(v.y,"\n  ");if(n>0){var w=u2({cx:t,cy:r,radius:n,angle:l,sign:u,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,S=w.theta,A=u2({cx:t,cy:r,radius:n,angle:s,sign:-u,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),P=A.circleTangency,k=A.lineTangency,E=A.theta,M=c?Math.abs(l-s):Math.abs(l-s)-S-E;if(M<0&&0===i)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(u>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},u3={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},u6=function(e){var t,r=u1(u1({},u3),e),n=r.cx,o=r.cy,i=r.innerRadius,c=r.outerRadius,l=r.cornerRadius,s=r.forceCornerRadius,u=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(c<i||f===p)return null;var h=(0,_.A)("recharts-sector",d),y=c-i,m=X(l,y,0,!0);return t=m>0&&360>Math.abs(f-p)?u4({cx:n,cy:o,innerRadius:i,outerRadius:c,cornerRadius:Math.min(m,y/2),forceCornerRadius:s,cornerIsExternal:u,startAngle:f,endAngle:p}):u5({cx:n,cy:o,innerRadius:i,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",uQ({},e_(r,!0),{className:h,d:t,role:"img"}))},u8=["option","shapeType","propTransformer","activeClassName","isActive"];function u9(e){return(u9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u7(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=u9(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==u9(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ft(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return a().createElement(nD,r);case"trapezoid":return a().createElement(uZ,r);case"sector":return a().createElement(u6,r);case"symbols":if("symbols"===t)return a().createElement(t2,r);break;default:return null}}function fr(e){var t,r=e.option,n=e.shapeType,o=e.propTransformer,c=e.activeClassName,l=e.isActive,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,u8);if((0,i.isValidElement)(r))t=(0,i.cloneElement)(r,fe(fe({},s),(0,i.isValidElement)(r)?r.props:r));else if(ea()(r))t=r(s);else if(u$()(r)&&!uq()(r)){var u=(void 0===o?function(e,t){return fe(fe({},t),e)}:o)(r,s);t=a().createElement(ft,{shapeType:n,elementProps:u})}else t=a().createElement(ft,{shapeType:n,elementProps:s});return l?a().createElement(eJ,{className:void 0===c?"recharts-active-shape":c},t):t}function fn(e,t){return null!=t&&"trapezoids"in e.props}function fo(e,t){return null!=t&&"sectors"in e.props}function fi(e,t){return null!=t&&"points"in e.props}function fa(e,t){var r,n,o=e.x===(null==t||null===(r=t.labelViewBox)||void 0===r?void 0:r.x)||e.x===t.x,i=e.y===(null==t||null===(n=t.labelViewBox)||void 0===n?void 0:n.y)||e.y===t.y;return o&&i}function fc(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function fl(e,t){var r=e.x===t.x,n=e.y===t.y,o=e.z===t.z;return r&&n&&o}var fs=["x","y"];function fu(e){return(fu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ff(){return(ff=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fd(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fp(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=fu(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fu(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fh(e,t){var r=e.x,n=e.y,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,fs),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(t.height||o.height),10),l=parseInt("".concat(t.width||o.width),10);return fd(fd(fd(fd(fd({},t),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:l,name:t.name,radius:t.radius})}function fy(e){return a().createElement(fr,ff({shapeType:"rectangle",propTransformer:fh,activeClassName:"recharts-active-bar"},e))}var fm=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof e)return e;var o="number"==typeof r;return o?e(r,n):(o||eV(!1),t)}},fv=["value","background"];function fb(e){return(fb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fg(){return(fg=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fx(Object(r),!0).forEach(function(t){fP(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fx(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fO(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fk(n.key),n)}}function fj(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(fj=function(){return!!e})()}function fS(e){return(fS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function fA(e,t){return(fA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function fP(e,t,r){return(t=fk(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fk(e){var t=function(e,t){if("object"!=fb(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fb(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fb(t)?t:t+""}var fE=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=fS(t),fP(e=function(e,t){if(t&&("object"===fb(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,fj()?Reflect.construct(t,r||[],fS(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!1}),fP(e,"id",G("recharts-bar-")),fP(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),t&&t()}),fP(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),t&&t()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fA(e,t)}(n,e),t=[{key:"renderRectanglesStatically",value:function(e){var t=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,c=r.activeBar,l=e_(this.props,!1);return e&&e.map(function(e,r){var s=r===i,u=fw(fw(fw({},l),e),{},{isActive:s,option:s?c:n,index:r,dataKey:o,onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd});return a().createElement(eJ,fg({className:"recharts-bar-rectangle"},ev(t.props,e,r),{key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),a().createElement(fy,u))})}},{key:"renderRectanglesWithAnimation",value:function(){var e=this,t=this.props,r=t.data,n=t.layout,o=t.isAnimationActive,i=t.animationBegin,c=t.animationDuration,l=t.animationEasing,s=t.animationId,u=this.state.prevData;return a().createElement(nA,{begin:i,duration:c,isActive:o,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(t){var o=t.t,i=r.map(function(e,t){var r=u&&u[t];if(r){var i=J(r.x,e.x),a=J(r.y,e.y),c=J(r.width,e.width),l=J(r.height,e.height);return fw(fw({},e),{},{x:i(o),y:a(o),width:c(o),height:l(o)})}if("horizontal"===n){var s=J(0,e.height)(o);return fw(fw({},e),{},{y:e.y+e.height-s,height:s})}var f=J(0,e.width)(o);return fw(fw({},e),{},{width:f})});return a().createElement(eJ,null,e.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var e=this.props,t=e.data,r=e.isAnimationActive,n=this.state.prevData;return r&&t&&t.length&&(!n||!li()(n,t))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(t)}},{key:"renderBackground",value:function(){var e=this,t=this.props,r=t.data,n=t.dataKey,o=t.activeIndex,i=e_(this.props.background,!1);return r.map(function(t,r){t.value;var c=t.background,l=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,fv);if(!c)return null;var s=fw(fw(fw(fw(fw({},l),{},{fill:"#eee"},c),i),ev(e.props,t,r)),{},{onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(fy,fg({key:"background-bar-".concat(r),option:e.props.background,isActive:r===o},s))})}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,c=r.layout,l=ek(r.children,l8);if(!l)return null;var s="vertical"===c?n[0].height/2:n[0].width/2,u=function(e,t){var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:sl(e,t)}};return a().createElement(eJ,{clipPath:e?"url(#clipPath-".concat(t,")"):null},l.map(function(e){return a().cloneElement(e,{key:"error-bar-".concat(t,"-").concat(e.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,offset:s,dataPointFormatter:u})}))}},{key:"render",value:function(){var e=this.props,t=e.hide,r=e.data,n=e.className,o=e.xAxis,i=e.yAxis,c=e.left,l=e.top,s=e.width,u=e.height,f=e.isAnimationActive,p=e.background,d=e.id;if(t||!r||!r.length)return null;var h=this.state.isAnimationFinished,y=(0,_.A)("recharts-bar",n),m=o&&o.allowDataOverflow,v=i&&i.allowDataOverflow,b=m||v,g=eo()(d)?this.id:d;return a().createElement(eJ,{className:y},m||v?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(g)},a().createElement("rect",{x:m?c:c-s/2,y:v?l:l-u/2,width:m?s:2*s,height:v?u:2*u}))):null,a().createElement(eJ,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||h)&&uz.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curData:e.data,prevData:t.curData}:e.data!==t.curData?{curData:e.data}:null}}],t&&fO(n.prototype,t),r&&fO(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function fM(e){return(fM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fD(n.key),n)}}function fN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fN(Object(r),!0).forEach(function(t){fC(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fC(e,t,r){return(t=fD(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fD(e){var t=function(e,t){if("object"!=fM(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fM(t)?t:t+""}fP(fE,"displayName","Bar"),fP(fE,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!tu.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fP(fE,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,o=e.bandSize,i=e.xAxis,a=e.yAxis,c=e.xAxisTicks,l=e.yAxisTicks,s=e.stackedData,u=e.dataStartIndex,f=e.displayedData,p=e.offset,d=sA(n,r);if(!d)return null;var h=t.layout,y=r.type.defaultProps,m=void 0!==y?fw(fw({},y),r.props):r.props,v=m.dataKey,b=m.children,g=m.minPointSize,x="horizontal"===h?a:i,w=s?x.scale.domain():null,O=sC({numericAxis:x}),j=ek(b,uk),S=f.map(function(e,t){s?f=sP(s[u+t],w):Array.isArray(f=sl(e,v))||(f=[O,f]);var n=fm(g,fE.defaultProps.minPointSize)(f[1],t);if("horizontal"===h){var f,p,y,m,b,x,S,A=[a.scale(f[0]),a.scale(f[1])],P=A[0],k=A[1];p=s_({axis:i,ticks:c,bandSize:o,offset:d.offset,entry:e,index:t}),y=null!==(S=null!=k?k:P)&&void 0!==S?S:void 0,m=d.size;var E=P-k;if(b=Number.isNaN(E)?0:E,x={x:p,y:a.y,width:m,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=q(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var T=[i.scale(f[0]),i.scale(f[1])],N=T[0],_=T[1];if(p=N,y=s_({axis:a,ticks:l,bandSize:o,offset:d.offset,entry:e,index:t}),m=_-N,b=d.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(m)<Math.abs(n)){var C=q(m||n)*(Math.abs(n)-Math.abs(m));m+=C}}return fw(fw(fw({},e),{},{x:p,y:y,width:m,height:b,value:s?f:f[1],payload:e,background:x},j&&j[t]&&j[t].props),{},{tooltipPayload:[s$(r,e)],tooltipPosition:{x:p+m/2,y:y+b/2}})});return fw({data:S,layout:h},p)});var fI=function(e,t,r,n,o){var i=e.width,a=e.height,c=e.layout,l=e.children,s=Object.keys(t),u={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!eE(l,fE);return s.reduce(function(i,a){var l,s,p,d,h,y=t[a],m=y.orientation,v=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(m).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=v[1]-v[0],S=1/0,A=y.categoricalDomain.sort(et);if(A.forEach(function(e,t){t>0&&(S=Math.min((e||0)-(A[t-1]||0),S))}),Number.isFinite(S)){var P=S/j,k="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(l=P*k/2),"no-gap"===y.padding){var E=X(e.barCategoryGap,P*k),M=P*k/2;l=M-E-(M-E)/k*E}}}s="xAxis"===n?[r.left+(g.left||0)+(l||0),r.left+r.width-(g.right||0)-(l||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(l||0),r.top+r.height-(g.bottom||0)-(l||0)]:y.range,w&&(s=[s[1],s[0]]);var T=sj(y,o,f),N=T.scale,_=T.realScaleType;N.domain(v).range(s),sS(N);var C=sT(N,f_(f_({},y),{},{realScaleType:_}));"xAxis"===n?(h="top"===m&&!x||"bottom"===m&&x,p=r.left,d=u[O]-h*y.height):"yAxis"===n&&(h="left"===m&&!x||"right"===m&&x,p=u[O]-h*y.width,d=r.top);var D=f_(f_(f_({},y),C),{},{realScaleType:_,x:p,y:d,scale:N,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=sz(D,C),y.hide||"xAxis"!==n?y.hide||(u[O]+=(h?-1:1)*D.width):u[O]+=(h?-1:1)*D.height,f_(f_({},i),{},fC({},a,D))},{})},fR=function(e,t){var r=e.x,n=e.y,o=t.x,i=t.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fB=function(){var e,t;function r(e){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=e}return e=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],t=[{key:"create",value:function(e){return new r(e)}}],e&&fT(r.prototype,e),t&&fT(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();fC(fB,"EPS",1e-4);var fL=function(e){var t=Object.keys(e).reduce(function(t,r){return f_(f_({},t),{},fC({},r,fB.create(e[r])))},{});return f_(f_({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return uS()(e,function(e,r){return t[r].apply(e,{bandAware:n,position:o})})},isInRange:function(e){return uP()(e,function(e,r){return t[r].isInRange(e)})}})},fz=function(e){var t=e.width,r=e.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/t);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):t/Math.cos(o))};function fF(){return(fF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function f$(e){return(f$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fU(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fU(Object(r),!0).forEach(function(t){fH(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fU(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fW(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(fW=function(){return!!e})()}function fK(e){return(fK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function fV(e,t){return(fV=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function fH(e,t,r){return(t=fG(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fG(e){var t=function(e,t){if("object"!=f$(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f$(t)?t:t+""}var fX=function(e){var t=e.x,r=e.y,n=e.xAxis,o=e.yAxis,i=fL({x:n.scale,y:o.scale}),a=i.apply({x:t,y:r},{bandAware:!0});return uO(e,"discard")&&!i.isInRange(a)?null:a},fY=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=fK(e),function(e,t){if(t&&("object"===f$(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,fW()?Reflect.construct(e,t||[],fK(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fV(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x,n=e.y,o=e.r,i=e.alwaysShow,c=e.clipPathId,l=V(t),s=V(n);if(er(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!l||!s)return null;var u=fX(this.props);if(!u)return null;var f=u.x,p=u.y,d=this.props,h=d.shape,y=d.className,m=fq(fq({clipPath:uO(this.props,"hidden")?"url(#".concat(c,")"):void 0},e_(this.props,!0)),{},{cx:f,cy:p});return a().createElement(eJ,{className:(0,_.A)("recharts-reference-dot",y)},r.renderDot(h,m),ux.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fG(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fH(fY,"displayName","ReferenceDot"),fH(fY,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fH(fY,"renderDot",function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):ea()(e)?e(t):a().createElement(ry,fF({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var fZ=r(67367),fJ=r.n(fZ),fQ=r(22964),f0=r.n(fQ),f1=r(86451),f2=r.n(f1)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),f5=(0,i.createContext)(void 0),f4=(0,i.createContext)(void 0),f3=(0,i.createContext)(void 0),f6=(0,i.createContext)({}),f8=(0,i.createContext)(void 0),f9=(0,i.createContext)(0),f7=(0,i.createContext)(0),pe=function(e){var t=e.state,r=t.xAxisMap,n=t.yAxisMap,o=t.offset,i=e.clipPathId,c=e.children,l=e.width,s=e.height,u=f2(o);return a().createElement(f5.Provider,{value:r},a().createElement(f4.Provider,{value:n},a().createElement(f6.Provider,{value:o},a().createElement(f3.Provider,{value:u},a().createElement(f8.Provider,{value:i},a().createElement(f9.Provider,{value:s},a().createElement(f7.Provider,{value:l},c)))))))},pt=function(e){var t=(0,i.useContext)(f5);null==t&&eV(!1);var r=t[e];return null==r&&eV(!1),r},pr=function(){var e=(0,i.useContext)(f4);return f0()(e,function(e){return uP()(e.domain,Number.isFinite)})||Y(e)},pn=function(e){var t=(0,i.useContext)(f4);null==t&&eV(!1);var r=t[e];return null==r&&eV(!1),r},po=function(){return(0,i.useContext)(f7)},pi=function(){return(0,i.useContext)(f9)};function pa(e){return(pa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pc(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pc=function(){return!!e})()}function pl(e){return(pl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ps(e,t){return(ps=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pu(Object(r),!0).forEach(function(t){pp(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pu(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pp(e,t,r){return(t=pd(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pd(e){var t=function(e,t){if("object"!=pa(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pa(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pa(t)?t:t+""}function ph(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function py(){return(py=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var pm=function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):ea()(e)?e(t):a().createElement("line",py({},t,{className:"recharts-reference-line-line"}))},pv=function(e,t,r,n,o,i,a,c,l){var s=o.x,u=o.y,f=o.width,p=o.height;if(r){var d=l.y,h=e.y.apply(d,{position:i});if(uO(l,"discard")&&!e.y.isInRange(h))return null;var y=[{x:s+f,y:h},{x:s,y:h}];return"left"===c?y.reverse():y}if(t){var m=l.x,v=e.x.apply(m,{position:i});if(uO(l,"discard")&&!e.x.isInRange(v))return null;var b=[{x:v,y:u+p},{x:v,y:u}];return"top"===a?b.reverse():b}if(n){var g=l.segment.map(function(t){return e.apply(t,{position:i})});return uO(l,"discard")&&fJ()(g,function(t){return!e.isInRange(t)})?null:g}return null};function pb(e){var t,r=e.x,n=e.y,o=e.segment,c=e.xAxisId,l=e.yAxisId,s=e.shape,u=e.className,f=e.alwaysShow,p=(0,i.useContext)(f8),d=pt(c),h=pn(l),y=(0,i.useContext)(f3);if(!p||!y)return null;er(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=pv(fL({x:d.scale,y:h.scale}),V(r),V(n),o&&2===o.length,y,e.position,d.orientation,h.orientation,e);if(!m)return null;var v=function(e){if(Array.isArray(e))return e}(m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(m,2)||function(e,t){if(e){if("string"==typeof e)return ph(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ph(e,t)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=v[0],g=b.x,x=b.y,w=v[1],O=w.x,j=w.y,S=pf(pf({clipPath:uO(e,"hidden")?"url(#".concat(p,")"):void 0},e_(e,!0)),{},{x1:g,y1:x,x2:O,y2:j});return a().createElement(eJ,{className:(0,_.A)("recharts-reference-line",u)},pm(s,S),ux.renderCallByParent(e,fR({x:(t={x1:g,y1:x,x2:O,y2:j}).x1,y:t.y1},{x:t.x2,y:t.y2})))}var pg=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=pl(e),function(e,t){if(t&&("object"===pa(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pc()?Reflect.construct(e,t||[],pl(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ps(e,t)}(r,e),t=[{key:"render",value:function(){return a().createElement(pb,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pd(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function px(){return(px=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pw(e){return(pw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pO(Object(r),!0).forEach(function(t){pk(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}pp(pg,"displayName","ReferenceLine"),pp(pg,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pS(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pS=function(){return!!e})()}function pA(e){return(pA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pP(e,t){return(pP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pk(e,t,r){return(t=pE(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pE(e){var t=function(e,t){if("object"!=pw(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pw(t)?t:t+""}var pM=function(e,t,r,n,o){var i=o.x1,a=o.x2,c=o.y1,l=o.y2,s=o.xAxis,u=o.yAxis;if(!s||!u)return null;var f=fL({x:s.scale,y:u.scale}),p={x:e?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},d={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(l,{position:"end"}):f.y.rangeMax};return!uO(o,"discard")||f.isInRange(p)&&f.isInRange(d)?fR(p,d):null},pT=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=pA(e),function(e,t){if(t&&("object"===pw(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pS()?Reflect.construct(e,t||[],pA(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pP(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x1,n=e.x2,o=e.y1,i=e.y2,c=e.className,l=e.alwaysShow,s=e.clipPathId;er(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var u=V(t),f=V(n),p=V(o),d=V(i),h=this.props.shape;if(!u&&!f&&!p&&!d&&!h)return null;var y=pM(u,f,p,d,this.props);if(!y&&!h)return null;var m=uO(this.props,"hidden")?"url(#".concat(s,")"):void 0;return a().createElement(eJ,{className:(0,_.A)("recharts-reference-area",c)},r.renderRect(h,pj(pj({clipPath:m},e_(this.props,!0)),y)),ux.renderCallByParent(this.props,y))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pE(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pN(e){return function(e){if(Array.isArray(e))return p_(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return p_(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p_(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p_(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}pk(pT,"displayName","ReferenceArea"),pk(pT,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pk(pT,"renderRect",function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):ea()(e)?e(t):a().createElement(nD,px({},t,{className:"recharts-reference-area-rect"}))});var pC=function(e,t,r,n,o){var i=ek(e,pg),a=ek(e,fY),c=[].concat(pN(i),pN(a)),l=ek(e,pT),s="".concat(n,"Id"),u=n[0],f=t;if(c.length&&(f=c.reduce(function(e,t){if(t.props[s]===r&&uO(t.props,"extendDomain")&&K(t.props[u])){var n=t.props[u];return[Math.min(e[0],n),Math.max(e[1],n)]}return e},f)),l.length){var p="".concat(u,"1"),d="".concat(u,"2");f=l.reduce(function(e,t){if(t.props[s]===r&&uO(t.props,"extendDomain")&&K(t.props[p])&&K(t.props[d])){var n=t.props[p],o=t.props[d];return[Math.min(e[0],n,o),Math.max(e[1],n,o)]}return e},f)}return o&&o.length&&(f=o.reduce(function(e,t){return K(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},f)),f},pD=r(11117),pI=new(r.n(pD)()),pR="recharts.syncMouseEvents";function pB(e){return(pB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pL(e,t,r){return(t=pz(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pz(e){var t=function(e,t){if("object"!=pB(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pB(t)?t:t+""}var pF=function(){var e,t;return e=function e(){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,e),pL(this,"activeIndex",0),pL(this,"coordinateList",[]),pL(this,"layout","horizontal")},t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,o=e.container,i=void 0===o?null:o,a=e.layout,c=void 0===a?null:a,l=e.offset,s=void 0===l?null:l,u=e.mouseHandlerCallback,f=void 0===u?null:u;this.coordinateList=null!==(t=null!=n?n:this.coordinateList)&&void 0!==t?t:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=s?s:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(e=window)||void 0===e?void 0:e.scrollX)||0,l=(null===(t=window)||void 0===t?void 0:t.scrollY)||0,s=o+this.offset.top+i/2+l;this.mouseHandlerCallback({pageX:n+a+c,pageY:s})}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pz(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}();function p$(){}function pU(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function pq(e){this._context=e}function pW(e){this._context=e}function pK(e){this._context=e}pq.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pU(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pU(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},pW.prototype={areaStart:p$,areaEnd:p$,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:pU(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},pK.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pU(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class pV{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function pH(e){this._context=e}function pG(e){this._context=e}function pX(e){return new pG(e)}pH.prototype={areaStart:p$,areaEnd:p$,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function pY(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pZ(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function pJ(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,c=(i-n)/3;e._context.bezierCurveTo(n+c,o+c*t,i-c,a-c*r,i,a)}function pQ(e){this._context=e}function p0(e){this._context=new p1(e)}function p1(e){this._context=e}function p2(e){this._context=e}function p5(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function p4(e,t){this._context=e,this._t=t}function p3(e){return e[0]}function p6(e){return e[1]}function p8(e,t){var r=t$(!0),n=null,o=pX,i=null,a=tH(c);function c(c){var l,s,u,f=(c=cX(c)).length,p=!1;for(null==n&&(i=o(u=a())),l=0;l<=f;++l)!(l<f&&r(s=c[l],l,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+e(s,l,c),+t(s,l,c));if(u)return i=null,u+""||null}return e="function"==typeof e?e:void 0===e?p3:t$(e),t="function"==typeof t?t:void 0===t?p6:t$(t),c.x=function(t){return arguments.length?(e="function"==typeof t?t:t$(+t),c):e},c.y=function(e){return arguments.length?(t="function"==typeof e?e:t$(+e),c):t},c.defined=function(e){return arguments.length?(r="function"==typeof e?e:t$(!!e),c):r},c.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),c):o},c.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),c):n},c}function p9(e,t,r){var n=null,o=t$(!0),i=null,a=pX,c=null,l=tH(s);function s(s){var u,f,p,d,h,y=(s=cX(s)).length,m=!1,v=Array(y),b=Array(y);for(null==i&&(c=a(h=l())),u=0;u<=y;++u){if(!(u<y&&o(d=s[u],u,s))===m){if(m=!m)f=u,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=u-1;p>=f;--p)c.point(v[p],b[p]);c.lineEnd(),c.areaEnd()}}m&&(v[u]=+e(d,u,s),b[u]=+t(d,u,s),c.point(n?+n(d,u,s):v[u],r?+r(d,u,s):b[u]))}if(h)return c=null,h+""||null}function u(){return p8().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?p3:t$(+e),t="function"==typeof t?t:void 0===t?t$(0):t$(+t),r="function"==typeof r?r:void 0===r?p6:t$(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:t$(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:t$(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:t$(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:t$(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:t$(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:t$(+e),s):r},s.lineX0=s.lineY0=function(){return u().x(e).y(t)},s.lineY1=function(){return u().x(e).y(r)},s.lineX1=function(){return u().x(n).y(t)},s.defined=function(e){return arguments.length?(o="function"==typeof e?e:t$(!!e),s):o},s.curve=function(e){return arguments.length?(a=e,null!=i&&(c=a(i)),s):a},s.context=function(e){return arguments.length?(null==e?i=c=null:c=a(i=e),s):i},s}function p7(e){return(p7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function de(){return(de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dt(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=p7(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p7(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dt(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}pG.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},pQ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pJ(this,this._t0,pZ(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,pJ(this,pZ(this,r=pY(this,e,t)),r);break;default:pJ(this,this._t0,r=pY(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(p0.prototype=Object.create(pQ.prototype)).point=function(e,t){pQ.prototype.point.call(this,t,e)},p1.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},p2.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=p5(e),o=p5(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},p4.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var dn={curveBasisClosed:function(e){return new pW(e)},curveBasisOpen:function(e){return new pK(e)},curveBasis:function(e){return new pq(e)},curveBumpX:function(e){return new pV(e,!0)},curveBumpY:function(e){return new pV(e,!1)},curveLinearClosed:function(e){return new pH(e)},curveLinear:pX,curveMonotoneX:function(e){return new pQ(e)},curveMonotoneY:function(e){return new p0(e)},curveNatural:function(e){return new p2(e)},curveStep:function(e){return new p4(e,.5)},curveStepAfter:function(e){return new p4(e,1)},curveStepBefore:function(e){return new p4(e,0)}},di=function(e){return e.x===+e.x&&e.y===+e.y},da=function(e){return e.x},dc=function(e){return e.y},dl=function(e,t){if(ea()(e))return e;var r="curve".concat(tA()(e));return("curveMonotone"===r||"curveBump"===r)&&t?dn["".concat(r).concat("vertical"===t?"Y":"X")]:dn[r]||pX},ds=function(e){var t,r=e.type,n=e.points,o=void 0===n?[]:n,i=e.baseLine,a=e.layout,c=e.connectNulls,l=void 0!==c&&c,s=dl(void 0===r?"linear":r,a),u=l?o.filter(function(e){return di(e)}):o;if(Array.isArray(i)){var f=l?i.filter(function(e){return di(e)}):i,p=u.map(function(e,t){return dr(dr({},e),{},{base:f[t]})});return(t="vertical"===a?p9().y(dc).x1(da).x0(function(e){return e.base.x}):p9().x(da).y1(dc).y0(function(e){return e.base.y})).defined(di).curve(s),t(p)}return(t="vertical"===a&&K(i)?p9().y(dc).x1(da).x0(i):K(i)?p9().x(da).y1(dc).y0(i):p8().x(da).y(dc)).defined(di).curve(s),t(u)},du=function(e){var t=e.className,r=e.points,n=e.path,o=e.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?ds(e):n;return a().createElement("path",de({},e_(e,!1),em(e),{className:(0,_.A)("recharts-curve",t),d:i,ref:o}))};function df(e){return(df="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var dp=["x","y","top","left","width","height","className"];function dd(){return(dd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var dy=function(e){var t=e.x,r=void 0===t?0:t,n=e.y,o=void 0===n?0:n,i=e.top,c=void 0===i?0:i,l=e.left,s=void 0===l?0:l,u=e.width,f=void 0===u?0:u,p=e.height,d=void 0===p?0:p,h=e.className,y=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dh(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=df(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=df(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==df(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:r,y:o,top:c,left:s,width:f,height:d},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,dp));return K(r)&&K(o)&&K(f)&&K(d)&&K(c)&&K(s)?a().createElement("path",dd({},e_(y,!0),{className:(0,_.A)("recharts-cross",h),d:"M".concat(r,",").concat(c,"v").concat(d,"M").concat(s,",").concat(o,"h").concat(f)})):null};function dm(e){var t=e.cx,r=e.cy,n=e.radius,o=e.startAngle,i=e.endAngle;return{points:[un(t,r,n,o),un(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}function dv(e){return(dv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function db(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?db(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=dv(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dv(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):db(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dx(e){var t,r,n,o,a=e.element,c=e.tooltipEventType,l=e.isActive,s=e.activeCoordinate,u=e.activePayload,f=e.offset,p=e.activeTooltipIndex,d=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,m=null!==(r=a.props.cursor)&&void 0!==r?r:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!m||!l||!s||"ScatterChart"!==y&&"axis"!==c)return null;var v=du;if("ScatterChart"===y)o=s,v=dy;else if("BarChart"===y)t=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?s.x-t:f.left+.5,y:"horizontal"===h?f.top+.5:s.y-t,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},v=nD;else if("radial"===h){var b=dm(s),g=b.cx,x=b.cy,w=b.radius;o={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},v=u6}else o={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return dm(t);var c=t.cx,l=t.cy,s=t.innerRadius,u=t.outerRadius,f=t.angle,p=un(c,l,s,f),d=un(c,l,u,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(h,s,f)},v=du;var O=dg(dg(dg(dg({stroke:"#ccc",pointerEvents:"none"},f),o),e_(m,!1)),{},{payload:u,payloadIndex:p,className:(0,_.A)("recharts-tooltip-cursor",m.className)});return(0,i.isValidElement)(m)?(0,i.cloneElement)(m,O):(0,i.createElement)(v,O)}var dw=["item"],dO=["children","className","width","height","style","compact","title","desc"];function dj(e){return(dj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dS(){return(dS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dA(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||dN(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dP(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function dk(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(dk=function(){return!!e})()}function dE(e){return(dE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function dM(e,t){return(dM=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function dT(e){return function(e){if(Array.isArray(e))return d_(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||dN(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dN(e,t){if(e){if("string"==typeof e)return d_(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d_(e,t)}}function d_(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function dC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dC(Object(r),!0).forEach(function(t){dI(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dI(e,t,r){return(t=dR(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dR(e){var t=function(e,t){if("object"!=dj(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dj(t)?t:t+""}var dB={xAxis:["bottom","top"],yAxis:["left","right"]},dL={width:"100%",height:"100%"},dz={x:0,y:0};function dF(e){return e}var d$=function(e,t,r,n){var o=t.find(function(e){return e&&e.index===r});if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,a=n.radius;return dD(dD(dD({},n),un(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,l=n.angle;return dD(dD(dD({},n),un(n.cx,n.cy,c,l)),{},{angle:l,radius:c})}return dz},dU=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,o=t.dataEndIndex,i=(null!=r?r:[]).reduce(function(e,t){var r=t.props.data;return r&&r.length?[].concat(dT(e),dT(r)):e},[]);return i.length>0?i:e&&e.length&&K(n)&&K(o)?e.slice(n,o+1):[]};function dq(e){return"number"===e?[0,"auto"]:void 0}var dW=function(e,t,r,n){var o=e.graphicalItems,i=e.tooltipAxis,a=dU(t,e);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var l,s,u=null!==(l=c.props.data)&&void 0!==l?l:t;return(u&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(u=u.slice(e.dataStartIndex,e.dataEndIndex+1)),s=i.dataKey&&!i.allowDuplicatedCategory?Q(void 0===u?a:u,i.dataKey,n):u&&u[r]||a[r])?[].concat(dT(o),[s$(c,s)]):o},[])},dK=function(e,t,r,n){var o=n||{x:e.chartX,y:e.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=e.orderedTooltipTicks,c=e.tooltipAxis,l=e.tooltipTicks,s=su(i,a,l,c);if(s>=0&&l){var u=l[s]&&l[s].value,f=dW(e,t,s,u),p=d$(r,a,s,o);return{activeTooltipIndex:s,activeLabel:u,activePayload:f,activeCoordinate:p}}return null},dV=function(e,t){var r=t.axes,n=t.graphicalItems,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,c=t.dataStartIndex,l=t.dataEndIndex,s=e.layout,u=e.children,f=e.stackOffset,p=sb(s,o);return r.reduce(function(t,r){var d=void 0!==r.type.defaultProps?dD(dD({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,m=d.allowDataOverflow,v=d.allowDuplicatedCategory,b=d.scale,g=d.ticks,x=d.includeHidden,w=d[i];if(t[w])return t;var O=dU(e.data,{graphicalItems:n.filter(function(e){var t;return(i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i])===w}),dataStartIndex:c,dataEndIndex:l}),j=O.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],o=null==e?void 0:e[1];if(n&&o&&K(n)&&K(o))return!0}return!1})(d.domain,m,h)&&(P=sL(d.domain,null,m),p&&("number"===h||"auto"!==b)&&(E=ss(O,y,"category")));var S=dq(h);if(!P||0===P.length){var A,P,k,E,M,T=null!==(M=d.domain)&&void 0!==M?M:S;if(y){if(P=ss(O,y,h),"category"===h&&p){var N=Z(P);v&&N?(k=P,P=eq()(0,j)):v||(P=sF(T,P,r).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(dT(e),[t])},[]))}else if("category"===h)P=v?P.filter(function(e){return""!==e&&!eo()(e)}):sF(T,P,r).reduce(function(e,t){return e.indexOf(t)>=0||""===t||eo()(t)?e:[].concat(dT(e),[t])},[]);else if("number"===h){var _=sm(O,n.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===w&&(x||!o)}),y,o,s);_&&(P=_)}p&&("number"===h||"auto"!==b)&&(E=ss(O,y,"category"))}else P=p?eq()(0,j):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:sI(a[w].stackGroups,c,l):sv(O,n.filter(function(e){var t=i in e.props?e.props[i]:e.type.defaultProps[i],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===w&&(x||!r)}),h,s,!0);"number"===h?(P=pC(u,P,w,o,g),T&&(P=sL(T,P,m))):"category"===h&&T&&P.every(function(e){return T.indexOf(e)>=0})&&(P=T)}return dD(dD({},t),{},dI({},w,dD(dD({},d),{},{axisType:o,domain:P,categoricalDomain:E,duplicateDomain:k,originalDomain:null!==(A=d.domain)&&void 0!==A?A:S,isCategorical:p,layout:s})))},{})},dH=function(e,t){var r=t.graphicalItems,n=t.Axis,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,c=t.dataStartIndex,l=t.dataEndIndex,s=e.layout,u=e.children,f=dU(e.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:l}),p=f.length,d=sb(s,o),h=-1;return r.reduce(function(e,t){var y,m=(void 0!==t.type.defaultProps?dD(dD({},t.type.defaultProps),t.props):t.props)[i],v=dq("number");return e[m]?e:(h++,y=d?eq()(0,p):a&&a[m]&&a[m].hasStack?pC(u,y=sI(a[m].stackGroups,c,l),m,o):pC(u,y=sL(v,sv(f,r.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===m&&!o}),"number",s),n.defaultProps.allowDataOverflow),m,o),dD(dD({},e),{},dI({},m,dD(dD({axisType:o},n.defaultProps),{},{hide:!0,orientation:F()(dB,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:v,isCategorical:d,layout:s}))))},{})},dG=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,o=t.AxisComp,i=t.graphicalItems,a=t.stackGroups,c=t.dataStartIndex,l=t.dataEndIndex,s=e.children,u="".concat(n,"Id"),f=ek(s,o),p={};return f&&f.length?p=dV(e,{axes:f,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:c,dataEndIndex:l}):i&&i.length&&(p=dH(e,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:c,dataEndIndex:l})),p},dX=function(e){var t=Y(e),r=sx(t,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:eK()(r,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:sz(t,r)}},dY=function(e){var t=e.children,r=e.defaultShowTooltip,n=eE(t,s6),o=0,i=0;return e.data&&0!==e.data.length&&(i=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},dZ=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},dJ=function(e,t){var r=e.props,n=e.graphicalItems,o=e.xAxisMap,i=void 0===o?{}:o,a=e.yAxisMap,c=void 0===a?{}:a,l=r.width,s=r.height,u=r.children,f=r.margin||{},p=eE(u,s6),d=eE(u,rd),h=Object.keys(c).reduce(function(e,t){var r=c[t],n=r.orientation;return r.mirror||r.hide?e:dD(dD({},e),{},dI({},n,e[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:dD(dD({},e),{},dI({},n,F()(e,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),m=dD(dD({},y),h),v=m.bottom;p&&(m.bottom+=p.props.height||s6.defaultProps.height),d&&t&&(m=sh(m,n,r,t));var b=l-m.left-m.right,g=s-m.top-m.bottom;return dD(dD({brushBottom:v},m),{},{width:Math.max(b,0),height:Math.max(g,0)})},dQ=function(e){var t=e.chartName,r=e.GraphicalChild,n=e.defaultTooltipEventType,o=void 0===n?"axis":n,c=e.validateTooltipEventTypes,l=void 0===c?["axis"]:c,s=e.axisComponents,u=e.legendContent,f=e.formatAxisMap,p=e.defaultProps,d=function(e,t){var r=t.graphicalItems,n=t.stackGroups,o=t.offset,i=t.updateId,a=t.dataStartIndex,c=t.dataEndIndex,l=e.barSize,u=e.layout,f=e.barGap,p=e.barCategoryGap,d=e.maxBarSize,h=dZ(u),y=h.numericAxisName,m=h.cateAxisName,v=!!r&&!!r.length&&r.some(function(e){var t=ej(e&&e.type);return t&&t.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,h){var g=dU(e.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?dD(dD({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(m,"Id")],A=s.reduce(function(e,r){var n=t["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||eV(!1);var i=n[o];return dD(dD({},e),{},dI(dI({},r.axisType,i),"".concat(r.axisType,"Ticks"),sx(i)))},{}),P=A[m],k=A["".concat(m,"Ticks")],E=n&&n[j]&&n[j].hasStack&&sD(r,n[j].stackGroups),M=ej(r.type).indexOf("Bar")>=0,T=sz(P,k),N=[],_=v&&sp({barSize:l,stackGroups:n,totalSize:"xAxis"===m?A[m].width:"yAxis"===m?A[m].height:void 0});if(M){var C,D,I=eo()(O)?d:O,R=null!==(C=null!==(D=sz(P,k,!0))&&void 0!==D?D:I)&&void 0!==C?C:0;N=sd({barGap:f,barCategoryGap:p,bandSize:R!==T?R:T,sizeList:_[S],maxBarSize:I}),R!==T&&(N=N.map(function(e){return dD(dD({},e),{},{position:dD(dD({},e.position),{},{offset:e.position.offset-R/2})})}))}var B=r&&r.type&&r.type.getComposedData;B&&b.push({props:dD(dD({},B(dD(dD({},A),{},{displayedData:g,props:e,dataKey:w,item:r,bandSize:T,barPosition:N,offset:o,stackedData:E,layout:u,dataStartIndex:a,dataEndIndex:c}))),{},dI(dI(dI({key:r.key||"item-".concat(h)},y,A[y]),m,A[m]),"animationId",i)),childIndex:eP(e.children).indexOf(r),item:r})}),b},h=function(e,n){var o=e.props,i=e.dataStartIndex,a=e.dataEndIndex,c=e.updateId;if(!eM({props:o}))return null;var l=o.children,u=o.layout,p=o.stackOffset,h=o.data,y=o.reverseStackOrder,m=dZ(u),v=m.numericAxisName,b=m.cateAxisName,g=ek(l,r),x=sM(h,g,"".concat(v,"Id"),"".concat(b,"Id"),p,y),w=s.reduce(function(e,t){var r="".concat(t.axisType,"Map");return dD(dD({},e),{},dI({},r,dG(o,dD(dD({},t),{},{graphicalItems:g,stackGroups:t.axisType===v&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),O=dJ(dD(dD({},w),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(e){w[e]=f(o,w[e],O,e.replace("Map",""),t)});var j=dX(w["".concat(b,"Map")]),S=d(o,dD(dD({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return dD(dD({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},y=function(e){var r;function n(e){var r,o,c,l,s;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),l=n,s=[e],l=dE(l),dI(c=function(e,t){if(t&&("object"===dj(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,dk()?Reflect.construct(l,s||[],dE(this).constructor):l.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),dI(c,"accessibilityManager",new pF),dI(c,"handleLegendBBoxUpdate",function(e){if(e){var t=c.state,r=t.dataStartIndex,n=t.dataEndIndex,o=t.updateId;c.setState(dD({legendBBox:e},h({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},dD(dD({},c.state),{},{legendBBox:e}))))}}),dI(c,"handleReceiveSyncEvent",function(e,t,r){c.props.syncId===e&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(t)}),dI(c,"handleBrushChange",function(e){var t=e.startIndex,r=e.endIndex;if(t!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return dD({dataStartIndex:t,dataEndIndex:r},h({props:c.props,dataStartIndex:t,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}}),dI(c,"handleMouseEnter",function(e){var t=c.getMouseInfo(e);if(t){var r=dD(dD({},t),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;ea()(n)&&n(r,e)}}),dI(c,"triggeredAfterMouseMove",function(e){var t=c.getMouseInfo(e),r=t?dD(dD({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;ea()(n)&&n(r,e)}),dI(c,"handleItemMouseEnter",function(e){c.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),dI(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),dI(c,"handleMouseMove",function(e){e.persist(),c.throttleTriggeredAfterMouseMove(e)}),dI(c,"handleMouseLeave",function(e){c.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};c.setState(t),c.triggerSyncEvent(t);var r=c.props.onMouseLeave;ea()(r)&&r(t,e)}),dI(c,"handleOuterEvent",function(e){var t,r,n=eR(e),o=F()(c.props,"".concat(n));n&&ea()(o)&&o(null!==(t=/.*touch.*/i.test(n)?c.getMouseInfo(e.changedTouches[0]):c.getMouseInfo(e))&&void 0!==t?t:{},e)}),dI(c,"handleClick",function(e){var t=c.getMouseInfo(e);if(t){var r=dD(dD({},t),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;ea()(n)&&n(r,e)}}),dI(c,"handleMouseDown",function(e){var t=c.props.onMouseDown;ea()(t)&&t(c.getMouseInfo(e),e)}),dI(c,"handleMouseUp",function(e){var t=c.props.onMouseUp;ea()(t)&&t(c.getMouseInfo(e),e)}),dI(c,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),dI(c,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.handleMouseDown(e.changedTouches[0])}),dI(c,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.handleMouseUp(e.changedTouches[0])}),dI(c,"handleDoubleClick",function(e){var t=c.props.onDoubleClick;ea()(t)&&t(c.getMouseInfo(e),e)}),dI(c,"handleContextMenu",function(e){var t=c.props.onContextMenu;ea()(t)&&t(c.getMouseInfo(e),e)}),dI(c,"triggerSyncEvent",function(e){void 0!==c.props.syncId&&pI.emit(pR,c.props.syncId,e,c.eventEmitterSymbol)}),dI(c,"applySyncEvent",function(e){var t=c.props,r=t.layout,n=t.syncMethod,o=c.state.updateId,i=e.dataStartIndex,a=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)c.setState(dD({dataStartIndex:i,dataEndIndex:a},h({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==e.activeTooltipIndex){var l=e.chartX,s=e.chartY,u=e.activeTooltipIndex,f=c.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)u=n(d,e);else if("value"===n){u=-1;for(var y=0;y<d.length;y++)if(d[y].value===e.activeLabel){u=y;break}}var m=dD(dD({},p),{},{x:p.left,y:p.top}),v=Math.min(l,m.x+m.width),b=Math.min(s,m.y+m.height),g=d[u]&&d[u].value,x=dW(c.state,c.props.data,u),w=d[u]?{x:"horizontal"===r?d[u].coordinate:v,y:"horizontal"===r?b:d[u].coordinate}:dz;c.setState(dD(dD({},e),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:u}))}else c.setState(e)}),dI(c,"renderCursor",function(e){var r,n=c.state,o=n.isTooltipActive,i=n.activeCoordinate,l=n.activePayload,s=n.offset,u=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),d=null!==(r=e.props.active)&&void 0!==r?r:o,h=c.props.layout,y=e.key||"_recharts-cursor";return a().createElement(dx,{key:y,activeCoordinate:i,activePayload:l,activeTooltipIndex:u,chartName:t,element:e,isActive:d,layout:h,offset:s,tooltipAxisBandSize:f,tooltipEventType:p})}),dI(c,"renderPolarAxis",function(e,t,r){var n=F()(e,"type.axisType"),o=F()(c.state,"".concat(n,"Map")),a=e.type.defaultProps,l=void 0!==a?dD(dD({},a),e.props):e.props,s=o&&o[l["".concat(n,"Id")]];return(0,i.cloneElement)(e,dD(dD({},s),{},{className:(0,_.A)(n,s.className),key:e.key||"".concat(t,"-").concat(r),ticks:sx(s,!0)}))}),dI(c,"renderPolarGrid",function(e){var t=e.props,r=t.radialLines,n=t.polarAngles,o=t.polarRadius,a=c.state,l=a.radiusAxisMap,s=a.angleAxisMap,u=Y(l),f=Y(s),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(e,{polarAngles:Array.isArray(n)?n:sx(f,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(o)?o:sx(u,!0).map(function(e){return e.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:e.key||"polar-grid",radialLines:r})}),dI(c,"renderLegend",function(){var e=c.state.formattedGraphicalItems,t=c.props,r=t.children,n=t.width,o=t.height,a=c.props.margin||{},l=st({children:r,formattedGraphicalItems:e,legendWidth:n-(a.left||0)-(a.right||0),legendContent:u});if(!l)return null;var s=l.item,f=dP(l,dw);return(0,i.cloneElement)(s,dD(dD({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),dI(c,"renderTooltip",function(){var e,t=c.props,r=t.children,n=t.accessibilityLayer,o=eE(r,tj);if(!o)return null;var a=c.state,l=a.isTooltipActive,s=a.activeCoordinate,u=a.activePayload,f=a.activeLabel,p=a.offset,d=null!==(e=o.props.active)&&void 0!==e?e:l;return(0,i.cloneElement)(o,{viewBox:dD(dD({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?u:[],coordinate:s,accessibilityLayer:n})}),dI(c,"renderBrush",function(e){var t=c.props,r=t.margin,n=t.data,o=c.state,a=o.offset,l=o.dataStartIndex,s=o.dataEndIndex,u=o.updateId;return(0,i.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:sO(c.handleBrushChange,e.props.onChange),data:n,x:K(e.props.x)?e.props.x:a.left,y:K(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:K(e.props.width)?e.props.width:a.width,startIndex:l,endIndex:s,updateId:"brush-".concat(u)})}),dI(c,"renderReferenceElement",function(e,t,r){if(!e)return null;var n=c.clipPathId,o=c.state,a=o.xAxisMap,l=o.yAxisMap,s=o.offset,u=e.type.defaultProps||{},f=e.props,p=f.xAxisId,d=void 0===p?u.xAxisId:p,h=f.yAxisId,y=void 0===h?u.yAxisId:h;return(0,i.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:a[d],yAxis:l[y],viewBox:{x:s.left,y:s.top,width:s.width,height:s.height},clipPathId:n})}),dI(c,"renderActivePoints",function(e){var t=e.item,r=e.activePoint,o=e.basePoint,i=e.childIndex,a=e.isRange,c=[],l=t.props.key,s=void 0!==t.item.type.defaultProps?dD(dD({},t.item.type.defaultProps),t.item.props):t.item.props,u=s.activeDot,f=dD(dD({index:i,dataKey:s.dataKey,cx:r.x,cy:r.y,r:4,fill:sf(t.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},e_(u,!1)),em(u));return c.push(n.renderActiveDot(u,f,"".concat(l,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(u,dD(dD({},f),{},{cx:o.x,cy:o.y}),"".concat(l,"-basePoint-").concat(i))):a&&c.push(null),c}),dI(c,"renderGraphicChild",function(e,t,r){var n=c.filterFormatItem(e,t,r);if(!n)return null;var o=c.getTooltipEventType(),a=c.state,l=a.isTooltipActive,s=a.tooltipAxis,u=a.activeTooltipIndex,f=a.activeLabel,p=eE(c.props.children,tj),d=n.props,h=d.points,y=d.isRange,m=d.baseLine,v=void 0!==n.item.type.defaultProps?dD(dD({},n.item.type.defaultProps),n.item.props):n.item.props,b=v.activeDot,g=v.hide,x=v.activeBar,w=v.activeShape,O=!!(!g&&l&&p&&(b||x||w)),j={};"axis"!==o&&p&&"click"===p.props.trigger?j={onClick:sO(c.handleItemMouseEnter,e.props.onClick)}:"axis"!==o&&(j={onMouseLeave:sO(c.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:sO(c.handleItemMouseEnter,e.props.onMouseEnter)});var S=(0,i.cloneElement)(e,dD(dD({},n.props),j));if(O){if(u>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var A="function"==typeof s.dataKey?function(e){return"function"==typeof s.dataKey?s.dataKey(e.payload):null}:"payload.".concat(s.dataKey.toString());k=Q(h,A,f),E=y&&m&&Q(m,A,f)}else k=null==h?void 0:h[u],E=y&&m&&m[u];if(w||x){var P=void 0!==e.props.activeIndex?e.props.activeIndex:u;return[(0,i.cloneElement)(e,dD(dD(dD({},n.props),j),{},{activeIndex:P})),null,null]}if(!eo()(k))return[S].concat(dT(c.renderActivePoints({item:n,activePoint:k,basePoint:E,childIndex:u,isRange:y})))}else{var k,E,M,T=(null!==(M=c.getItemByXY(c.state.activeCoordinate))&&void 0!==M?M:{graphicalItem:S}).graphicalItem,N=T.item,_=void 0===N?e:N,C=T.childIndex,D=dD(dD(dD({},n.props),j),{},{activeIndex:C});return[(0,i.cloneElement)(_,D),null,null]}}return y?[S,null,null]:[S,null]}),dI(c,"renderCustomized",function(e,t,r){return(0,i.cloneElement)(e,dD(dD({key:"recharts-customized-".concat(r)},c.props),c.state))}),dI(c,"renderMap",{CartesianGrid:{handler:dF,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:dF},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:dF},YAxis:{handler:dF},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(r=e.id)&&void 0!==r?r:G("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=D()(c.triggeredAfterMouseMove,null!==(o=e.throttleDelay)&&void 0!==o?o:1e3/60),c.state={},c}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dM(e,t)}(n,e),r=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(e=this.props.margin.left)&&void 0!==e?e:0,top:null!==(t=this.props.margin.top)&&void 0!==t?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,o=e.layout,i=eE(t,tj);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,l=dW(this.state,r,a,c),s=this.state.tooltipTicks[a].coordinate,u=(this.state.offset.top+n)/2,f="horizontal"===o?{x:s,y:u}:{y:s,x:u},p=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});p&&(f=dD(dD({},f),p.props.points[a].tooltipPosition),l=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:l,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(e){eC([eE(e.children,tj)],[eE(this.props.children,tj)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=eE(this.props.children,tj);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return l.indexOf(t)>=0?t:o}return o}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,r=t.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(e.pageX-n.left),chartY:Math.round(e.pageY-n.top)},i=r.width/t.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,l=c.xAxisMap,s=c.yAxisMap,u=this.getTooltipEventType(),f=dK(this.state,this.props.data,this.props.layout,a);if("axis"!==u&&l&&s){var p=Y(l).scale,d=Y(s).scale,h=p&&p.invert?p.invert(o.chartX):null,y=d&&d.invert?d.invert(o.chartY):null;return dD(dD({},o),{},{xValue:h,yValue:y},f)}return f?dD(dD({},o),f):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=e/r,i=t/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,l=c.angleAxisMap,s=c.radiusAxisMap;return l&&s?ul({x:o,y:i},Y(l)):null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=eE(e,tj),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),dD(dD({},em(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pI.on(pR,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pI.removeListener(pR,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===e||a.props.key===e.key||t===ej(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,n=t.top,o=t.height,i=t.width;return a().createElement("defs",null,a().createElement("clipPath",{id:e},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=dA(t,2),n=r[0],o=r[1];return dD(dD({},e),{},dI({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=dA(t,2),n=r[0],o=r[1];return dD(dD({},e),{},dI({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null===(t=this.state.xAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null===(t=this.state.yAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,l=a.item,s=void 0!==l.type.defaultProps?dD(dD({},l.type.defaultProps),l.props):l.props,u=ej(l.type);if("Bar"===u){var f=(c.data||[]).find(function(t){return n_(e,t)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===u){var p=(c.data||[]).find(function(t){return ul(e,t)});if(p)return{graphicalItem:a,payload:p}}else if(fn(a,n)||fo(a,n)||fi(a,n)){var d=function(e){var t,r,n,o=e.activeTooltipItem,i=e.graphicalItem,a=e.itemData,c=(fn(i,o)?t="trapezoids":fo(i,o)?t="sectors":fi(i,o)&&(t="points"),t),l=fn(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:fo(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:fi(i,o)?o.payload:{},s=a.filter(function(e,t){var r=li()(l,e),n=i.props[c].filter(function(e){var t;return(fn(i,o)?t=fa:fo(i,o)?t=fc:fi(i,o)&&(t=fl),t)(e,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&t===a});return a.indexOf(s[s.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:s.data}),h=void 0===s.activeIndex?d:s.activeIndex;return{graphicalItem:dD(dD({},a),{},{childIndex:h}),payload:fi(a,n)?s.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var e,t,r=this;if(!eM(this))return null;var n=this.props,o=n.children,i=n.className,c=n.width,l=n.height,s=n.style,u=n.compact,f=n.title,p=n.desc,d=e_(dP(n,dO),!1);if(u)return a().createElement(pe,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(eX,dS({},d,{width:c,height:l,title:f,desc:p}),this.renderClipPath(),eI(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,d.role=null!==(t=this.props.role)&&void 0!==t?t:"application",d.onKeyDown=function(e){r.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return a().createElement(pe,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",dS({className:(0,_.A)("recharts-wrapper",i),style:dD({position:"relative",cursor:"default",width:c,height:l},s)},h,{ref:function(e){r.container=e}}),a().createElement(eX,dS({},d,{width:c,height:l,title:f,desc:p,style:dL}),this.renderClipPath(),eI(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dR(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);dI(y,"displayName",t),dI(y,"defaultProps",dD({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),dI(y,"getDerivedStateFromProps",function(e,t){var r=e.dataKey,n=e.data,o=e.children,i=e.width,a=e.height,c=e.layout,l=e.stackOffset,s=e.margin,u=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var p=dY(e);return dD(dD(dD({},p),{},{updateId:0},h(dD(dD({props:e},p),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==t.prevDataKey||n!==t.prevData||i!==t.prevWidth||a!==t.prevHeight||c!==t.prevLayout||l!==t.prevStackOffset||!eu(s,t.prevMargin)){var d=dY(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},m=dD(dD({},dK(t,n,c)),{},{updateId:t.updateId+1}),v=dD(dD(dD({},d),y),m);return dD(dD(dD({},v),h(dD({props:e},v),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!eC(o,t.prevChildren)){var b,g,x,w,O=eE(o,s6),j=O&&null!==(b=null===(g=O.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:u,S=O&&null!==(x=null===(w=O.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:f,A=eo()(n)||j!==u||S!==f?t.updateId+1:t.updateId;return dD(dD({updateId:A},h(dD(dD({props:e},t),{},{updateId:A,dataStartIndex:j,dataEndIndex:S}),t)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),dI(y,"renderActiveDot",function(e,t,r){var n;return n=(0,i.isValidElement)(e)?(0,i.cloneElement)(e,t):ea()(e)?e(t):a().createElement(ry,t),a().createElement(eJ,{className:"recharts-active-dot",key:r},n)});var m=(0,i.forwardRef)(function(e,t){return a().createElement(y,dS({},e,{ref:t}))});return m.displayName=y.displayName,m},d0=["layout","type","stroke","connectNulls","isRange","ref"],d1=["key"];function d2(e){return(d2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d5(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function d4(){return(d4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function d3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d3(Object(r),!0).forEach(function(t){ht(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d8(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hr(n.key),n)}}function d9(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(d9=function(){return!!e})()}function d7(e){return(d7=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function he(e,t){return(he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ht(e,t,r){return(t=hr(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hr(e){var t=function(e,t){if("object"!=d2(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d2(t)?t:t+""}var hn=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=d7(t),ht(e=function(e,t){if(t&&("object"===d2(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,d9()?Reflect.construct(t,r||[],d7(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!0}),ht(e,"id",G("recharts-area-")),ht(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),ea()(t)&&t()}),ht(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),ea()(t)&&t()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&he(e,t)}(n,e),t=[{key:"renderDots",value:function(e,t,r){var o=this.props.isAnimationActive,i=this.state.isAnimationFinished;if(o&&!i)return null;var c=this.props,l=c.dot,s=c.points,u=c.dataKey,f=e_(this.props,!1),p=e_(l,!0),d=s.map(function(e,t){var r=d6(d6(d6({key:"dot-".concat(t),r:3},f),p),{},{index:t,cx:e.x,cy:e.y,dataKey:u,value:e.value,payload:e.payload,points:s});return n.renderDotItem(l,r)}),h={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(r,")"):null};return a().createElement(eJ,d4({className:"recharts-area-dots"},h),d)}},{key:"renderHorizontalRect",value:function(e){var t=this.props,r=t.baseLine,n=t.points,o=t.strokeWidth,i=n[0].x,c=n[n.length-1].x,l=e*Math.abs(i-c),s=c7()(n.map(function(e){return e.y||0}));return(K(r)&&"number"==typeof r?s=Math.max(r,s):r&&Array.isArray(r)&&r.length&&(s=Math.max(c7()(r.map(function(e){return e.y||0})),s)),K(s))?a().createElement("rect",{x:i<c?i:i-l,y:0,width:l,height:Math.floor(s+(o?parseInt("".concat(o),10):1))}):null}},{key:"renderVerticalRect",value:function(e){var t=this.props,r=t.baseLine,n=t.points,o=t.strokeWidth,i=n[0].y,c=n[n.length-1].y,l=e*Math.abs(i-c),s=c7()(n.map(function(e){return e.x||0}));return(K(r)&&"number"==typeof r?s=Math.max(r,s):r&&Array.isArray(r)&&r.length&&(s=Math.max(c7()(r.map(function(e){return e.x||0})),s)),K(s))?a().createElement("rect",{x:0,y:i<c?i:i-l,width:s+(o?parseInt("".concat(o),10):1),height:Math.floor(l)}):null}},{key:"renderClipRect",value:function(e){return"vertical"===this.props.layout?this.renderVerticalRect(e):this.renderHorizontalRect(e)}},{key:"renderAreaStatically",value:function(e,t,r,n){var o=this.props,i=o.layout,c=o.type,l=o.stroke,s=o.connectNulls,u=o.isRange,f=(o.ref,d5(o,d0));return a().createElement(eJ,{clipPath:r?"url(#clipPath-".concat(n,")"):null},a().createElement(du,d4({},e_(f,!0),{points:e,connectNulls:s,type:c,baseLine:t,layout:i,stroke:"none",className:"recharts-area-area"})),"none"!==l&&a().createElement(du,d4({},e_(this.props,!1),{className:"recharts-area-curve",layout:i,type:c,connectNulls:s,fill:"none",points:e})),"none"!==l&&u&&a().createElement(du,d4({},e_(this.props,!1),{className:"recharts-area-curve",layout:i,type:c,connectNulls:s,fill:"none",points:t})))}},{key:"renderAreaWithAnimation",value:function(e,t){var r=this,n=this.props,o=n.points,i=n.baseLine,c=n.isAnimationActive,l=n.animationBegin,s=n.animationDuration,u=n.animationEasing,f=n.animationId,p=this.state,d=p.prevPoints,h=p.prevBaseLine;return a().createElement(nA,{begin:l,duration:s,isActive:c,easing:u,from:{t:0},to:{t:1},key:"area-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var c=n.t;if(d){var l,s=d.length/o.length,u=o.map(function(e,t){var r=Math.floor(t*s);if(d[r]){var n=d[r],o=J(n.x,e.x),i=J(n.y,e.y);return d6(d6({},e),{},{x:o(c),y:i(c)})}return e});return l=K(i)&&"number"==typeof i?J(h,i)(c):eo()(i)||L()(i)?J(h,0)(c):i.map(function(e,t){var r=Math.floor(t*s);if(h[r]){var n=h[r],o=J(n.x,e.x),i=J(n.y,e.y);return d6(d6({},e),{},{x:o(c),y:i(c)})}return e}),r.renderAreaStatically(u,l,e,t)}return a().createElement(eJ,null,a().createElement("defs",null,a().createElement("clipPath",{id:"animationClipPath-".concat(t)},r.renderClipRect(c))),a().createElement(eJ,{clipPath:"url(#animationClipPath-".concat(t,")")},r.renderAreaStatically(o,i,e,t)))})}},{key:"renderArea",value:function(e,t){var r=this.props,n=r.points,o=r.baseLine,i=r.isAnimationActive,a=this.state,c=a.prevPoints,l=a.prevBaseLine,s=a.totalLength;return i&&n&&n.length&&(!c&&s>0||!li()(c,n)||!li()(l,o))?this.renderAreaWithAnimation(e,t):this.renderAreaStatically(n,o,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,n=t.dot,o=t.points,i=t.className,c=t.top,l=t.left,s=t.xAxis,u=t.yAxis,f=t.width,p=t.height,d=t.isAnimationActive,h=t.id;if(r||!o||!o.length)return null;var y=this.state.isAnimationFinished,m=1===o.length,v=(0,_.A)("recharts-area",i),b=s&&s.allowDataOverflow,g=u&&u.allowDataOverflow,x=b||g,w=eo()(h)?this.id:h,O=null!==(e=e_(n,!1))&&void 0!==e?e:{r:3,strokeWidth:2},j=O.r,S=O.strokeWidth,A=(n&&"object"===ew(n)&&"clipDot"in n?n:{}).clipDot,P=void 0===A||A,k=2*(void 0===j?3:j)+(void 0===S?2:S);return a().createElement(eJ,{className:v},b||g?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(w)},a().createElement("rect",{x:b?l:l-f/2,y:g?c:c-p/2,width:b?f:2*f,height:g?p:2*p})),!P&&a().createElement("clipPath",{id:"clipPath-dots-".concat(w)},a().createElement("rect",{x:l-k/2,y:c-k/2,width:f+k,height:p+k}))):null,m?null:this.renderArea(x,w),(n||m)&&this.renderDots(x,P,w),(!d||y)&&uz.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,curBaseLine:e.baseLine,prevPoints:t.curPoints,prevBaseLine:t.curBaseLine}:e.points!==t.curPoints||e.baseLine!==t.curBaseLine?{curPoints:e.points,curBaseLine:e.baseLine}:null}}],t&&d8(n.prototype,t),r&&d8(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function ho(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],o=0;o<e.length;o+=t){if(void 0!==r&&!0!==r(e[o]))return;n.push(e[o])}return n}function hi(e,t,r,n,o){if(e*t<e*n||e*t>e*o)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-o)<=0}function ha(e){return(ha="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hc(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ha(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ha(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ha(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hs(e,t,r){var n,o,i,a,c,l=e.tick,s=e.ticks,u=e.viewBox,f=e.minTickGap,p=e.orientation,d=e.interval,h=e.tickFormatter,y=e.unit,m=e.angle;if(!s||!s.length||!l)return[];if(K(d)||tu.isSsr)return ho(s,("number"==typeof d&&K(d)?d:0)+1);var v=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nZ(y,{fontSize:t,letterSpacing:r}):{width:0,height:0},x=function(e,n){var o,i=ea()(h)?h(e.value,n):e.value;return"width"===b?fz({width:(o=nZ(i,{fontSize:t,letterSpacing:r})).width+g.width,height:o.height+g.height},m):nZ(i,{fontSize:t,letterSpacing:r})[b]},w=s.length>=2?q(s[1].coordinate-s[0].coordinate):1,O=(n="width"===b,o=u.x,i=u.y,a=u.width,c=u.height,1===w?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===d?function(e,t,r,n,o){for(var i,a=(n||[]).slice(),c=t.start,l=t.end,s=0,u=1,f=c;u<=a.length;)if(i=function(){var t,i=null==n?void 0:n[s];if(void 0===i)return{v:ho(n,u)};var a=s,p=function(){return void 0===t&&(t=r(i,a)),t},d=i.coordinate,h=0===s||hi(e,d,p,f,l);h||(s=0,f=c,u+=1),h&&(f=d+e*(p()/2+o),s+=u)}())return i.v;return[]}(w,O,x,s,f):("preserveStart"===d||"preserveStartEnd"===d?function(e,t,r,n,o,i){var a=(n||[]).slice(),c=a.length,l=t.start,s=t.end;if(i){var u=n[c-1],f=r(u,c-1),p=e*(u.coordinate+e*f/2-s);a[c-1]=u=hl(hl({},u),{},{tickCoord:p>0?u.coordinate-p*e:u.coordinate}),hi(e,u.tickCoord,function(){return f},l,s)&&(s=u.tickCoord-e*(f/2+o),a[c-1]=hl(hl({},u),{},{isShow:!0}))}for(var d=i?c-1:c,h=function(t){var n,i=a[t],c=function(){return void 0===n&&(n=r(i,t)),n};if(0===t){var u=e*(i.coordinate-e*c()/2-l);a[t]=i=hl(hl({},i),{},{tickCoord:u<0?i.coordinate-u*e:i.coordinate})}else a[t]=i=hl(hl({},i),{},{tickCoord:i.coordinate});hi(e,i.tickCoord,c,l,s)&&(l=i.tickCoord+e*(c()/2+o),a[t]=hl(hl({},i),{},{isShow:!0}))},y=0;y<d;y++)h(y);return a}(w,O,x,s,f,"preserveStartEnd"===d):function(e,t,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=t.start,l=t.end,s=function(t){var n,s=i[t],u=function(){return void 0===n&&(n=r(s,t)),n};if(t===a-1){var f=e*(s.coordinate+e*u()/2-l);i[t]=s=hl(hl({},s),{},{tickCoord:f>0?s.coordinate-f*e:s.coordinate})}else i[t]=s=hl(hl({},s),{},{tickCoord:s.coordinate});hi(e,s.tickCoord,u,c,l)&&(l=s.tickCoord-e*(u()/2+o),i[t]=hl(hl({},s),{},{isShow:!0}))},u=a-1;u>=0;u--)s(u);return i}(w,O,x,s,f)).filter(function(e){return e.isShow})}ht(hn,"displayName","Area"),ht(hn,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!tu.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),ht(hn,"getBaseValue",function(e,t,r,n){var o=e.layout,i=e.baseValue,a=t.props.baseValue,c=null!=a?a:i;if(K(c)&&"number"==typeof c)return c;var l="horizontal"===o?n:r,s=l.scale.domain();if("number"===l.type){var u=Math.max(s[0],s[1]),f=Math.min(s[0],s[1]);return"dataMin"===c?f:"dataMax"===c?u:u<0?u:Math.max(Math.min(s[0],s[1]),0)}return"dataMin"===c?s[0]:"dataMax"===c?s[1]:s[0]}),ht(hn,"getComposedData",function(e){var t,r=e.props,n=e.item,o=e.xAxis,i=e.yAxis,a=e.xAxisTicks,c=e.yAxisTicks,l=e.bandSize,s=e.dataKey,u=e.stackedData,f=e.dataStartIndex,p=e.displayedData,d=e.offset,h=r.layout,y=u&&u.length,m=hn.getBaseValue(r,n,o,i),v="horizontal"===h,b=!1,g=p.map(function(e,t){y?r=u[f+t]:Array.isArray(r=sl(e,s))?b=!0:r=[m,r];var r,n=null==r[1]||y&&null==sl(e,s);return v?{x:sN({axis:o,ticks:a,bandSize:l,entry:e,index:t}),y:n?null:i.scale(r[1]),value:r,payload:e}:{x:n?null:o.scale(r[1]),y:sN({axis:i,ticks:c,bandSize:l,entry:e,index:t}),value:r,payload:e}});return t=y||b?g.map(function(e){var t=Array.isArray(e.value)?e.value[0]:null;return v?{x:e.x,y:null!=t&&null!=e.y?i.scale(t):null}:{x:null!=t?o.scale(t):null,y:e.y}}):v?i.scale(m):o.scale(m),d6({points:g,baseLine:t,layout:h,isRange:b},d)}),ht(hn,"renderDotItem",function(e,t){var r;if(a().isValidElement(e))r=a().cloneElement(e,t);else if(ea()(e))r=e(t);else{var n=(0,_.A)("recharts-area-dot","boolean"!=typeof e?e.className:""),o=t.key,i=d5(t,d1);r=a().createElement(ry,d4({},i,{key:o,className:n}))}return r});var hu=["viewBox"],hf=["viewBox"],hp=["ticks"];function hd(e){return(hd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hh(){return(hh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hy(Object(r),!0).forEach(function(t){hO(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hy(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hv(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function hb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hj(n.key),n)}}function hg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hg=function(){return!!e})()}function hx(e){return(hx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hw(e,t){return(hw=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hO(e,t,r){return(t=hj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hj(e){var t=function(e,t){if("object"!=hd(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hd(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hd(t)?t:t+""}var hS=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=hx(r),(t=function(e,t){if(t&&("object"===hd(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,hg()?Reflect.construct(r,o||[],hx(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hw(e,t)}(n,e),t=[{key:"shouldComponentUpdate",value:function(e,t){var r=e.viewBox,n=hv(e,hu),o=this.props,i=o.viewBox,a=hv(o,hf);return!eu(r,i)||!eu(n,a)||!eu(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,r,n,o,i,a,c=this.props,l=c.x,s=c.y,u=c.width,f=c.height,p=c.orientation,d=c.tickSize,h=c.mirror,y=c.tickMargin,m=h?-1:1,v=e.tickSize||d,b=K(e.tickCoord)?e.tickCoord:e.coordinate;switch(p){case"top":t=r=e.coordinate,a=(n=(o=s+ +!h*f)-m*v)-m*y,i=b;break;case"left":n=o=e.coordinate,i=(t=(r=l+ +!h*u)-m*v)-m*y,a=b;break;case"right":n=o=e.coordinate,i=(t=(r=l+ +h*u)+m*v)+m*y,a=b;break;default:t=r=e.coordinate,a=(n=(o=s+ +h*f)+m*v)+m*y,i=b}return{line:{x1:t,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,r=t.orientation,n=t.mirror;switch(r){case"left":e=n?"start":"end";break;case"right":e=n?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,r=e.mirror,n="end";switch(t){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.orientation,c=e.mirror,l=e.axisLine,s=hm(hm(hm({},e_(this.props,!1)),e_(l,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var u=+("top"===i&&!c||"bottom"===i&&c);s=hm(hm({},s),{},{x1:t,y1:r+u*o,x2:t+n,y2:r+u*o})}else{var f=+("left"===i&&!c||"right"===i&&c);s=hm(hm({},s),{},{x1:t+f*n,y1:r,x2:t+f*n,y2:r+o})}return a().createElement("line",hh({},s,{className:(0,_.A)("recharts-cartesian-axis-line",F()(l,"className"))}))}},{key:"renderTicks",value:function(e,t,r){var o=this,i=this.props,c=i.tickLine,l=i.stroke,s=i.tick,u=i.tickFormatter,f=i.unit,p=hs(hm(hm({},this.props),{},{ticks:e}),t,r),d=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),y=e_(this.props,!1),m=e_(s,!1),v=hm(hm({},y),{},{fill:"none"},e_(c,!1)),b=p.map(function(e,t){var r=o.getTickLineCoord(e),i=r.line,b=r.tick,g=hm(hm(hm(hm({textAnchor:d,verticalAnchor:h},y),{},{stroke:"none",fill:l},m),b),{},{index:t,payload:e,visibleTicksCount:p.length,tickFormatter:u});return a().createElement(eJ,hh({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},ev(o.props,e,t)),c&&a().createElement("line",hh({},v,i,{className:(0,_.A)("recharts-cartesian-axis-tick-line",F()(c,"className"))})),s&&n.renderTickItem(s,g,"".concat(ea()(u)?u(e.value,t):e.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var e=this,t=this.props,r=t.axisLine,n=t.width,o=t.height,i=t.ticksGenerator,c=t.className;if(t.hide)return null;var l=this.props,s=l.ticks,u=hv(l,hp),f=s;return(ea()(i)&&(f=i(s&&s.length>0?this.props:u)),n<=0||o<=0||!f||!f.length)?null:a().createElement(eJ,{className:(0,_.A)("recharts-cartesian-axis",c),ref:function(t){e.layerReference=t}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),ux.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return a().isValidElement(e)?a().cloneElement(e,t):ea()(e)?e(t):a().createElement(oh,hh({},t,{className:"recharts-cartesian-axis-tick-value"}),r)}}],t&&hb(n.prototype,t),r&&hb(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);function hA(e){return(hA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}hO(hS,"displayName","CartesianAxis"),hO(hS,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function hP(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hP=function(){return!!e})()}function hk(e){return(hk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hE(e,t){return(hE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hM(e,t,r){return(t=hT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hT(e){var t=function(e,t){if("object"!=hA(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hA(t)?t:t+""}function hN(){return(hN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function h_(e){var t=e.xAxisId,r=po(),n=pi(),o=pt(t);return null==o?null:a().createElement(hS,hN({},o,{className:(0,_.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return sx(e,!0)}}))}var hC=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=hk(e),function(e,t){if(t&&("object"===hA(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,hP()?Reflect.construct(e,t||[],hk(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hE(e,t)}(r,e),t=[{key:"render",value:function(){return a().createElement(h_,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hT(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function hD(e){return(hD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}hM(hC,"displayName","XAxis"),hM(hC,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function hI(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hI=function(){return!!e})()}function hR(e){return(hR=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hB(e,t){return(hB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hL(e,t,r){return(t=hz(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hz(e){var t=function(e,t){if("object"!=hD(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hD(t)?t:t+""}function hF(){return(hF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var h$=function(e){var t=e.yAxisId,r=po(),n=pi(),o=pn(t);return null==o?null:a().createElement(hS,hF({},o,{className:(0,_.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return sx(e,!0)}}))},hU=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=hR(e),function(e,t){if(t&&("object"===hD(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,hI()?Reflect.construct(e,t||[],hR(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hB(e,t)}(r,e),t=[{key:"render",value:function(){return a().createElement(h$,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hz(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);hL(hU,"displayName","YAxis"),hL(hU,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var hq=dQ({chartName:"AreaChart",GraphicalChild:hn,axisComponents:[{axisType:"xAxis",AxisComp:hC},{axisType:"yAxis",AxisComp:hU}],formatAxisMap:fI}),hW=["x1","y1","x2","y2","key"],hK=["offset"];function hV(e){return(hV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hH(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hV(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hV(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hX(){return(hX=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hY(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var hZ=function(e){var t=e.fill;if(!t||"none"===t)return null;var r=e.fillOpacity,n=e.x,o=e.y,i=e.width,c=e.height,l=e.ry;return a().createElement("rect",{x:n,y:o,ry:l,width:i,height:c,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function hJ(e,t){var r;if(a().isValidElement(e))r=a().cloneElement(e,t);else if(ea()(e))r=e(t);else{var n=t.x1,o=t.y1,i=t.x2,c=t.y2,l=t.key,s=e_(hY(t,hW),!1),u=(s.offset,hY(s,hK));r=a().createElement("line",hX({},u,{x1:n,y1:o,x2:i,y2:c,fill:"none",key:l}))}return r}function hQ(e){var t=e.x,r=e.width,n=e.horizontal,o=void 0===n||n,i=e.horizontalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return hJ(o,hG(hG({},e),{},{x1:t,y1:n,x2:t+r,y2:n,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function h0(e){var t=e.y,r=e.height,n=e.vertical,o=void 0===n||n,i=e.verticalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return hJ(o,hG(hG({},e),{},{x1:n,y1:t,x2:n,y2:t+r,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function h1(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,o=e.y,i=e.width,c=e.height,l=e.horizontalPoints,s=e.horizontal;if(!(void 0===s||s)||!t||!t.length)return null;var u=l.map(function(e){return Math.round(e+o-o)}).sort(function(e,t){return e-t});o!==u[0]&&u.unshift(0);var f=u.map(function(e,l){var s=u[l+1]?u[l+1]-e:o+c-e;if(s<=0)return null;var f=l%t.length;return a().createElement("rect",{key:"react-".concat(l),y:e,x:n,height:s,width:i,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function h2(e){var t=e.vertical,r=e.verticalFill,n=e.fillOpacity,o=e.x,i=e.y,c=e.width,l=e.height,s=e.verticalPoints;if(!(void 0===t||t)||!r||!r.length)return null;var u=s.map(function(e){return Math.round(e+o-o)}).sort(function(e,t){return e-t});o!==u[0]&&u.unshift(0);var f=u.map(function(e,t){var s=u[t+1]?u[t+1]-e:o+c-e;if(s<=0)return null;var f=t%r.length;return a().createElement("rect",{key:"react-".concat(t),x:e,y:i,width:s,height:l,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var h5=function(e,t){var r=e.xAxis,n=e.width,o=e.height,i=e.offset;return sg(hs(hG(hG(hG({},hS.defaultProps),r),{},{ticks:sx(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,t)},h4=function(e,t){var r=e.yAxis,n=e.width,o=e.height,i=e.offset;return sg(hs(hG(hG(hG({},hS.defaultProps),r),{},{ticks:sx(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,t)},h3={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function h6(e){var t,r,n,o,c,l,s=po(),u=pi(),f=(0,i.useContext)(f6),p=hG(hG({},e),{},{stroke:null!==(t=e.stroke)&&void 0!==t?t:h3.stroke,fill:null!==(r=e.fill)&&void 0!==r?r:h3.fill,horizontal:null!==(n=e.horizontal)&&void 0!==n?n:h3.horizontal,horizontalFill:null!==(o=e.horizontalFill)&&void 0!==o?o:h3.horizontalFill,vertical:null!==(c=e.vertical)&&void 0!==c?c:h3.vertical,verticalFill:null!==(l=e.verticalFill)&&void 0!==l?l:h3.verticalFill,x:K(e.x)?e.x:f.left,y:K(e.y)?e.y:f.top,width:K(e.width)?e.width:f.width,height:K(e.height)?e.height:f.height}),d=p.x,h=p.y,y=p.width,m=p.height,v=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=Y((0,i.useContext)(f5)),w=pr();if(!K(y)||y<=0||!K(m)||m<=0||!K(d)||d!==+d||!K(h)||h!==+h)return null;var O=p.verticalCoordinatesGenerator||h5,j=p.horizontalCoordinatesGenerator||h4,S=p.horizontalPoints,A=p.verticalPoints;if((!S||!S.length)&&ea()(j)){var P=b&&b.length,k=j({yAxis:w?hG(hG({},w),{},{ticks:P?b:w.ticks}):void 0,width:s,height:u,offset:f},!!P||v);er(Array.isArray(k),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(hV(k),"]")),Array.isArray(k)&&(S=k)}if((!A||!A.length)&&ea()(O)){var E=g&&g.length,M=O({xAxis:x?hG(hG({},x),{},{ticks:E?g:x.ticks}):void 0,width:s,height:u,offset:f},!!E||v);er(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(hV(M),"]")),Array.isArray(M)&&(A=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(hZ,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(hQ,hX({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:w})),a().createElement(h0,hX({},p,{offset:f,verticalPoints:A,xAxis:x,yAxis:w})),a().createElement(h1,hX({},p,{horizontalPoints:S})),a().createElement(h2,hX({},p,{verticalPoints:A})))}h6.displayName="CartesianGrid";var h8=dQ({chartName:"BarChart",GraphicalChild:fE,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:hC},{axisType:"yAxis",AxisComp:hU}],formatAxisMap:fI});function h9(e){return(h9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h7(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(h7=function(){return!!e})()}function ye(e){return(ye=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yt(e,t){return(yt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yr(e,t,r){return(t=yn(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yn(e){var t=function(e,t){if("object"!=h9(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=h9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==h9(t)?t:t+""}var yo=function(e){function t(){var e,r;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,t),e=t,r=arguments,e=ye(e),function(e,t){if(t&&("object"===h9(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,h7()?Reflect.construct(e,r||[],ye(this).constructor):e.apply(this,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yt(e,t)}(t,e),function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yn(n.key),n)}}(t.prototype,[{key:"render",value:function(){return null}}]),Object.defineProperty(t,"prototype",{writable:!1}),t}(a().Component);yr(yo,"displayName","ZAxis"),yr(yo,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"});var yi=["option","isActive"];function ya(){return(ya=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yc(e){var t=e.option,r=e.isActive,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,yi);return"string"==typeof t?a().createElement(fr,ya({option:a().createElement(t2,ya({type:t},n)),isActive:r,shapeType:"symbols"},n)):a().createElement(fr,ya({option:t,isActive:r,shapeType:"symbols"},n))}function yl(e){return(yl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ys(){return(ys=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yu(Object(r),!0).forEach(function(t){ym(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yu(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yv(n.key),n)}}function yd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yd=function(){return!!e})()}function yh(e){return(yh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yy(e,t){return(yy=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ym(e,t,r){return(t=yv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yv(e){var t=function(e,t){if("object"!=yl(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yl(t)?t:t+""}var yb=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=yh(t),ym(e=function(e,t){if(t&&("object"===yl(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,yd()?Reflect.construct(t,r||[],yh(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!1}),ym(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0})}),ym(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1})}),ym(e,"id",G("recharts-scatter-")),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yy(e,t)}(n,e),t=[{key:"renderSymbolsStatically",value:function(e){var t=this,r=this.props,n=r.shape,o=r.activeShape,i=r.activeIndex,c=e_(this.props,!1);return e.map(function(e,r){var l=i===r,s=yf(yf({},c),e);return a().createElement(eJ,ys({className:"recharts-scatter-symbol",key:"symbol-".concat(null==e?void 0:e.cx,"-").concat(null==e?void 0:e.cy,"-").concat(null==e?void 0:e.size,"-").concat(r)},ev(t.props,e,r),{role:"img"}),a().createElement(yc,ys({option:l?o:n,isActive:l,key:"symbol-".concat(r)},s)))})}},{key:"renderSymbolsWithAnimation",value:function(){var e=this,t=this.props,r=t.points,n=t.isAnimationActive,o=t.animationBegin,i=t.animationDuration,c=t.animationEasing,l=t.animationId,s=this.state.prevPoints;return a().createElement(nA,{begin:o,duration:i,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(t){var n=t.t,o=r.map(function(e,t){var r=s&&s[t];if(r){var o=J(r.cx,e.cx),i=J(r.cy,e.cy),a=J(r.size,e.size);return yf(yf({},e),{},{cx:o(n),cy:i(n),size:a(n)})}var c=J(0,e.size);return yf(yf({},e),{},{size:c(n)})});return a().createElement(eJ,null,e.renderSymbolsStatically(o))})}},{key:"renderSymbols",value:function(){var e=this.props,t=e.points,r=e.isAnimationActive,n=this.state.prevPoints;return r&&t&&t.length&&(!n||!li()(n,t))?this.renderSymbolsWithAnimation():this.renderSymbolsStatically(t)}},{key:"renderErrorBar",value:function(){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,t=e.points,r=e.xAxis,n=e.yAxis,o=ek(e.children,l8);return o?o.map(function(e,o){var i=e.props,c=i.direction,l=i.dataKey;return a().cloneElement(e,{key:"".concat(c,"-").concat(l,"-").concat(t[o]),data:t,xAxis:r,yAxis:n,layout:"x"===c?"vertical":"horizontal",dataPointFormatter:function(e,t){return{x:e.cx,y:e.cy,value:"x"===c?+e.node.x:+e.node.y,errorVal:sl(e,t)}}})}):null}},{key:"renderLine",value:function(){var e,t,r=this.props,n=r.points,o=r.line,i=r.lineType,c=r.lineJointType,l=e_(this.props,!1),s=e_(o,!1);if("joint"===i)e=n.map(function(e){return{x:e.cx,y:e.cy}});else if("fitting"===i){var u=ee(n),f=u.xmin,p=u.xmax,d=u.a,h=u.b,y=function(e){return d*e+h};e=[{x:f,y:y(f)},{x:p,y:y(p)}]}var m=yf(yf(yf({},l),{},{fill:"none",stroke:l&&l.fill},s),{},{points:e});return t=a().isValidElement(o)?a().cloneElement(o,m):ea()(o)?o(m):a().createElement(du,ys({},m,{type:c})),a().createElement(eJ,{className:"recharts-scatter-line",key:"recharts-scatter-line"},t)}},{key:"render",value:function(){var e=this.props,t=e.hide,r=e.points,n=e.line,o=e.className,i=e.xAxis,c=e.yAxis,l=e.left,s=e.top,u=e.width,f=e.height,p=e.id,d=e.isAnimationActive;if(t||!r||!r.length)return null;var h=this.state.isAnimationFinished,y=(0,_.A)("recharts-scatter",o),m=i&&i.allowDataOverflow,v=c&&c.allowDataOverflow,b=eo()(p)?this.id:p;return a().createElement(eJ,{className:y,clipPath:m||v?"url(#clipPath-".concat(b,")"):null},m||v?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(b)},a().createElement("rect",{x:m?l:l-u/2,y:v?s:s-f/2,width:m?u:2*u,height:v?f:2*f}))):null,n&&this.renderLine(),this.renderErrorBar(),a().createElement(eJ,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!d||h)&&uz.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}}],t&&yp(n.prototype,t),r&&yp(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);ym(yb,"displayName","Scatter"),ym(yb,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!tu.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"}),ym(yb,"getComposedData",function(e){var t=e.xAxis,r=e.yAxis,n=e.zAxis,o=e.item,i=e.displayedData,a=e.xAxisTicks,c=e.yAxisTicks,l=e.offset,s=o.props.tooltipType,u=ek(o.props.children,uk),f=eo()(t.dataKey)?o.props.dataKey:t.dataKey,p=eo()(r.dataKey)?o.props.dataKey:r.dataKey,d=n&&n.dataKey,h=n?n.range:yo.defaultProps.range,y=h&&h[0],m=t.scale.bandwidth?t.scale.bandwidth():0,v=r.scale.bandwidth?r.scale.bandwidth():0,b=i.map(function(e,i){var l=sl(e,f),h=sl(e,p),b=!eo()(d)&&sl(e,d)||"-",g=[{name:eo()(t.dataKey)?o.props.name:t.name||t.dataKey,unit:t.unit||"",value:l,payload:e,dataKey:f,type:s},{name:eo()(r.dataKey)?o.props.name:r.name||r.dataKey,unit:r.unit||"",value:h,payload:e,dataKey:p,type:s}];"-"!==b&&g.push({name:n.name||n.dataKey,unit:n.unit||"",value:b,payload:e,dataKey:d,type:s});var x=sN({axis:t,ticks:a,bandSize:m,entry:e,index:i,dataKey:f}),w=sN({axis:r,ticks:c,bandSize:v,entry:e,index:i,dataKey:p}),O="-"!==b?n.scale(b):y,j=Math.sqrt(Math.max(O,0)/Math.PI);return yf(yf({},e),{},{cx:x,cy:w,x:x-j,y:w-j,xAxis:t,yAxis:r,zAxis:n,width:2*j,height:2*j,size:O,node:{x:l,y:h,z:b},tooltipPayload:g,tooltipPosition:{x:x,y:w},payload:e},u&&u[i]&&u[i].props)});return yf({points:b},l)});var yg=dQ({chartName:"ScatterChart",GraphicalChild:yb,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:hC},{axisType:"yAxis",AxisComp:hU},{axisType:"zAxis",AxisComp:yo}],formatAxisMap:fI}),yx=["points","className","baseLinePoints","connectNulls"];function yw(){return(yw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yO(e){return function(e){if(Array.isArray(e))return yj(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return yj(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yj(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yj(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var yS=function(e){return e&&e.x===+e.x&&e.y===+e.y},yA=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){yS(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),yS(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},yP=function(e,t){var r=yA(e);t&&(r=[r.reduce(function(e,t){return[].concat(yO(e),yO(t))},[])]);var n=r.map(function(e){return e.reduce(function(e,t,r){return"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},yk=function(e,t,r){var n=yP(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(yP(t.reverse(),r).slice(1))},yE=function(e){var t=e.points,r=e.className,n=e.baseLinePoints,o=e.connectNulls,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,yx);if(!t||!t.length)return null;var c=(0,_.A)("recharts-polygon",r);if(n&&n.length){var l=i.stroke&&"none"!==i.stroke,s=yk(t,n,o);return a().createElement("g",{className:c},a().createElement("path",yw({},e_(i,!0),{fill:"Z"===s.slice(-1)?i.fill:"none",stroke:"none",d:s})),l?a().createElement("path",yw({},e_(i,!0),{fill:"none",d:yP(t,o)})):null,l?a().createElement("path",yw({},e_(i,!0),{fill:"none",d:yP(n,o)})):null)}var u=yP(t,o);return a().createElement("path",yw({},e_(i,!0),{fill:"Z"===u.slice(-1)?i.fill:"none",className:c,d:u}))};function yM(e){return(yM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yT(){return(yT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yN(Object(r),!0).forEach(function(t){yB(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yL(n.key),n)}}function yD(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yD=function(){return!!e})()}function yI(e){return(yI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yR(e,t){return(yR=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yB(e,t,r){return(t=yL(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yL(e){var t=function(e,t){if("object"!=yM(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yM(t)?t:t+""}var yz=Math.PI/180,yF=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=yI(e),function(e,t){if(t&&("object"===yM(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,yD()?Reflect.construct(e,t||[],yI(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yR(e,t)}(n,e),t=[{key:"getTickLineCoord",value:function(e){var t=this.props,r=t.cx,n=t.cy,o=t.radius,i=t.orientation,a=t.tickSize,c=un(r,n,o,e.coordinate),l=un(r,n,o+("inner"===i?-1:1)*(a||8),e.coordinate);return{x1:c.x,y1:c.y,x2:l.x,y2:l.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,r=Math.cos(-e.coordinate*yz);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.radius,o=e.axisLine,i=e.axisLineType,c=y_(y_({},e_(this.props,!1)),{},{fill:"none"},e_(o,!1));if("circle"===i)return a().createElement(ry,yT({className:"recharts-polar-angle-axis-line"},c,{cx:t,cy:r,r:n}));var l=this.props.ticks.map(function(e){return un(t,r,n,e.coordinate)});return a().createElement(yE,yT({className:"recharts-polar-angle-axis-line"},c,{points:l}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.tickLine,c=t.tickFormatter,l=t.stroke,s=e_(this.props,!1),u=e_(o,!1),f=y_(y_({},s),{},{fill:"none"},e_(i,!1)),p=r.map(function(t,r){var p=e.getTickLineCoord(t),d=y_(y_(y_({textAnchor:e.getTickTextAnchor(t)},s),{},{stroke:"none",fill:l},u),{},{index:r,payload:t,x:p.x2,y:p.y2});return a().createElement(eJ,yT({className:(0,_.A)("recharts-polar-angle-axis-tick",us(o)),key:"tick-".concat(t.coordinate)},ev(e.props,t,r)),i&&a().createElement("line",yT({className:"recharts-polar-angle-axis-tick-line"},f,p)),o&&n.renderTickItem(o,d,c?c(t.value,r):t.value))});return a().createElement(eJ,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.radius,n=e.axisLine;return!(r<=0)&&t&&t.length?a().createElement(eJ,{className:(0,_.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return a().isValidElement(e)?a().cloneElement(e,t):ea()(e)?e(t):a().createElement(oh,yT({},t,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],t&&yC(n.prototype,t),r&&yC(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);yB(yF,"displayName","PolarAngleAxis"),yB(yF,"axisType","angleAxis"),yB(yF,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var y$=r(57088),yU=r.n(y$),yq=r(10034),yW=r.n(yq),yK=["cx","cy","angle","ticks","axisLine"],yV=["ticks","tick","angle","tickFormatter","stroke"];function yH(e){return(yH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yG(){return(yG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yX(Object(r),!0).forEach(function(t){y2(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yZ(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function yJ(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,y5(n.key),n)}}function yQ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yQ=function(){return!!e})()}function y0(e){return(y0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function y1(e,t){return(y1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function y2(e,t,r){return(t=y5(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y5(e){var t=function(e,t){if("object"!=yH(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yH(t)?t:t+""}var y4=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=y0(e),function(e,t){if(t&&("object"===yH(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,yQ()?Reflect.construct(e,t||[],y0(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&y1(e,t)}(n,e),t=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,r=this.props,n=r.angle;return un(r.cx,r.cy,t,n)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=yU()(o,function(e){return e.coordinate||0});return{cx:t,cy:r,startAngle:n,endAngle:n,innerRadius:yW()(o,function(e){return e.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=e.axisLine,c=yZ(e,yK),l=o.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),s=un(t,r,l[0],n),u=un(t,r,l[1],n),f=yY(yY(yY({},e_(c,!1)),{},{fill:"none"},e_(i,!1)),{},{x1:s.x,y1:s.y,x2:u.x,y2:u.y});return a().createElement("line",yG({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.angle,c=t.tickFormatter,l=t.stroke,s=yZ(t,yV),u=this.getTickTextAnchor(),f=e_(s,!1),p=e_(o,!1),d=r.map(function(t,r){var s=e.getTickValueCoord(t),d=yY(yY(yY(yY({textAnchor:u,transform:"rotate(".concat(90-i,", ").concat(s.x,", ").concat(s.y,")")},f),{},{stroke:"none",fill:l},p),{},{index:r},s),{},{payload:t});return a().createElement(eJ,yG({className:(0,_.A)("recharts-polar-radius-axis-tick",us(o)),key:"tick-".concat(t.coordinate)},ev(e.props,t,r)),n.renderTickItem(o,d,c?c(t.value,r):t.value))});return a().createElement(eJ,{className:"recharts-polar-radius-axis-ticks"},d)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.axisLine,n=e.tick;return t&&t.length?a().createElement(eJ,{className:(0,_.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),ux.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return a().isValidElement(e)?a().cloneElement(e,t):ea()(e)?e(t):a().createElement(oh,yG({},t,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],t&&yJ(n.prototype,t),r&&yJ(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function y3(e){return(y3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y6(){return(y6=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function y8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y9(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y8(Object(r),!0).forEach(function(t){mn(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y7(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mo(n.key),n)}}function me(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(me=function(){return!!e})()}function mt(e){return(mt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mr(e,t){return(mr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mn(e,t,r){return(t=mo(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mo(e){var t=function(e,t){if("object"!=y3(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=y3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==y3(t)?t:t+""}y2(y4,"displayName","PolarRadiusAxis"),y2(y4,"axisType","radiusAxis"),y2(y4,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var mi=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=mt(r),mn(t=function(e,t){if(t&&("object"===y3(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,me()?Reflect.construct(r,o||[],mt(this).constructor):r.apply(this,o)),"pieRef",null),mn(t,"sectorRefs",[]),mn(t,"id",G("recharts-pie-")),mn(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),ea()(e)&&e()}),mn(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),ea()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mr(e,t)}(n,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,o=t.labelLine,i=t.dataKey,c=t.valueKey,l=e_(this.props,!1),s=e_(r,!1),u=e_(o,!1),f=r&&r.offsetRadius||20,p=e.map(function(e,t){var p=(e.startAngle+e.endAngle)/2,d=un(e.cx,e.cy,e.outerRadius+f,p),h=y9(y9(y9(y9({},l),e),{},{stroke:"none"},s),{},{index:t,textAnchor:n.getTextAnchor(d.x,e.cx)},d),y=y9(y9(y9(y9({},l),e),{},{fill:"none",stroke:e.fill},u),{},{index:t,points:[un(e.cx,e.cy,e.outerRadius,p),d]}),m=i;return eo()(i)&&eo()(c)?m="value":eo()(i)&&(m=c),a().createElement(eJ,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&n.renderLabelLineItem(o,y,"line"),n.renderLabelItem(r,h,sl(e,m)))});return a().createElement(eJ,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return e.map(function(r,c){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var l=t.isActiveIndex(c),s=i&&t.hasActiveIndex()?i:null,u=y9(y9({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return a().createElement(eJ,y6({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},ev(t.props,r,c),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),a().createElement(fr,y6({option:l?n:s,isActive:l,shapeType:"sector"},u)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,n=t.isAnimationActive,o=t.animationBegin,i=t.animationDuration,c=t.animationEasing,l=t.animationId,s=this.state,u=s.prevSectors,f=s.prevIsAnimationActive;return a().createElement(nA,{begin:o,duration:i,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(l,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var n=t.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=u&&u[t],a=t>0?F()(e,"paddingAngle",0):0;if(r){var c=J(r.endAngle-r.startAngle,e.endAngle-e.startAngle),l=y9(y9({},e),{},{startAngle:i+a,endAngle:i+c(n)+a});o.push(l),i=l.endAngle}else{var s=J(0,e.endAngle-e.startAngle)(n),f=y9(y9({},e),{},{startAngle:i+a,endAngle:i+s+a});o.push(f),i=f.endAngle}}),a().createElement(eJ,null,e.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var n=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,n=this.state.prevSectors;return r&&t&&t.length&&(!n||!li()(n,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,n=t.sectors,o=t.className,i=t.label,c=t.cx,l=t.cy,s=t.innerRadius,u=t.outerRadius,f=t.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!K(c)||!K(l)||!K(s)||!K(u))return null;var d=(0,_.A)("recharts-pie",o);return a().createElement(eJ,{tabIndex:this.props.rootTabIndex,className:d,ref:function(t){e.pieRef=t}},this.renderSectors(),i&&this.renderLabels(n),ux.renderCallByParent(this.props,null,!1),(!f||p)&&uz.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);if(ea()(e))return e(t);var n=(0,_.A)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return a().createElement(du,y6({},t,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);var n=r;if(ea()(e)&&(n=e(t),a().isValidElement(n)))return n;var o=(0,_.A)("recharts-pie-label-text","boolean"==typeof e||ea()(e)?"":e.className);return a().createElement(oh,y6({},t,{alignmentBaseline:"middle",className:o}),n)}}],t&&y7(n.prototype,t),r&&y7(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);mn(mi,"displayName","Pie"),mn(mi,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!tu.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),mn(mi,"parseDeltaAngle",function(e,t){return q(t-e)*Math.min(Math.abs(t-e),360)}),mn(mi,"getRealPieData",function(e){var t=e.data,r=e.children,n=e_(e,!1),o=ek(r,uk);return t&&t.length?t.map(function(e,t){return y9(y9(y9({payload:e},n),e),o&&o[t]&&o[t].props)}):o&&o.length?o.map(function(e){return y9(y9({},n),e.props)}):[]}),mn(mi,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,o=t.width,i=t.height,a=uo(o,i);return{cx:n+X(e.cx,o,o/2),cy:r+X(e.cy,i,i/2),innerRadius:X(e.innerRadius,a,0),outerRadius:X(e.outerRadius,a,.8*a),maxRadius:e.maxRadius||Math.sqrt(o*o+i*i)/2}}),mn(mi,"getComposedData",function(e){var t,r,n=e.item,o=e.offset,i=void 0!==n.type.defaultProps?y9(y9({},n.type.defaultProps),n.props):n.props,a=mi.getRealPieData(i);if(!a||!a.length)return null;var c=i.cornerRadius,l=i.startAngle,s=i.endAngle,u=i.paddingAngle,f=i.dataKey,p=i.nameKey,d=i.valueKey,h=i.tooltipType,y=Math.abs(i.minAngle),m=mi.parseCoordinateOfPie(i,o),v=mi.parseDeltaAngle(l,s),b=Math.abs(v),g=f;eo()(f)&&eo()(d)?(er(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):eo()(f)&&(er(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=d);var x=a.filter(function(e){return 0!==sl(e,g,0)}).length,w=b-x*y-(b>=360?x:x-1)*u,O=a.reduce(function(e,t){var r=sl(t,g,0);return e+(K(r)?r:0)},0);return O>0&&(t=a.map(function(e,t){var n,o=sl(e,g,0),i=sl(e,p,t),a=(K(o)?o:0)/O,s=(n=t?r.endAngle+q(v)*u*+(0!==o):l)+q(v)*((0!==o?y:0)+a*w),f=(n+s)/2,d=(m.innerRadius+m.outerRadius)/2,b=[{name:i,value:o,payload:e,dataKey:g,type:h}],x=un(m.cx,m.cy,d,f);return r=y9(y9(y9({percent:a,cornerRadius:c,name:i,tooltipPayload:b,midAngle:f,middleRadius:d,tooltipPosition:x},e),m),{},{value:sl(e,g),startAngle:n,endAngle:s,payload:e,paddingAngle:q(v)*u})})),y9(y9({},m),{},{sectors:t,data:a})});var ma=dQ({chartName:"PieChart",GraphicalChild:mi,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:yF},{axisType:"radiusAxis",AxisComp:y4}],formatAxisMap:function(e,t,r,n,o){var i=e.width,a=e.height,c=e.startAngle,l=e.endAngle,s=X(e.cx,i,i/2),u=X(e.cy,a,a/2),f=uo(i,a,r),p=X(e.innerRadius,f,0),d=X(e.outerRadius,f,.8*f);return Object.keys(t).reduce(function(e,r){var i,a=t[r],f=a.domain,h=a.reversed;if(eo()(a.range))"angleAxis"===n?i=[c,l]:"radiusAxis"===n&&(i=[p,d]),h&&(i=[i[1],i[0]]);else{var y,m=function(e){if(Array.isArray(e))return e}(y=i=a.range)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{i=(r=r.call(e)).next;for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(y,2)||function(e,t){if(e){if("string"==typeof e)return ut(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ut(e,t)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=m[0],l=m[1]}var v=sj(a,o),b=v.realScaleType,g=v.scale;g.domain(f).range(i),sS(g);var x=sT(g,s7(s7({},a),{},{realScaleType:b})),w=s7(s7(s7({},a),x),{},{range:i,radius:d,realScaleType:b,scale:g,cx:s,cy:u,innerRadius:p,outerRadius:d,startAngle:c,endAngle:l});return s7(s7({},e),{},ue({},r,w))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),mc=r(96241);let ml={light:"",dark:".dark"},ms=i.createContext(null);function mu(){let e=i.useContext(ms);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let mf=i.forwardRef(({id:e,className:t,children:r,config:n,...a},c)=>{let l=i.useId(),s=`chart-${e||l.replace(/:/g,"")}`;return(0,o.jsx)(ms.Provider,{value:{config:n},children:(0,o.jsxs)("div",{"data-chart":s,ref:c,className:(0,mc.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",t),...a,children:[(0,o.jsx)(mp,{id:s,config:n}),(0,o.jsx)(e$,{children:r})]})})});mf.displayName="Chart";let mp=({id:e,config:t})=>{let r=Object.entries(t).filter(([e,t])=>t.theme||t.color);return r.length?(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(ml).map(([t,n])=>`
${n} [data-chart=${e}] {
${r.map(([e,r])=>{let n=r.theme?.[t]||r.color;return n?`  --color-${e}: ${n};`:null}).join("\n")}
}
`).join("\n")}}):null},md=i.forwardRef(({active:e,payload:t,className:r,indicator:n="dot",hideLabel:a=!1,hideIndicator:c=!1,label:l,labelFormatter:s,labelClassName:u,formatter:f,color:p,nameKey:d,labelKey:h},y)=>{let{config:m}=mu(),v=i.useMemo(()=>{if(a||!t?.length)return null;let[e]=t,r=`${h||e.dataKey||e.name||"value"}`,n=mh(m,e,r),i=h||"string"!=typeof l?n?.label:m[l]?.label||l;return s?(0,o.jsx)("div",{className:(0,mc.cn)("font-medium",u),children:s(i,t)}):i?(0,o.jsx)("div",{className:(0,mc.cn)("font-medium",u),children:i}):null},[l,s,t,a,u,m,h]);if(!e||!t?.length)return null;let b=1===t.length&&"dot"!==n;return(0,o.jsxs)("div",{ref:y,className:(0,mc.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",r),children:[b?null:v,(0,o.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${d||e.name||e.dataKey||"value"}`,i=mh(m,e,r),a=p||e.payload.fill||e.color;return(0,o.jsx)("div",{className:(0,mc.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===n&&"items-center"),children:f&&e?.value!==void 0&&e.name?f(e.value,e.name,e,t,e.payload):(0,o.jsxs)(o.Fragment,{children:[i?.icon?(0,o.jsx)(i.icon,{}):!c&&(0,o.jsx)("div",{className:(0,mc.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":b&&"dashed"===n}),style:{"--color-bg":a,"--color-border":a}}),(0,o.jsxs)("div",{className:(0,mc.cn)("flex flex-1 justify-between leading-none",b?"items-end":"items-center"),children:[(0,o.jsxs)("div",{className:"grid gap-1.5",children:[b?v:null,(0,o.jsx)("span",{className:"text-muted-foreground",children:i?.label||e.name})]}),e.value&&(0,o.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});function mh(e,t,r){if("object"!=typeof t||null===t)return;let n="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,o=r;return r in t&&"string"==typeof t[r]?o=t[r]:n&&r in n&&"string"==typeof n[r]&&(o=n[r]),o in e?e[o]:e[r]}md.displayName="ChartTooltip",i.forwardRef(({className:e,hideIcon:t=!1,payload:r,verticalAlign:n="bottom",nameKey:i},a)=>{let{config:c}=mu();return r?.length?(0,o.jsx)("div",{ref:a,className:(0,mc.cn)("flex items-center justify-center gap-4","top"===n?"pb-3":"pt-3",e),children:r.map(e=>{let r=`${i||e.dataKey||"value"}`,n=mh(c,e,r);return(0,o.jsxs)("div",{className:(0,mc.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[n?.icon&&!t?(0,o.jsx)(n.icon,{}):(0,o.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),n?.label]},e.value)})}):null}).displayName="ChartLegend";var my=r(70569),mm=r(11273),mv=r(98599),mb=r(8730),mg=r(96963),mx=r(14163),mw=r(13495),mO=r(65551),mj=i.createContext(void 0);function mS(e){let t=i.useContext(mj);return e||t||"ltr"}var mA="rovingFocusGroup.onEntryFocus",mP={bubbles:!1,cancelable:!0},mk="RovingFocusGroup",[mE,mM,mT]=function(e){let t=e+"CollectionProvider",[r,n]=(0,mm.A)(t),[a,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,n=i.useRef(null),c=i.useRef(new Map).current;return(0,o.jsx)(a,{scope:t,itemMap:c,collectionRef:n,children:r})};l.displayName=t;let s=e+"CollectionSlot",u=i.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(s,r),a=(0,mv.s)(t,i.collectionRef);return(0,o.jsx)(mb.DX,{ref:a,children:n})});u.displayName=s;let f=e+"CollectionItemSlot",p="data-radix-collection-item",d=i.forwardRef((e,t)=>{let{scope:r,children:n,...a}=e,l=i.useRef(null),s=(0,mv.s)(t,l),u=c(f,r);return i.useEffect(()=>(u.itemMap.set(l,{ref:l,...a}),()=>void u.itemMap.delete(l))),(0,o.jsx)(mb.DX,{[p]:"",ref:s,children:n})});return d.displayName=f,[{Provider:l,Slot:u,ItemSlot:d},function(t){let r=c(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}(mk),[mN,m_]=(0,mm.A)(mk,[mT]),[mC,mD]=mN(mk),mI=i.forwardRef((e,t)=>(0,o.jsx)(mE.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(mE.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(mR,{...e,ref:t})})}));mI.displayName=mk;var mR=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:a=!1,dir:c,currentTabStopId:l,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:u,onEntryFocus:f,preventScrollOnEntryFocus:p=!1,...d}=e,h=i.useRef(null),y=(0,mv.s)(t,h),m=mS(c),[v=null,b]=(0,mO.i)({prop:l,defaultProp:s,onChange:u}),[g,x]=i.useState(!1),w=(0,mw.c)(f),O=mM(r),j=i.useRef(!1),[S,A]=i.useState(0);return i.useEffect(()=>{let e=h.current;if(e)return e.addEventListener(mA,w),()=>e.removeEventListener(mA,w)},[w]),(0,o.jsx)(mC,{scope:r,orientation:n,dir:m,loop:a,currentTabStopId:v,onItemFocus:i.useCallback(e=>b(e),[b]),onItemShiftTab:i.useCallback(()=>x(!0),[]),onFocusableItemAdd:i.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:i.useCallback(()=>A(e=>e-1),[]),children:(0,o.jsx)(mx.sG.div,{tabIndex:g||0===S?-1:0,"data-orientation":n,...d,ref:y,style:{outline:"none",...e.style},onMouseDown:(0,my.m)(e.onMouseDown,()=>{j.current=!0}),onFocus:(0,my.m)(e.onFocus,e=>{let t=!j.current;if(e.target===e.currentTarget&&t&&!g){let t=new CustomEvent(mA,mP);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);mF([e.find(e=>e.active),e.find(e=>e.id===v),...e].filter(Boolean).map(e=>e.ref.current),p)}}j.current=!1}),onBlur:(0,my.m)(e.onBlur,()=>x(!1))})})}),mB="RovingFocusGroupItem",mL=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:a=!1,tabStopId:c,...l}=e,s=(0,mg.B)(),u=c||s,f=mD(mB,r),p=f.currentTabStopId===u,d=mM(r),{onFocusableItemAdd:h,onFocusableItemRemove:y}=f;return i.useEffect(()=>{if(n)return h(),()=>y()},[n,h,y]),(0,o.jsx)(mE.ItemSlot,{scope:r,id:u,focusable:n,active:a,children:(0,o.jsx)(mx.sG.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...l,ref:t,onMouseDown:(0,my.m)(e.onMouseDown,e=>{n?f.onItemFocus(u):e.preventDefault()}),onFocus:(0,my.m)(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:(0,my.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return mz[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=d().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=f.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>mF(r))}})})})});mL.displayName=mB;var mz={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function mF(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var m$=r(46059),mU="Tabs",[mq,mW]=(0,mm.A)(mU,[m_]),mK=m_(),[mV,mH]=mq(mU),mG=i.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:i,defaultValue:a,orientation:c="horizontal",dir:l,activationMode:s="automatic",...u}=e,f=mS(l),[p,d]=(0,mO.i)({prop:n,onChange:i,defaultProp:a});return(0,o.jsx)(mV,{scope:r,baseId:(0,mg.B)(),value:p,onValueChange:d,orientation:c,dir:f,activationMode:s,children:(0,o.jsx)(mx.sG.div,{dir:f,"data-orientation":c,...u,ref:t})})});mG.displayName=mU;var mX="TabsList",mY=i.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...i}=e,a=mH(mX,r),c=mK(r);return(0,o.jsx)(mI,{asChild:!0,...c,orientation:a.orientation,dir:a.dir,loop:n,children:(0,o.jsx)(mx.sG.div,{role:"tablist","aria-orientation":a.orientation,...i,ref:t})})});mY.displayName=mX;var mZ="TabsTrigger",mJ=i.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...a}=e,c=mH(mZ,r),l=mK(r),s=m1(c.baseId,n),u=m2(c.baseId,n),f=n===c.value;return(0,o.jsx)(mL,{asChild:!0,...l,focusable:!i,active:f,children:(0,o.jsx)(mx.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:s,...a,ref:t,onMouseDown:(0,my.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(n)}),onKeyDown:(0,my.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(n)}),onFocus:(0,my.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;f||i||!e||c.onValueChange(n)})})})});mJ.displayName=mZ;var mQ="TabsContent",m0=i.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:a,children:c,...l}=e,s=mH(mQ,r),u=m1(s.baseId,n),f=m2(s.baseId,n),p=n===s.value,d=i.useRef(p);return i.useEffect(()=>{let e=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,o.jsx)(m$.C,{present:a||p,children:({present:r})=>(0,o.jsx)(mx.sG.div,{"data-state":p?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:f,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:r&&c})})});function m1(e,t){return`${e}-trigger-${t}`}function m2(e,t){return`${e}-content-${t}`}m0.displayName=mQ;let m5=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(mY,{ref:r,className:(0,mc.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));m5.displayName=mY.displayName;let m4=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(mJ,{ref:r,className:(0,mc.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));m4.displayName=mJ.displayName;let m3=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(m0,{ref:r,className:(0,mc.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));m3.displayName=m0.displayName;var m6=r(59821),m8="Progress",[m9,m7]=(0,mm.A)(m8),[ve,vt]=m9(m8),vr=i.forwardRef((e,t)=>{var r,n;let{__scopeProgress:i,value:a=null,max:c,getValueLabel:l=vi,...s}=e;(c||0===c)&&!vl(c)&&console.error((r=`${c}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=vl(c)?c:100;null===a||vs(a,u)||console.error((n=`${a}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let f=vs(a,u)?a:null,p=vc(f)?l(f,u):void 0;return(0,o.jsx)(ve,{scope:i,value:f,max:u,children:(0,o.jsx)(mx.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":vc(f)?f:void 0,"aria-valuetext":p,role:"progressbar","data-state":va(f,u),"data-value":f??void 0,"data-max":u,...s,ref:t})})});vr.displayName=m8;var vn="ProgressIndicator",vo=i.forwardRef((e,t)=>{let{__scopeProgress:r,...n}=e,i=vt(vn,r);return(0,o.jsx)(mx.sG.div,{"data-state":va(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...n,ref:t})});function vi(e,t){return`${Math.round(e/t*100)}%`}function va(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function vc(e){return"number"==typeof e}function vl(e){return vc(e)&&!isNaN(e)&&e>0}function vs(e,t){return vc(e)&&!isNaN(e)&&e<=t&&e>=0}vo.displayName=vn;let vu=i.forwardRef(({className:e,value:t,...r},n)=>(0,o.jsx)(vr,{ref:n,className:(0,mc.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,o.jsx)(vo,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));function vf(){let[e,t]=(0,i.useState)("1W"),[r,n]=(0,i.useState)(!0),[a,l]=(0,i.useState)("portfolio"),[_,C]=(0,i.useState)(null),{positions:D,trades:I,accountInfo:R,getTotalPnL:B,getTotalMargin:L,getAvailableBalance:z,marketData:F}=(0,N.fx)(),$=(0,i.useMemo)(()=>{let e=I.length,t=I.filter(e=>e.realizedPnl>0).length,r=I.filter(e=>e.realizedPnl<0).length,n=I.reduce((e,t)=>e+t.realizedPnl,0),o=B(),i=_?.balance.current||0,a=_?.balance.transactions?.[0]?.balance_after||1e4,c=i+o,l=(c-a)/a*100,s=I.length>0?Math.max(...I.map(e=>e.realizedPnl)):0,u=I.length>0?Math.min(...I.map(e=>e.realizedPnl)):0,f=e>0?I.reduce((e,t)=>e+t.price*t.quantity,0)/e:0,p=D.reduce((e,t)=>(e[t.symbol]=(e[t.symbol]||0)+Math.abs(t.size*t.markPrice),e),{}),d=I.filter(e=>e.realizedPnl>0),h=I.filter(e=>e.realizedPnl<0),y=d.length>0?d.reduce((e,t)=>e+t.realizedPnl,0)/d.length:0,m=h.length>0?Math.abs(h.reduce((e,t)=>e+t.realizedPnl,0)/h.length):0,v=I.map(e=>e.realizedPnl/a*100),b=v.length>0?v.reduce((e,t)=>e+t,0)/v.length:0,g=v.length>1?Math.sqrt(v.reduce((e,t)=>e+Math.pow(t-b,2),0)/(v.length-1)):0,x=0,w=a,O=a;I.forEach(e=>{(O+=e.realizedPnl)>w&&(w=O);let t=(w-O)/w*100;t>x&&(x=t)});let j=x>0?l/x:999*(l>0),S=v.filter(e=>e<0),A=S.length>0?Math.sqrt(S.reduce((e,t)=>e+Math.pow(t,2),0)/S.length):0,P=0,k=0,E=0,M=0;I.forEach(e=>{e.realizedPnl>0?(E++,M=0,P=Math.max(P,E)):e.realizedPnl<0&&(M++,E=0,k=Math.max(k,M))});let T=e>1?(I[I.length-1]?.timestamp-I[0]?.timestamp)/e/36e5:0;return{totalTrades:e,winningTrades:t,losingTrades:r,winRate:e>0?t/e*100:0,totalRealizedPnL:n,totalUnrealizedPnL:o,totalPnL:n+o,currentBalance:i,portfolioValue:c,totalReturn:l,bestTrade:s,worstTrade:u,avgTradeSize:f,avgPnLPerTrade:e>0?n/e:0,positionsBySymbol:p,totalMargin:L(),availableBalance:z(),marginUsage:i>0?L()/i*100:0,avgWin:y,avgLoss:m,profitFactor:m>0?y/m:999*(y>0),sharpeRatio:g>0?(b-.02/252)/g:0,maxDrawdown:x,calmarRatio:j,sortinoRatio:A>0?b/A:999*(b>0),maxConsecutiveWins:P,maxConsecutiveLosses:k,avgHoldingTime:T,riskRewardRatio:m>0?y/m:0,returnStdDev:g}},[I,D,_,B,L,z]),U=(0,i.useMemo)(()=>{let t=[],r=Date.now(),n={"24H":864e5,"1W":6048e5,"1M":2592e6,"3M":7776e6,"1Y":31536e6},o=(n[e]||n["1W"])/20;for(let n=20;n>=0;n--){let i=r-n*o,a=new Date(i),c=I.filter(e=>e.timestamp<=i).reduce((e,t)=>e+t.realizedPnl,0),l=(_?.balance.current||1e4)+c;t.push({date:"24H"===e?a.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):"1W"===e?a.toLocaleDateString([],{weekday:"short"}):a.toLocaleDateString([],{month:"short",day:"numeric"}),portfolio:l,pnl:c,timestamp:i})}return t},[I,_,e]),q=(0,i.useMemo)(()=>{let e=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#06b6d4","#84cc16","#f97316"];return Object.entries($.positionsBySymbol).map(([t,r],n)=>({name:t,value:r,color:e[n%e.length]}))},[$.positionsBySymbol]),W=(0,i.useMemo)(()=>{let e=[],t=_?.balance.transactions?.[0]?.balance_after||1e4,r=t;return I.forEach((n,o)=>{(r+=n.realizedPnl)>t&&(t=r);let i=(t-r)/t*100;e.push({trade:o+1,drawdown:-i,value:r,date:new Date(n.timestamp).toLocaleDateString()})}),e},[I,_]),K=(0,i.useMemo)(()=>Object.keys($.positionsBySymbol).map(e=>{let t=I.filter(t=>t.symbol===e),r=t.map(e=>e.realizedPnl/(_?.balance.current||1e4)*100),n=r.length>0?r.reduce((e,t)=>e+t,0)/r.length:0,o=r.length>1?Math.sqrt(r.reduce((e,t)=>e+Math.pow(t-n,2),0)/(r.length-1)):0;return{symbol:e,return:n,risk:o,trades:t.length}}),[I,$.positionsBySymbol,_]),V=(0,i.useMemo)(()=>{let e={};return I.forEach(t=>{let r=new Date(t.timestamp),n=`${r.getFullYear()}-${String(r.getMonth()+1).padStart(2,"0")}`;e[n]||(e[n]={pnl:0,trades:0}),e[n].pnl+=t.realizedPnl,e[n].trades+=1}),Object.entries(e).map(([e,t])=>({month:e,pnl:t.pnl,trades:t.trades,avgPnL:t.trades>0?t.pnl/t.trades:0}))},[I]),H=(0,i.useMemo)(()=>[{min:-1/0,max:-1e3,label:"< -$1000"},{min:-1e3,max:-500,label:"-$1000 to -$500"},{min:-500,max:-100,label:"-$500 to -$100"},{min:-100,max:0,label:"-$100 to $0"},{min:0,max:100,label:"$0 to $100"},{min:100,max:500,label:"$100 to $500"},{min:500,max:1e3,label:"$500 to $1000"},{min:1e3,max:1/0,label:"> $1000"}].map(e=>({range:e.label,count:I.filter(t=>t.realizedPnl>e.min&&t.realizedPnl<=e.max).length})),[I]),G=[{title:"Portfolio Value",value:r?`$${$.portfolioValue.toLocaleString()}`:"••••••",change:`${$.totalReturn>=0?"+":""}${$.totalReturn.toFixed(2)}%`,trend:$.totalReturn>=0?"up":"down",icon:c.A,color:"bg-primary/10 text-primary"},{title:"Sharpe Ratio",value:$.sharpeRatio.toFixed(2),change:"Risk-Adj. Return",trend:$.sharpeRatio>=1?"up":$.sharpeRatio>=0?"neutral":"down",icon:s,color:$.sharpeRatio>=1?"bg-emerald-100 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400":$.sharpeRatio>=0?"bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400":"bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400"},{title:"Max Drawdown",value:`${$.maxDrawdown.toFixed(1)}%`,change:"Risk Level",trend:$.maxDrawdown<=10?"up":$.maxDrawdown<=20?"neutral":"down",icon:u.A,color:$.maxDrawdown<=10?"bg-emerald-100 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400":$.maxDrawdown<=20?"bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400":"bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400"},{title:"Profit Factor",value:$.profitFactor>999?"∞":$.profitFactor.toFixed(2),change:`Avg Win/Loss: ${$.riskRewardRatio.toFixed(2)}`,trend:$.profitFactor>=2?"up":$.profitFactor>=1?"neutral":"down",icon:f,color:$.profitFactor>=2?"bg-emerald-100 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400":$.profitFactor>=1?"bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400":"bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400"}],X=[{title:"Win Rate",value:`${$.winRate.toFixed(1)}%`,change:`${$.winningTrades}W / ${$.losingTrades}L`,trend:$.winRate>=50?"up":"down",icon:p,color:$.winRate>=50?"bg-emerald-100 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400":"bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400"},{title:"Calmar Ratio",value:$.calmarRatio>999?"∞":$.calmarRatio.toFixed(2),change:"Return/Drawdown",trend:$.calmarRatio>=3?"up":$.calmarRatio>=1?"neutral":"down",icon:d,color:$.calmarRatio>=3?"bg-emerald-100 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400":$.calmarRatio>=1?"bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400":"bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400"},{title:"Avg Holding Time",value:`${$.avgHoldingTime.toFixed(1)}h`,change:"Per Position",trend:"neutral",icon:h,color:"bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"},{title:"Consecutive Wins",value:$.maxConsecutiveWins.toString(),change:`Max Losses: ${$.maxConsecutiveLosses}`,trend:$.maxConsecutiveWins>=$.maxConsecutiveLosses?"up":"down",icon:y.A,color:"bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400"}];return(0,o.jsxs)("div",{className:"p-3 sm:p-4 space-y-4 sm:space-y-6",children:[(0,o.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center mb-3 md:mb-0",children:[(0,o.jsx)("div",{className:"bg-primary/10 rounded-full p-2 mr-3",children:(0,o.jsx)(m.A,{className:"h-5 w-5 sm:h-6 sm:w-6 text-primary"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-foreground",children:"Premium Trading Analytics"}),(0,o.jsx)("p",{className:"text-muted-foreground text-xs sm:text-sm",children:"Advanced performance insights and risk analysis"})]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,o.jsxs)(m6.E,{variant:"secondary",className:"bg-gradient-to-r from-purple-500 to-blue-500 text-white",children:[(0,o.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Premium"]}),(0,o.jsx)("button",{onClick:()=>n(!r),className:"p-2 rounded-md hover:bg-muted border border-border","aria-label":r?"Hide balance":"Show balance",children:r?(0,o.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"}):(0,o.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,o.jsxs)("button",{className:"px-3 sm:px-4 py-2 bg-card border border-border rounded-md text-xs sm:text-sm font-medium flex items-center hover:bg-muted text-foreground",children:[(0,o.jsx)(x.A,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2"}),(0,o.jsx)("span",{className:"hidden sm:inline",children:"Export"}),(0,o.jsx)("span",{className:"sm:hidden",children:"Export"})]}),(0,o.jsxs)("button",{className:"px-3 sm:px-4 py-2 bg-primary text-primary-foreground rounded-md text-xs sm:text-sm font-medium hover:bg-primary/90 flex items-center",children:[(0,o.jsx)(w.A,{className:"h-3 w-3 sm:h-4 sm:w-4 inline-block mr-1 sm:mr-2"}),(0,o.jsx)("span",{className:"hidden sm:inline",children:"Refresh"}),(0,o.jsx)("span",{className:"sm:hidden",children:"Refresh"})]})]})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4",children:G.map((e,t)=>{let r=e.icon;return(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-3 sm:p-4 relative overflow-hidden",children:[(0,o.jsx)("div",{className:"absolute top-0 right-0 w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-primary/10 to-transparent rounded-bl-full"}),(0,o.jsxs)("div",{className:"flex items-center justify-between mb-2 sm:mb-3",children:[(0,o.jsx)("div",{className:`rounded-full p-1.5 sm:p-2 ${e.color}`,children:(0,o.jsx)(r,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),"up"===e.trend?(0,o.jsx)(O.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-emerald-500"}):"down"===e.trend?(0,o.jsx)(j.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-red-500"}):(0,o.jsx)(S,{className:"h-3 w-3 sm:h-4 sm:w-4 text-yellow-500"})]}),(0,o.jsx)("div",{className:"text-xs sm:text-sm text-muted-foreground mb-1",children:e.title}),(0,o.jsx)("div",{className:"text-lg sm:text-2xl font-bold text-foreground mb-1",children:e.value}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:e.change})]},t)})}),(0,o.jsxs)(mG,{defaultValue:"overview",className:"w-full",children:[(0,o.jsxs)(m5,{className:"grid w-full grid-cols-5 h-auto",children:[(0,o.jsxs)(m4,{value:"overview",className:"flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 sm:p-3",children:[(0,o.jsx)(m.A,{className:"h-3 w-3 sm:h-4 sm:w-4"}),(0,o.jsx)("span",{className:"text-xs sm:text-sm",children:"Overview"})]}),(0,o.jsxs)(m4,{value:"risk",className:"flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 sm:p-3",children:[(0,o.jsx)(u.A,{className:"h-3 w-3 sm:h-4 sm:w-4"}),(0,o.jsx)("span",{className:"text-xs sm:text-sm hidden sm:inline",children:"Risk Analysis"}),(0,o.jsx)("span",{className:"text-xs sm:hidden",children:"Risk"})]}),(0,o.jsxs)(m4,{value:"performance",className:"flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 sm:p-3",children:[(0,o.jsx)(A.A,{className:"h-3 w-3 sm:h-4 sm:w-4"}),(0,o.jsx)("span",{className:"text-xs sm:text-sm hidden sm:inline",children:"Performance"}),(0,o.jsx)("span",{className:"text-xs sm:hidden",children:"Perf"})]}),(0,o.jsxs)(m4,{value:"behavior",className:"flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 sm:p-3",children:[(0,o.jsx)(s,{className:"h-3 w-3 sm:h-4 sm:w-4"}),(0,o.jsx)("span",{className:"text-xs sm:text-sm",children:"Behavior"})]}),(0,o.jsxs)(m4,{value:"distribution",className:"flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 sm:p-3",children:[(0,o.jsx)(P.A,{className:"h-3 w-3 sm:h-4 sm:w-4"}),(0,o.jsx)("span",{className:"text-xs sm:text-sm hidden sm:inline",children:"Distribution"}),(0,o.jsx)("span",{className:"text-xs sm:hidden",children:"Dist"})]})]}),(0,o.jsxs)(m3,{value:"overview",className:"space-y-4 sm:space-y-6 mt-4 sm:mt-6",children:[(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4 sm:p-6",children:[(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-base sm:text-lg font-bold text-foreground",children:"Portfolio Performance"}),(0,o.jsx)("p",{className:"text-xs sm:text-sm text-muted-foreground",children:"Track your portfolio value over time"})]}),(0,o.jsx)("div",{className:"flex items-center space-x-2 mt-3 sm:mt-0",children:(0,o.jsx)("div",{className:"flex space-x-1 overflow-x-auto",children:["24H","1W","1M","3M","1Y"].map(r=>(0,o.jsx)("button",{className:`px-2 sm:px-3 py-1 text-xs rounded-md font-medium whitespace-nowrap ${e===r?"bg-primary text-primary-foreground":"bg-muted text-foreground hover:bg-muted/80"}`,onClick:()=>t(r),children:r},r))})})]}),(0,o.jsx)("div",{className:"h-64 sm:h-80 w-full overflow-hidden",children:(0,o.jsx)(mf,{config:{portfolio:{label:"Portfolio Value",color:"hsl(var(--primary))"},pnl:{label:"P&L",color:"hsl(var(--chart-2))"}},className:"h-full w-full",children:(0,o.jsx)(e$,{width:"100%",height:"100%",children:(0,o.jsxs)(hq,{data:U,margin:{top:10,right:30,left:20,bottom:20},children:[(0,o.jsx)("defs",{children:(0,o.jsxs)("linearGradient",{id:"portfolioGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,o.jsx)("stop",{offset:"5%",stopColor:"hsl(var(--primary))",stopOpacity:.3}),(0,o.jsx)("stop",{offset:"95%",stopColor:"hsl(var(--primary))",stopOpacity:0})]})}),(0,o.jsx)(h6,{strokeDasharray:"3 3",className:"stroke-muted"}),(0,o.jsx)(hC,{dataKey:"date",fontSize:12,tickLine:!1,axisLine:!1,className:"fill-muted-foreground"}),(0,o.jsx)(hU,{fontSize:12,tickFormatter:e=>`$${e.toLocaleString()}`,tickLine:!1,axisLine:!1,className:"fill-muted-foreground"}),(0,o.jsx)(tj,{content:(0,o.jsx)(md,{}),formatter:(e,t)=>[`$${Number(e).toLocaleString()}`,"portfolio"===t?"Portfolio Value":"P&L"]}),(0,o.jsx)(hn,{type:"monotone",dataKey:"portfolio",stroke:"hsl(var(--primary))",strokeWidth:2,fill:"url(#portfolioGradient)",dot:!1})]})})})})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4",children:X.map((e,t)=>{let r=e.icon;return(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-3 sm:p-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-2 sm:mb-3",children:[(0,o.jsx)("div",{className:`rounded-full p-1.5 sm:p-2 ${e.color}`,children:(0,o.jsx)(r,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),"up"===e.trend?(0,o.jsx)(O.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-emerald-500"}):"down"===e.trend?(0,o.jsx)(j.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-red-500"}):(0,o.jsx)(S,{className:"h-3 w-3 sm:h-4 sm:w-4 text-yellow-500"})]}),(0,o.jsx)("div",{className:"text-xs sm:text-sm text-muted-foreground mb-1",children:e.title}),(0,o.jsx)("div",{className:"text-lg sm:text-2xl font-bold text-foreground mb-1",children:e.value}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:e.change})]},t)})})]}),(0,o.jsxs)(m3,{value:"risk",className:"space-y-6 mt-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Risk Score"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Overall portfolio risk"})]}),(0,o.jsx)(u.A,{className:"h-6 w-6 text-primary"})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:`text-4xl font-bold ${$.maxDrawdown<=10?"text-emerald-600 dark:text-emerald-400":$.maxDrawdown<=20?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400"}`,children:$.maxDrawdown<=10?"LOW":$.maxDrawdown<=20?"MED":"HIGH"}),(0,o.jsx)("div",{className:"text-sm text-muted-foreground",children:"Risk Level"})]}),(0,o.jsx)(vu,{value:Math.min(2*$.maxDrawdown,100),className:"h-2"}),(0,o.jsxs)("div",{className:"text-xs text-muted-foreground text-center",children:["Based on max drawdown: ",$.maxDrawdown.toFixed(1),"%"]})]})]}),(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Volatility"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Return volatility"})]}),(0,o.jsx)(S,{className:"h-6 w-6 text-primary"})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Standard Deviation"}),(0,o.jsxs)("span",{className:"font-medium text-foreground",children:[$.returnStdDev.toFixed(2),"%"]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Sortino Ratio"}),(0,o.jsx)("span",{className:"font-medium text-foreground",children:$.sortinoRatio>999?"∞":$.sortinoRatio.toFixed(2)})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Downside Deviation"}),(0,o.jsxs)("span",{className:"font-medium text-foreground",children:[(.7*$.returnStdDev).toFixed(2),"%"]})]})]})]}),(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Risk-Adjusted"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Performance ratios"})]}),(0,o.jsx)(d,{className:"h-6 w-6 text-primary"})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Sharpe Ratio"}),(0,o.jsx)("span",{className:`font-medium ${$.sharpeRatio>=1?"text-emerald-600 dark:text-emerald-400":$.sharpeRatio>=0?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400"}`,children:$.sharpeRatio.toFixed(2)})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Calmar Ratio"}),(0,o.jsx)("span",{className:"font-medium text-foreground",children:$.calmarRatio>999?"∞":$.calmarRatio.toFixed(2)})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Risk/Reward"}),(0,o.jsx)("span",{className:"font-medium text-foreground",children:$.riskRewardRatio.toFixed(2)})]})]})]})]}),W.length>0&&(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Drawdown Analysis"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Portfolio drawdown over time"})]}),(0,o.jsx)(k.A,{className:"h-5 w-5 text-primary"})]}),(0,o.jsx)("div",{className:"h-64 w-full overflow-hidden",children:(0,o.jsx)(mf,{config:{drawdown:{label:"Drawdown",color:"hsl(var(--destructive))"}},className:"h-full w-full",children:(0,o.jsx)(e$,{width:"100%",height:"100%",children:(0,o.jsxs)(hq,{data:W,margin:{top:10,right:30,left:20,bottom:20},children:[(0,o.jsx)("defs",{children:(0,o.jsxs)("linearGradient",{id:"drawdownGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,o.jsx)("stop",{offset:"5%",stopColor:"hsl(var(--destructive))",stopOpacity:.3}),(0,o.jsx)("stop",{offset:"95%",stopColor:"hsl(var(--destructive))",stopOpacity:0})]})}),(0,o.jsx)(h6,{strokeDasharray:"3 3",className:"stroke-muted"}),(0,o.jsx)(hC,{dataKey:"trade",fontSize:12,tickLine:!1,axisLine:!1,className:"fill-muted-foreground"}),(0,o.jsx)(hU,{fontSize:12,tickFormatter:e=>`${e.toFixed(1)}%`,tickLine:!1,axisLine:!1,className:"fill-muted-foreground"}),(0,o.jsx)(tj,{content:(0,o.jsx)(md,{}),formatter:e=>[`${Number(e).toFixed(2)}%`,"Drawdown"]}),(0,o.jsx)(hn,{type:"monotone",dataKey:"drawdown",stroke:"hsl(var(--destructive))",strokeWidth:2,fill:"url(#drawdownGradient)",dot:!1})]})})})})]})]}),(0,o.jsxs)(m3,{value:"performance",className:"space-y-6 mt-6",children:[V.length>0&&(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Monthly Performance"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"P&L breakdown by month"})]}),(0,o.jsx)(E,{className:"h-5 w-5 text-primary"})]}),(0,o.jsx)("div",{className:"h-64 w-full overflow-hidden",children:(0,o.jsx)(mf,{config:{pnl:{label:"P&L",color:"hsl(var(--primary))"}},className:"h-full w-full",children:(0,o.jsx)(e$,{width:"100%",height:"100%",children:(0,o.jsxs)(h8,{data:V,margin:{top:10,right:30,left:20,bottom:20},children:[(0,o.jsx)(h6,{strokeDasharray:"3 3",className:"stroke-muted"}),(0,o.jsx)(hC,{dataKey:"month",fontSize:12,tickLine:!1,axisLine:!1,className:"fill-muted-foreground"}),(0,o.jsx)(hU,{fontSize:12,tickFormatter:e=>`$${e.toLocaleString()}`,tickLine:!1,axisLine:!1,className:"fill-muted-foreground"}),(0,o.jsx)(tj,{content:(0,o.jsx)(md,{}),formatter:e=>[`$${Number(e).toFixed(2)}`,"P&L"]}),(0,o.jsx)(fE,{dataKey:"pnl",fill:"hsl(var(--primary))",radius:[4,4,0,0]})]})})})})]}),K.length>0&&(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Risk vs Return by Asset"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Risk-return profile of traded assets"})]}),(0,o.jsx)(S,{className:"h-5 w-5 text-primary"})]}),(0,o.jsx)("div",{className:"h-64 w-full overflow-hidden",children:(0,o.jsx)(mf,{config:{return:{label:"Return",color:"hsl(var(--primary))"}},className:"h-full w-full",children:(0,o.jsx)(e$,{width:"100%",height:"100%",children:(0,o.jsxs)(yg,{data:K,margin:{top:10,right:30,left:20,bottom:20},children:[(0,o.jsx)(h6,{strokeDasharray:"3 3",className:"stroke-muted"}),(0,o.jsx)(hC,{dataKey:"risk",fontSize:12,tickLine:!1,axisLine:!1,className:"fill-muted-foreground",label:{value:"Risk (Volatility %)",position:"insideBottom",offset:-5}}),(0,o.jsx)(hU,{dataKey:"return",fontSize:12,tickLine:!1,axisLine:!1,className:"fill-muted-foreground",label:{value:"Return %",angle:-90,position:"insideLeft"}}),(0,o.jsx)(tj,{content:(0,o.jsx)(md,{}),formatter:(e,t,r)=>[`${Number(e).toFixed(2)}%`,"return"===t?"Return":"Risk",r.payload.symbol]}),(0,o.jsx)(yb,{dataKey:"return",fill:"hsl(var(--primary))"})]})})})})]})]}),(0,o.jsx)(m3,{value:"behavior",className:"space-y-6 mt-6",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Trading Patterns"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Behavioral insights"})]}),(0,o.jsx)(s,{className:"h-6 w-6 text-primary"})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Avg Holding Time"}),(0,o.jsxs)("span",{className:"font-medium text-foreground",children:[$.avgHoldingTime.toFixed(1),"h"]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Max Consecutive Wins"}),(0,o.jsx)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:$.maxConsecutiveWins})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Max Consecutive Losses"}),(0,o.jsx)("span",{className:"font-medium text-red-600 dark:text-red-400",children:$.maxConsecutiveLosses})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Avg Trade Size"}),(0,o.jsxs)("span",{className:"font-medium text-foreground",children:["$",$.avgTradeSize.toLocaleString()]})]})]})]}),(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Performance Ratios"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Key performance metrics"})]}),(0,o.jsx)(f,{className:"h-6 w-6 text-primary"})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Profit Factor"}),(0,o.jsx)("span",{className:"font-medium text-foreground",children:$.profitFactor>999?"∞":$.profitFactor.toFixed(2)})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Average Win"}),(0,o.jsxs)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:["$",$.avgWin.toFixed(2)]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Average Loss"}),(0,o.jsxs)("span",{className:"font-medium text-red-600 dark:text-red-400",children:["$",$.avgLoss.toFixed(2)]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:"Risk/Reward Ratio"}),(0,o.jsx)("span",{className:"font-medium text-foreground",children:$.riskRewardRatio.toFixed(2)})]})]})]})]})}),(0,o.jsxs)(m3,{value:"distribution",className:"space-y-6 mt-6",children:[H.length>0&&(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-foreground",children:"Trade P&L Distribution"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Distribution of trade outcomes"})]}),(0,o.jsx)(P.A,{className:"h-5 w-5 text-primary"})]}),(0,o.jsx)("div",{className:"h-64 w-full overflow-hidden",children:(0,o.jsx)(mf,{config:{count:{label:"Count",color:"hsl(var(--primary))"}},className:"h-full w-full",children:(0,o.jsx)(e$,{width:"100%",height:"100%",children:(0,o.jsxs)(h8,{data:H,margin:{top:10,right:30,left:20,bottom:60},children:[(0,o.jsx)(h6,{strokeDasharray:"3 3",className:"stroke-muted"}),(0,o.jsx)(hC,{dataKey:"range",fontSize:10,tickLine:!1,axisLine:!1,className:"fill-muted-foreground",angle:-45,textAnchor:"end",height:60}),(0,o.jsx)(hU,{fontSize:12,tickLine:!1,axisLine:!1,className:"fill-muted-foreground"}),(0,o.jsx)(tj,{content:(0,o.jsx)(md,{}),formatter:e=>[e,"Trades"]}),(0,o.jsx)(fE,{dataKey:"count",fill:"hsl(var(--primary))",radius:[4,4,0,0]})]})})})})]}),(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-lg font-bold text-foreground",children:"Position Distribution"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Current portfolio allocation"})]}),(0,o.jsx)(M.A,{className:"h-5 w-5 text-primary"})]}),q.length>0?(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("div",{className:"h-48 w-full flex items-center justify-center overflow-hidden",children:(0,o.jsx)(mf,{config:{value:{label:"Value",color:"hsl(var(--primary))"}},className:"h-full w-full",children:(0,o.jsx)(e$,{width:"100%",height:"100%",children:(0,o.jsxs)(ma,{children:[(0,o.jsx)(tj,{content:(0,o.jsx)(md,{}),formatter:(e,t)=>[`$${Number(e).toLocaleString()}`,t]}),(0,o.jsx)(mi,{data:q,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:2,dataKey:"value",children:q.map((e,t)=>(0,o.jsx)(uk,{fill:e.color},`cell-${t}`))})]})})})}),(0,o.jsx)("div",{className:"space-y-2",children:q.map((e,t)=>(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.color}}),(0,o.jsx)("span",{className:"text-sm font-medium text-foreground",children:e.name})]}),(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:r?`$${e.value.toLocaleString()}`:"••••••"})]},t))})]}):(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)(M.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-3"}),(0,o.jsx)("p",{className:"text-muted-foreground",children:"No active positions"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Start trading to see your position distribution"})]})]})]})]}),I.length>0&&(0,o.jsxs)("div",{className:"bg-card rounded-lg border border-border p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-lg font-bold text-foreground",children:"Recent Trading Activity"}),(0,o.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Latest ",Math.min(I.length,5)," trades"]})]}),(0,o.jsx)(T.A,{className:"h-5 w-5 text-primary"})]}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"min-w-full",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"text-xs text-muted-foreground border-b border-border",children:[(0,o.jsx)("th",{className:"pb-2 text-left",children:"SYMBOL"}),(0,o.jsx)("th",{className:"pb-2 text-left",children:"SIDE"}),(0,o.jsx)("th",{className:"pb-2 text-right",children:"SIZE"}),(0,o.jsx)("th",{className:"pb-2 text-right",children:"PRICE"}),(0,o.jsx)("th",{className:"pb-2 text-right",children:"P&L"}),(0,o.jsx)("th",{className:"pb-2 text-right",children:"TIME"})]})}),(0,o.jsx)("tbody",{children:I.slice(0,5).map((e,t)=>(0,o.jsxs)("tr",{className:"border-b border-border last:border-0 hover:bg-muted",children:[(0,o.jsx)("td",{className:"py-3",children:(0,o.jsx)("span",{className:"font-medium text-foreground",children:e.symbol})}),(0,o.jsx)("td",{className:"py-3",children:(0,o.jsx)("span",{className:`text-xs px-2 py-1 rounded-full font-medium ${"BUY"===e.side?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400":"bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"}`,children:e.side})}),(0,o.jsx)("td",{className:"py-3 text-right text-foreground",children:e.quantity}),(0,o.jsxs)("td",{className:"py-3 text-right text-foreground",children:["$",e.price.toLocaleString()]}),(0,o.jsx)("td",{className:"py-3 text-right",children:(0,o.jsxs)("span",{className:`font-medium ${e.realizedPnl>=0?"text-emerald-600 dark:text-emerald-400":"text-red-600 dark:text-red-400"}`,children:[e.realizedPnl>=0?"+":"","$",e.realizedPnl.toFixed(2)]})}),(0,o.jsx)("td",{className:"py-3 text-right text-muted-foreground text-xs",children:new Date(e.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]},t))})]})})]})]})}vu.displayName=vr.displayName},69433:(e,t,r)=>{e.exports=r(5566)("toUpperCase")},69619:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=0x1fffffffffffff}},69691:(e,t,r)=>{var n=r(41157),o=r(99114),i=r(22);e.exports=function(e,t){var r={};return t=i(t,3),o(e,function(e,o,i){n(r,o,t(e,o,i))}),r}},70151:(e,t,r)=>{var n=r(85718);e.exports=function(){return n.Date.now()}},70222:(e,t,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),r=e[c];try{e[c]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[c]=r:delete e[c]),o}},71960:e=>{e.exports=function(e,t,r){for(var n=-1,o=null==e?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}},71967:(e,t,r)=>{var n=r(15871);e.exports=function(e,t){return n(e,t)}},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74610:e=>{e.exports=function(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return -1}},75254:(e,t,r)=>{var n=r(78418),o=r(93311),i=r(41132);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},75411:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},75847:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r;return n(e,function(e,n,o){return!(r=t(e,n,o))}),!!r}},77822:(e,t,r)=>{var n=r(93490);e.exports=function(e){return n(e)&&e!=+e}},77834:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},78418:(e,t,r)=>{var n=r(67200),o=r(15871);e.exports=function(e,t,r,i){var a=r.length,c=a,l=!i;if(null==e)return!c;for(e=Object(e);a--;){var s=r[a];if(l&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++a<c;){var u=(s=r[a])[0],f=e[u],p=s[1];if(l&&s[2]){if(void 0===f&&!(u in e))return!1}else{var d=new n;if(i)var h=i(f,p,u,e,t,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},79428:e=>{"use strict";e.exports=require("buffer")},79474:(e,t,r)=>{e.exports=r(85718).Symbol},79551:e=>{"use strict";e.exports=require("url")},80195:(e,t,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),c=1/0,l=n?n.prototype:void 0,s=l?l.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return s?s.call(t):"";var r=t+"";return"0"==r&&1/t==-c?"-0":r}},80329:(e,t,r)=>{e=r.nmd(e);var n=r(85718),o=r(1944),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,c=a&&a.exports===i?n.Buffer:void 0,l=c?c.isBuffer:void 0;e.exports=l||o},80458:(e,t,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},80704:(e,t,r)=>{var n=r(96678);e.exports=function(e,t){return!!(null==e?0:e.length)&&n(e,t,0)>-1}},81488:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},81630:e=>{"use strict";e.exports=require("http")},81957:(e,t,r)=>{var n=r(49227);e.exports=function(e,t){if(e!==t){var r=void 0!==e,o=null===e,i=e==e,a=n(e),c=void 0!==t,l=null===t,s=t==t,u=n(t);if(!l&&!u&&!a&&e>t||a&&c&&s&&!l&&!u||o&&c&&s||!r&&s||!i)return 1;if(!o&&!a&&!u&&e<t||u&&r&&i&&!o&&!a||l&&r&&i||!c&&i||!s)return -1}return 0}},82038:(e,t,r)=>{var n=r(34821),o=r(35163),i=r(40542),a=r(80329),c=r(38428),l=r(10090),s=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),u=!r&&o(e),f=!r&&!u&&a(e),p=!r&&!u&&!f&&l(e),d=r||u||f||p,h=d?n(e.length,String):[],y=h.length;for(var m in e)(t||s.call(e,m))&&!(d&&("length"==m||f&&("offset"==m||"parent"==m)||p&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||c(m,y)))&&h.push(m);return h}},84031:(e,t,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=+!!t,t}},84482:(e,t,r)=>{var n=r(28977);e.exports=function(e){var t=n(e),r=t%1;return t==t?r?t-r:t:0}},84713:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},85244:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},85406:(e,t,r)=>{e.exports=r(85718)["__core-js_shared__"]},85450:(e,t,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(a&&e&&e[a])}},85718:(e,t,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},85745:(e,t,r)=>{var n=r(86451);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},85938:(e,t,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);e.exports=i(function(e,t){if(null==e)return[];var r=t.length;return r>1&&a(e,t[0],t[1])?t=[]:r>2&&a(t[0],t[1],t[2])&&(t=[t[0]]),o(e,n(t,1),[])})},86451:(e,t,r)=>{var n=r(95746);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},87270:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}},87321:(e,t,r)=>{var n=r(98798),o=r(7383),i=r(28977);e.exports=function(e){return function(t,r,a){return a&&"number"!=typeof a&&o(t,r,a)&&(r=a=void 0),t=i(t),void 0===r?(r=t,t=0):r=i(r),a=void 0===a?t<r?1:-1:i(a),n(t,r,a,e)}}},87506:(e,t,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),c=r(39672);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=c,e.exports=l},87955:(e,t,r)=>{e.exports=r(84031)()},89167:(e,t,r)=>{e.exports=r(41547)(r(85718),"DataView")},89185:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},89492:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},89605:(e,t,r)=>{e.exports=r(65662)(Object.keys,Object)},89624:e=>{e.exports=function(e){return function(t){return e(t)}}},90200:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},90453:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},90851:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},91290:e=>{e.exports=function(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return -1}},91645:e=>{"use strict";e.exports=require("net")},91928:(e,t,r)=>{var n=r(41547);e.exports=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}()},92662:(e,t,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),c=r(95308),l=r(2408);e.exports=function(e,t,r){var s=-1,u=o,f=e.length,p=!0,d=[],h=d;if(r)p=!1,u=i;else if(f>=200){var y=t?null:c(e);if(y)return l(y);p=!1,u=a,h=new n}else h=t?[]:d;t:for(;++s<f;){var m=e[s],v=t?t(m):m;if(m=r||0!==m?m:0,p&&v==v){for(var b=h.length;b--;)if(h[b]===v)continue t;t&&h.push(v),d.push(m)}else u(h,v,r)||(h!==d&&h.push(v),d.push(m))}return d}},93311:(e,t,r)=>{var n=r(34883),o=r(7651);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},93490:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"number"==typeof e||o(e)&&"[object Number]"==n(e)}},94388:(e,t,r)=>{var n=r(57797);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},94735:e=>{"use strict";e.exports=require("events")},95308:(e,t,r)=>{var n=r(34772),o=r(36959),i=r(2408);e.exports=n&&1/i(new n([,-0]))[1]==1/0?function(e){return new n(e)}:o},95746:(e,t,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),c=r(19976);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=c,e.exports=l},96678:(e,t,r)=>{var n=r(91290),o=r(39774),i=r(74610);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,o,r)}},97668:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case c:case a:case p:case d:return e;default:switch(e=e&&e.$$typeof){case u:case s:case f:case y:case h:case l:return e;default:return t}}case o:return t}}}(e)===i}},98451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return!0===e||!1===e||o(e)&&"[object Boolean]"==n(e)}},98798:e=>{var t=Math.ceil,r=Math.max;e.exports=function(e,n,o,i){for(var a=-1,c=r(t((n-e)/(o||1)),0),l=Array(c);c--;)l[i?c:++a]=e,e+=o;return l}},99114:(e,t,r)=>{var n=r(12344),o=r(7651);e.exports=function(e,t){return e&&n(e,t,o)}},99180:e=>{e.exports=function(e,t){return e>t}},99525:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[128,658,817,452,277,895,887],()=>r(3898));module.exports=n})();