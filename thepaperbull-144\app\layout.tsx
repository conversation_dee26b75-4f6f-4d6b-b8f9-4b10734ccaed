import type React from "react"
import "@/app/globals.css"
import { Inter } from "next/font/google"
import type { Metada<PERSON> } from "next"
import { Toaster } from "@/components/ui/sonner"
import { NotificationContainer } from "@/components/ui/notification"
import { Providers } from "@/components/providers"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "ThePaperBull - Crypto Trading Platform",
  description: "Advanced cryptocurrency futures trading platform with real-time data and professional trading tools",
  generator: "v0.dev",
  metadataBase: new URL("https://thepaperbull.com"),
  icons: {
    icon: [
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon-16x16.svg', sizes: '16x16', type: 'image/svg+xml' },
      { url: '/favicon-32x32.svg', sizes: '32x32', type: 'image/svg+xml' },
    ],
    apple: [
      { url: '/apple-touch-icon.svg', sizes: '180x180', type: 'image/svg+xml' },
    ],
    other: [
      { rel: 'mask-icon', url: '/favicon.svg', color: '#10b981' },
    ],
  },
  manifest: '/site.webmanifest',
  themeColor: '#10b981',
  openGraph: {
    title: "ThePaperBull - Advanced Crypto Trading Platform",
    description: "Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.",
    images: [
      {
        url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png",
        width: 1200,
        height: 630,
        alt: "ThePaperBull Trading Platform Interface",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "ThePaperBull - Advanced Crypto Trading Platform",
    description: "Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.",
    images: [
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png",
    ],
    creator: "@thepaperbull",
  },
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
          <NotificationContainer />
        </Providers>
      </body>
    </html>
  )
}
