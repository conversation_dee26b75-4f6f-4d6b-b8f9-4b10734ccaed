import { ref, push, set, remove, onValue, off, serverTimestamp, onDisconnect } from 'firebase/database'
import { realtimeDb } from './firebase'
import userService from './user-service'
import { Position, Order, Trade, AccountInfo, MarketData } from '@/types/trading'
import { notificationService } from './notification-service'

interface TradingState {
  positions: Position[]
  orders: Order[]
  trades: Trade[]
  accountInfo: AccountInfo | null
  marketData: Record<string, MarketData>
  isLoading: boolean
  error: string | null
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
}

class RealtimeTradingService {
  private state: TradingState = {
    positions: [],
    orders: [],
    trades: [],
    accountInfo: null,
    marketData: {},
    isLoading: false,
    error: null,
    connectionStatus: 'disconnected'
  }

  private subscribers: ((state: TradingState) => void)[] = []
  private userId: string | null = null
  private unsubscribeFunctions: (() => void)[] = []
  private lastOrderTime: number = 0
  private readonly ORDER_COOLDOWN = 1000 // 1 second cooldown between orders
  private updateTimeout: NodeJS.Timeout | null = null
  private reconnectTimeout: NodeJS.Timeout | null = null
  private initializationPromise: Promise<void> | null = null
  private isInitializing: boolean = false

  constructor() {
    // Subscribe to user changes with improved handling
    userService.subscribe((user) => {
      console.log('Realtime Trading Service - User state changed:', {
        hasUser: !!user,
        userId: user?.id,
        currentUserId: this.userId,
        isInitializing: this.isInitializing,
        userStructure: user ? Object.keys(user) : null
      })

      if (user && user.id) {
        if (this.userId !== user.id) {
          console.log('Initializing realtime trading service for user:', user.id)
          this.initializeForUser(user.id)
        } else if (this.userId === user.id && this.state.connectionStatus === 'disconnected') {
          // Reconnect if we have the same user but are disconnected
          console.log('Reconnecting realtime trading service for existing user:', user.id)
          this.reconnectForUser(user.id)
        }
      } else {
        console.log('User logged out, cleaning up realtime trading service')
        this.cleanup()
      }
    })

    // Enhanced startup user check with retry logic
    this.checkForExistingUser()
  }

  private async checkForExistingUser() {
    // Check multiple times with delays to handle async auth restoration
    const maxAttempts = 5
    let attempts = 0

    const checkUser = async () => {
      attempts++
      const currentUser = userService.getUser()
      const firebaseUser = userService.getFirebaseUser()

      console.log(`User check attempt ${attempts}:`, {
        currentUser: currentUser ? { id: currentUser.id, email: currentUser.email } : null,
        firebaseUser: firebaseUser ? { uid: firebaseUser.uid, email: firebaseUser.email } : null,
        hasUserId: !!this.userId
      })

      if (currentUser && currentUser.id && !this.userId) {
        console.log('Found existing user on startup, initializing:', currentUser.id)
        await this.initializeForUser(currentUser.id)
        return true
      } else if (firebaseUser && !this.userId && !currentUser) {
        // Firebase user exists but user service hasn't loaded yet, wait a bit more
        console.log('Firebase user exists, waiting for user service to load...')
        return false
      }

      return !!this.userId
    }

    // Initial check
    if (await checkUser()) return

    // Retry with delays
    for (let i = 1; i < maxAttempts; i++) {
      await new Promise(resolve => setTimeout(resolve, 500 * i)) // Increasing delays
      if (await checkUser()) return
    }

    console.log('No user found after all attempts, service will initialize when user logs in')
  }

  // Subscribe to state changes
  subscribe(callback: (state: TradingState) => void): () => void {
    this.subscribers.push(callback)
    callback(this.state) // Immediate callback with current state

    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  // Get current state method moved to avoid duplication

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.state))
  }

  // Debounced notification for frequent updates (like price changes)
  private notifySubscribersDebounced() {
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout)
    }

    this.updateTimeout = setTimeout(() => {
      this.notifySubscribers()
    }, 100) // 100ms debounce
  }

  private async initializeForUser(userId: string): Promise<void> {
    // Prevent multiple simultaneous initializations
    if (this.isInitializing && this.initializationPromise) {
      console.log('Already initializing, waiting for completion...')
      return this.initializationPromise
    }

    this.isInitializing = true
    this.initializationPromise = this._doInitializeForUser(userId)

    try {
      await this.initializationPromise
    } finally {
      this.isInitializing = false
      this.initializationPromise = null
    }
  }

  private async _doInitializeForUser(userId: string): Promise<void> {
    console.log('Starting initialization for user:', userId)

    // Clean up any existing connections first
    if (this.userId && this.userId !== userId) {
      console.log('Cleaning up previous user connections')
      this.cleanup()
    }

    this.userId = userId
    this.state.isLoading = true
    this.state.connectionStatus = 'connecting'
    this.state.error = null

    // Provide immediate feedback to UI
    this.notifySubscribers()

    try {
      // Initialize account info immediately
      await this.initializeAccountInfo()

      // Notify again with account info
      this.notifySubscribers()

      // Set up real-time listeners with connection monitoring
      // Note: The position listener will set isLoading to false once data is received
      await this.setupRealtimeListeners()

      this.state.connectionStatus = 'connected'
      this.state.error = null

      // Final notification (isLoading will be handled by position listener)
      this.notifySubscribers()

      console.log('Realtime trading service initialized successfully for user:', userId)
    } catch (error) {
      console.error('Error initializing realtime trading service:', error)
      this.state.isLoading = false
      this.state.connectionStatus = 'error'
      this.state.error = 'Failed to initialize trading service'
      this.notifySubscribers()

      // Schedule retry
      this.scheduleReconnect(userId)
      throw error
    }
  }

  private async reconnectForUser(userId: string): Promise<void> {
    console.log('Reconnecting for user:', userId)

    // Clear any pending reconnect
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    try {
      await this.initializeForUser(userId)
    } catch (error) {
      console.error('Reconnection failed:', error)
    }
  }

  private scheduleReconnect(userId: string, delay: number = 5000): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
    }

    console.log(`Scheduling reconnect in ${delay}ms for user:`, userId)
    this.reconnectTimeout = setTimeout(() => {
      if (this.userId === userId) { // Only reconnect if still the same user
        console.log('Attempting scheduled reconnect for user:', userId)
        this.reconnectForUser(userId)
      }
    }, delay)
  }

  private async initializeAccountInfo() {
    if (!this.userId) return

    const userBalance = userService.getUserBalance()
    this.state.accountInfo = {
      totalWalletBalance: userBalance,
      totalUnrealizedProfit: 0,
      totalMarginBalance: userBalance,
      totalPositionInitialMargin: 0,
      totalOpenOrderInitialMargin: 0,
      availableBalance: userBalance,
      maxWithdrawAmount: userBalance,
      updateTime: Date.now()
    }
  }

  private async setupRealtimeListeners(): Promise<void> {
    if (!this.userId) {
      throw new Error('Cannot setup listeners without user ID')
    }

    console.log('Setting up realtime listeners for user:', this.userId)

    // Clean up any existing listeners first
    this.cleanupListeners()

    try {
      // Set up connection monitoring
      const connectedRef = ref(realtimeDb, '.info/connected')
      const connectionUnsubscribe = onValue(connectedRef, (snapshot) => {
        const connected = snapshot.val()
        console.log('Firebase connection status:', connected)

        if (connected) {
          this.state.connectionStatus = 'connected'
          this.state.error = null
        } else {
          this.state.connectionStatus = 'disconnected'
          this.state.error = 'Connection lost'
          // Schedule reconnect when connection is restored
          this.scheduleReconnect(this.userId!, 2000)
        }
        this.notifySubscribers()
      })

      // Listen to positions with enhanced error handling and immediate loading
      const positionsRef = ref(realtimeDb, `users/${this.userId}/positions`)
      const positionsUnsubscribe = onValue(positionsRef,
        (snapshot) => {
          try {
            const data = snapshot.val()
            console.log('Positions data received:', data ? Object.keys(data).length : 0, 'positions')

            // Process positions with proper PnL calculation
            this.state.positions = data ? Object.entries(data).map(([id, pos]: [string, any]) => {
              const position = {
                id,
                ...pos,
                timestamp: pos.timestamp || Date.now(),
                // Ensure numeric values are properly parsed
                entryPrice: Number(pos.entryPrice) || 0,
                markPrice: Number(pos.markPrice) || Number(pos.entryPrice) || 0,
                size: Number(pos.size) || 0,
                margin: Number(pos.margin) || 0,
                leverage: Number(pos.leverage) || 10,
                pnl: Number(pos.pnl) || 0,
                pnlPercent: Number(pos.pnlPercent) || 0,
                liquidationPrice: Number(pos.liquidationPrice) || 0
              }

              // Recalculate PnL with current market data if available
              const currentPrice = this.state.marketData[position.symbol]?.price
              if (currentPrice && currentPrice > 0) {
                const updatedPosition = this.updatePositionPnL(position, currentPrice)
                return updatedPosition
              }

              return position
            }) : []

            // Set loading to false once positions are loaded
            this.state.isLoading = false
            this.updateAccountInfo()
            this.notifySubscribers()
          } catch (error) {
            console.error('Error processing positions data:', error)
            this.state.isLoading = false
            this.state.error = 'Failed to process positions data'
            this.notifySubscribers()
          }
        },
        (error) => {
          console.error('Error listening to positions:', error)
          this.state.error = 'Failed to load positions'
          this.state.connectionStatus = 'error'
          this.state.isLoading = false
          this.notifySubscribers()
          this.scheduleReconnect(this.userId!, 5000)
        }
      )

      // Listen to orders with error handling
      const ordersRef = ref(realtimeDb, `users/${this.userId}/orders`)
      const ordersUnsubscribe = onValue(ordersRef,
        (snapshot) => {
          try {
            const data = snapshot.val()
            console.log('Orders data received:', data ? Object.keys(data).length : 0, 'orders')

            this.state.orders = data ? Object.entries(data).map(([id, order]: [string, any]) => ({
              id,
              ...order,
              timestamp: order.timestamp || Date.now()
            })) : []

            this.updateAccountInfo()
            this.notifySubscribers()
          } catch (error) {
            console.error('Error processing orders data:', error)
          }
        },
        (error) => {
          console.error('Error listening to orders:', error)
          this.state.error = 'Failed to load orders'
          this.state.connectionStatus = 'error'
          this.notifySubscribers()
          this.scheduleReconnect(this.userId!, 5000)
        }
      )

      // Listen to trades with error handling
      const tradesRef = ref(realtimeDb, `users/${this.userId}/trades`)
      const tradesUnsubscribe = onValue(tradesRef,
        (snapshot) => {
          try {
            const data = snapshot.val()
            console.log('Trades data received:', data ? Object.keys(data).length : 0, 'trades')

            this.state.trades = data ? Object.entries(data).map(([id, trade]: [string, any]) => ({
              id,
              ...trade,
              timestamp: trade.timestamp || Date.now()
            })).sort((a, b) => b.timestamp - a.timestamp) : []

            this.notifySubscribers()
          } catch (error) {
            console.error('Error processing trades data:', error)
          }
        },
        (error) => {
          console.error('Error listening to trades:', error)
          this.state.error = 'Failed to load trade history'
          this.state.connectionStatus = 'error'
          this.notifySubscribers()
          this.scheduleReconnect(this.userId!, 5000)
        }
      )

      // Store unsubscribe functions
      this.unsubscribeFunctions = [
        connectionUnsubscribe,
        () => off(positionsRef, 'value', positionsUnsubscribe),
        () => off(ordersRef, 'value', ordersUnsubscribe),
        () => off(tradesRef, 'value', tradesUnsubscribe)
      ]

      console.log('Realtime listeners setup completed for user:', this.userId)
    } catch (error) {
      console.error('Error setting up realtime listeners:', error)
      throw error
    }
  }

  private cleanupListeners(): void {
    console.log('Cleaning up existing listeners')
    this.unsubscribeFunctions.forEach(unsubscribe => {
      try {
        unsubscribe()
      } catch (error) {
        console.error('Error during listener cleanup:', error)
      }
    })
    this.unsubscribeFunctions = []
  }

  private updateAccountInfo() {
    if (!this.state.accountInfo) return

    const userBalance = userService.getUserBalance()

    // Calculate total PnL from positions
    const totalPnL = this.state.positions.reduce((sum, pos) => sum + (pos.pnl || 0), 0)

    // Calculate total margin used by positions
    const totalMargin = this.state.positions.reduce((sum, pos) => sum + (pos.margin || 0), 0)

    // Calculate total order margin (for pending orders) - more accurate calculation
    const totalOrderMargin = this.state.orders
      .filter(order => order.status === 'NEW')
      .reduce((sum, order) => {
        // Use current market price if available, otherwise use order price
        const currentPrice = this.state.marketData[order.symbol]?.price || order.price
        const orderValue = order.origQty * currentPrice
        return sum + (orderValue / (order.leverage || 10))
      }, 0)

    // Calculate available balance more conservatively
    const totalUsedMargin = totalMargin + totalOrderMargin
    const availableBalance = Math.max(0, userBalance - totalUsedMargin)

    // Update account info
    this.state.accountInfo.totalWalletBalance = userBalance
    this.state.accountInfo.totalUnrealizedProfit = totalPnL
    this.state.accountInfo.totalPositionInitialMargin = totalMargin
    this.state.accountInfo.totalOpenOrderInitialMargin = totalOrderMargin
    this.state.accountInfo.totalMarginBalance = userBalance + totalPnL
    this.state.accountInfo.availableBalance = availableBalance
    this.state.accountInfo.maxWithdrawAmount = availableBalance
    this.state.accountInfo.updateTime = Date.now()

    console.log('Account Info Updated:', {
      userBalance,
      totalMargin,
      totalOrderMargin,
      totalUsedMargin,
      availableBalance,
      positionsCount: this.state.positions.length,
      ordersCount: this.state.orders.filter(o => o.status === 'NEW').length
    })
  }

  // Update market data and recalculate PnL (optimized - no database writes)
  updateMarketData(symbol: string, data: Partial<MarketData>): void {
    // Ensure timestamp is set if not provided
    const updatedData = {
      ...data,
      timestamp: data.timestamp || Date.now()
    }

    this.state.marketData[symbol] = { ...this.state.marketData[symbol], ...updatedData }

    // Update position PnL based on new market data (LOCAL ONLY - no database writes)
    if (data.price) {
      let hasPositionUpdates = false

      this.state.positions = this.state.positions.map(position => {
        if (position.symbol === symbol) {
          const updatedPosition = this.updatePositionPnL(position, data.price!)
          // Check if PnL actually changed to avoid unnecessary updates
          if (updatedPosition.pnl !== position.pnl || updatedPosition.markPrice !== position.markPrice) {
            hasPositionUpdates = true
          }
          return updatedPosition
        }
        return position
      })

      // Only update and notify if there were actual changes
      if (hasPositionUpdates) {
        this.updateAccountInfo()
        this.notifySubscribers() // Use immediate notifications for PnL updates
      }
    }
  }

  // Calculate PnL for a position based on current market price - Fixed calculation
  private calculatePnL(position: Position, currentPrice: number): { pnl: number; pnlPercent: number } {
    // Validate inputs
    if (!position || !currentPrice || currentPrice <= 0 || position.entryPrice <= 0 || position.size <= 0) {
      return { pnl: 0, pnlPercent: 0 }
    }

    // Calculate price difference based on position side
    const priceDiff = position.side === 'LONG'
      ? currentPrice - position.entryPrice
      : position.entryPrice - currentPrice

    // PnL = price difference * position size
    // Note: position.size should be the actual quantity, not leveraged quantity
    const pnl = priceDiff * position.size

    // PnL percentage based on margin invested (initial margin)
    const pnlPercent = position.margin > 0 ? (pnl / position.margin) * 100 : 0

    // Additional validation to prevent extreme values
    const validatedPnl = isFinite(pnl) ? pnl : 0
    const validatedPnlPercent = isFinite(pnlPercent) ? pnlPercent : 0

    return {
      pnl: Number(validatedPnl.toFixed(2)),
      pnlPercent: Number(validatedPnlPercent.toFixed(2))
    }
  }

  // Update position PnL based on current market price
  private updatePositionPnL(position: Position, currentPrice: number): Position {
    const { pnl, pnlPercent } = this.calculatePnL(position, currentPrice)

    return {
      ...position,
      markPrice: currentPrice,
      pnl,
      pnlPercent
    }
  }

  // Calculate liquidation price
  private calculateLiquidationPrice(entryPrice: number, side: 'LONG' | 'SHORT', leverage: number): number {
    const maintenanceMarginRate = 0.005 // 0.5%
    const liquidationBuffer = 1 - maintenanceMarginRate - (1 / leverage)

    if (side === 'LONG') {
      return entryPrice * liquidationBuffer
    } else {
      return entryPrice * (2 - liquidationBuffer)
    }
  }

  // Place a new order with immediate UI feedback
  async placeOrder(orderRequest: {
    symbol: string
    side: 'BUY' | 'SELL'
    type: 'MARKET' | 'LIMIT'
    quantity: number
    price?: number
    leverage?: number
    stopLoss?: number
    takeProfit?: number
  }): Promise<string> {
    // Enhanced authentication check with fallback
    if (!this.userId) {
      // Try to get user ID from Firebase Auth as fallback
      const firebaseUser = userService.getFirebaseUser()
      const userServiceUser = userService.getUser()

      console.log('Authentication check in placeOrder:', {
        realtimeServiceUserId: this.userId,
        firebaseUser: firebaseUser ? { uid: firebaseUser.uid, email: firebaseUser.email } : null,
        userServiceUser: userServiceUser ? { id: userServiceUser.id, email: userServiceUser.email } : null
      })

      if (firebaseUser) {
        console.log('Using Firebase user ID as fallback:', firebaseUser.uid)
        this.userId = firebaseUser.uid
        // Initialize for this user
        await this.initializeForUser(firebaseUser.uid)
      } else if (userServiceUser && userServiceUser.id) {
        console.log('Using user service ID as fallback:', userServiceUser.id)
        this.userId = userServiceUser.id
        await this.initializeForUser(userServiceUser.id)
      } else {
        console.error('Authentication failed - no user ID available')
        throw new Error('User not authenticated. Please sign in and try again.')
      }
    }

    // Check order cooldown to prevent rapid-fire orders
    const now = Date.now()
    if (now - this.lastOrderTime < this.ORDER_COOLDOWN) {
      const remainingTime = this.ORDER_COOLDOWN - (now - this.lastOrderTime)
      throw new Error(`Please wait ${Math.ceil(remainingTime / 1000)} second(s) before placing another order`)
    }

    // Get current market price with enhanced real-time fetching for market orders
    let currentPrice: number

    if (orderRequest.type === 'MARKET') {
      // For market orders, ensure we get the most current price
      currentPrice = await this.getCurrentMarketPrice(orderRequest.symbol)
      console.log('Market order - using real-time price:', currentPrice, 'for symbol:', orderRequest.symbol)
    } else {
      // For limit orders, use the specified price or fallback to market data
      currentPrice = orderRequest.price || this.state.marketData[orderRequest.symbol]?.price || 0
      console.log('Limit order - using specified price:', currentPrice, 'for symbol:', orderRequest.symbol)
    }

    if (currentPrice <= 0) {
      throw new Error('Unable to get current market price. Please check your connection and try again.')
    }

    // Calculate order requirements
    const orderValue = orderRequest.quantity * currentPrice
    const requiredMargin = orderValue / (orderRequest.leverage || 10)
    const commission = orderValue * 0.001 // 0.1% commission

    // Get current account state and validate balance more strictly
    const userBalance = userService.getUserBalance()
    const currentMargin = this.state.positions.reduce((sum, pos) => sum + (pos.margin || 0), 0)
    const pendingOrderMargin = this.state.orders
      .filter(order => order.status === 'NEW')
      .reduce((sum, order) => {
        const price = this.state.marketData[order.symbol]?.price || order.price
        const value = order.origQty * price
        return sum + (value / (order.leverage || 10))
      }, 0)

    const totalUsedMargin = currentMargin + pendingOrderMargin
    const availableBalance = userBalance - totalUsedMargin
    const totalRequired = requiredMargin + commission

    console.log('Balance Validation:', {
      userBalance,
      currentMargin,
      pendingOrderMargin,
      totalUsedMargin,
      availableBalance,
      requiredMargin,
      commission,
      totalRequired,
      orderValue,
      leverage: orderRequest.leverage || 10
    })

    if (totalRequired > availableBalance) {
      throw new Error(`Insufficient balance. Required: ${totalRequired.toFixed(2)} USDT, Available: ${availableBalance.toFixed(2)} USDT`)
    }

    // Additional safety check - prevent orders if balance is too low
    if (availableBalance < 10) { // Minimum 10 USDT required
      throw new Error('Insufficient balance. Minimum 10 USDT required for trading.')
    }

    const orderId = `ord_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create order object
    const order: Order = {
      id: orderId,
      symbol: orderRequest.symbol,
      side: orderRequest.side,
      type: orderRequest.type,
      origQty: orderRequest.quantity,
      executedQty: 0,
      price: orderRequest.price || currentPrice,
      status: 'NEW',
      timestamp: Date.now(),
      leverage: orderRequest.leverage || 10
    }

    // Update last order time
    this.lastOrderTime = now

    try {
      // Handle market orders vs limit orders differently
      if (orderRequest.type === 'MARKET') {
        // For market orders: Execute immediately without adding to orders list
        console.log('Processing MARKET order - executing immediately')

        // Execute the order immediately (this will create a position)
        await this.executeMarketOrder(order, currentPrice)

        // Save the executed order to database for history (but not in orders collection)
        const executedOrderRef = ref(realtimeDb, `users/${this.userId}/executed_orders/${orderId}`)
        await set(executedOrderRef, {
          ...order,
          status: 'FILLED',
          executedQty: order.origQty,
          executionPrice: currentPrice,
          createdAt: serverTimestamp(),
          executedAt: serverTimestamp()
        })
      } else {
        // For limit orders: Add to orders list and wait for execution
        console.log('Processing LIMIT order - adding to open orders')

        // Add order to local state for limit orders only
        this.state.orders.push(order)
        this.updateAccountInfo()
        this.notifySubscribers()

        // Add order to Realtime Database
        const orderRef = ref(realtimeDb, `users/${this.userId}/orders/${orderId}`)
        await set(orderRef, {
          ...order,
          createdAt: serverTimestamp()
        })
      }

      // Show immediate notification
      notificationService.createTradeNotification(this.userId, 'order_placed', {
        symbol: orderRequest.symbol,
        side: orderRequest.side,
        type: orderRequest.type,
        price: orderRequest.price || currentPrice,
        quantity: orderRequest.quantity
      })

      return orderId
    } catch (error) {
      // Remove from local state if Firebase operation failed
      this.state.orders = this.state.orders.filter(o => o.id !== orderId)
      this.updateAccountInfo()
      this.notifySubscribers()
      throw error
    }
  }

  // Execute a market order immediately (create position without adding to orders)
  private async executeMarketOrder(order: Order, executionPrice: number): Promise<void> {
    if (!this.userId) return

    const commission = order.origQty * executionPrice * 0.001
    const margin = (order.origQty * executionPrice) / order.leverage

    // Final balance validation before execution using cached data
    const userBalance = userService.getUser()?.balance?.current || userService.getUserBalance()
    const currentMargin = this.state.positions.reduce((sum, pos) => sum + (pos.margin || 0), 0)
    const availableBalance = userBalance - currentMargin
    const totalRequired = margin + commission

    console.log('Market Order Execution Balance Check:', {
      userBalance,
      currentMargin,
      availableBalance,
      margin,
      commission,
      totalRequired,
      orderId: order.id
    })

    if (totalRequired > availableBalance) {
      throw new Error(`Market order execution failed: Insufficient balance. Required: ${totalRequired.toFixed(2)} USDT, Available: ${availableBalance.toFixed(2)} USDT`)
    }

    const positionId = `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create position object
    const position: Position = {
      id: positionId,
      symbol: order.symbol,
      side: order.side === 'BUY' ? 'LONG' : 'SHORT',
      entryPrice: executionPrice,
      markPrice: executionPrice,
      size: order.origQty,
      margin: margin,
      leverage: order.leverage,
      pnl: 0,
      pnlPercent: 0,
      liquidationPrice: this.calculateLiquidationPrice(
        executionPrice,
        order.side === 'BUY' ? 'LONG' : 'SHORT',
        order.leverage
      ),
      timestamp: Date.now(),
      orderId: order.id
    }

    // Add position to local state immediately
    this.state.positions.push(position)
    this.updateAccountInfo()
    this.notifySubscribers()

    // Show immediate notification
    notificationService.createTradeNotification(this.userId, 'position_opened', {
      symbol: order.symbol,
      side: position.side,
      size: order.origQty,
      entryPrice: executionPrice
    })

    try {
      // Save position to Realtime Database
      const positionRef = ref(realtimeDb, `users/${this.userId}/positions/${positionId}`)
      await set(positionRef, {
        ...position,
        createdAt: serverTimestamp()
      })

      // Update user balance (deduct commission)
      const currentBalance = userService.getUserBalance()
      await userService.updateBalance(
        currentBalance - commission,
        'commission',
        `Trading commission: ${commission.toFixed(2)} USDT`
      )

      console.log('Market order executed successfully:', {
        orderId: order.id,
        positionId,
        symbol: order.symbol,
        side: position.side,
        size: order.origQty,
        entryPrice: executionPrice
      })

    } catch (error) {
      console.error('Error saving market order execution to Firebase:', error)
      // Remove position from local state if Firebase operation failed
      this.state.positions = this.state.positions.filter(p => p.id !== positionId)
      this.updateAccountInfo()
      this.notifySubscribers()
      throw error
    }
  }

  // Execute a limit order (create position and update order status)
  private async executeOrder(orderId: string, order: Order, executionPrice: number): Promise<void> {
    if (!this.userId) return

    const commission = order.origQty * executionPrice * 0.001
    const margin = (order.origQty * executionPrice) / order.leverage

    // Final balance validation before execution using cached data
    const userBalance = userService.getUser()?.balance?.current || userService.getUserBalance()
    const currentMargin = this.state.positions.reduce((sum, pos) => sum + (pos.margin || 0), 0)
    const availableBalance = userBalance - currentMargin
    const totalRequired = margin + commission

    console.log('Limit Order Execution Balance Check:', {
      userBalance,
      currentMargin,
      availableBalance,
      margin,
      commission,
      totalRequired,
      orderId
    })

    if (totalRequired > availableBalance) {
      // Remove the order from local state
      this.state.orders = this.state.orders.filter(o => o.id !== orderId)
      this.updateAccountInfo()
      this.notifySubscribers()
      throw new Error(`Limit order execution failed: Insufficient balance. Required: ${totalRequired.toFixed(2)} USDT, Available: ${availableBalance.toFixed(2)} USDT`)
    }

    const positionId = `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create position object
    const position: Position = {
      id: positionId,
      symbol: order.symbol,
      side: order.side === 'BUY' ? 'LONG' : 'SHORT',
      entryPrice: executionPrice,
      markPrice: executionPrice,
      size: order.origQty,
      margin: margin,
      leverage: order.leverage,
      pnl: 0,
      pnlPercent: 0,
      liquidationPrice: this.calculateLiquidationPrice(
        executionPrice,
        order.side === 'BUY' ? 'LONG' : 'SHORT',
        order.leverage
      ),
      timestamp: Date.now(),
      orderId: orderId
    }

    // Add position to local state immediately
    this.state.positions.push(position)

    // Remove the filled order from orders array (limit orders only)
    this.state.orders = this.state.orders.filter(o => o.id !== orderId)

    this.updateAccountInfo()
    this.notifySubscribers()

    // Show immediate notification
    notificationService.createTradeNotification(this.userId, 'position_opened', {
      symbol: order.symbol,
      side: position.side,
      size: order.origQty,
      entryPrice: executionPrice
    })

    try {
      // Save to Realtime Database in parallel
      const positionRef = ref(realtimeDb, `users/${this.userId}/positions/${positionId}`)
      const orderRef = ref(realtimeDb, `users/${this.userId}/orders/${orderId}`)

      await Promise.all([
        set(positionRef, {
          ...position,
          createdAt: serverTimestamp()
        }),
        // Remove the order from database since it's filled
        remove(orderRef)
      ])

      // Update user balance (deduct commission)
      const currentBalance = userService.getUserBalance()
      await userService.updateBalance(
        currentBalance - commission,
        'commission',
        `Trading commission: ${commission.toFixed(2)} USDT`
      )

      console.log('Limit order executed successfully:', {
        orderId,
        positionId,
        symbol: order.symbol,
        side: position.side,
        size: order.origQty,
        entryPrice: executionPrice
      })

    } catch (error) {
      console.error('Error saving position to Firebase:', error)
      // Position is already in local state, so user sees it immediately
      // Firebase sync will happen eventually
    }
  }

  // Close a position with immediate UI feedback
  async closePosition(positionId: string): Promise<void> {
    if (!this.userId) return

    const positionIndex = this.state.positions.findIndex(p => p.id === positionId)
    if (positionIndex === -1) return

    const position = this.state.positions[positionIndex]

    // Get real-time price for position closing
    let currentPrice: number
    try {
      currentPrice = await this.getCurrentMarketPrice(position.symbol)
      console.log('Position close - using real-time price:', currentPrice, 'for symbol:', position.symbol)
    } catch (error) {
      console.warn('Failed to get real-time price for position close, using fallback:', error.message)
      currentPrice = this.state.marketData[position.symbol]?.price || position.markPrice
    }

    const commission = position.size * currentPrice * 0.001

    // Remove position from local state immediately
    this.state.positions.splice(positionIndex, 1)
    this.updateAccountInfo()
    this.notifySubscribers()

    // Show immediate notification
    notificationService.createTradeNotification(this.userId, 'position_closed', {
      symbol: position.symbol,
      side: position.side,
      pnl: position.pnl,
      closePrice: currentPrice
    })

    try {
      // Create trade record
      const tradeId = `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const trade: Trade = {
        id: tradeId,
        symbol: position.symbol,
        side: position.side === 'LONG' ? 'SELL' : 'BUY',
        price: currentPrice,
        quantity: position.size,
        commission: commission,
        realizedPnl: position.pnl,
        timestamp: Date.now(),
        leverage: position.leverage,
        orderId: position.orderId || '',
        positionId: positionId
      }

      // Add trade to local state
      this.state.trades.unshift(trade)
      this.notifySubscribers()

      // Remove from Realtime Database and add trade
      const positionRef = ref(realtimeDb, `users/${this.userId}/positions/${positionId}`)
      const tradeRef = ref(realtimeDb, `users/${this.userId}/trades/${tradeId}`)

      await Promise.all([
        remove(positionRef),
        set(tradeRef, {
          ...trade,
          createdAt: serverTimestamp()
        })
      ])

      // Update user balance
      const currentBalance = userService.getUserBalance()
      await userService.updateBalance(
        currentBalance + position.pnl - commission,
        position.pnl > 0 ? 'trade_profit' : 'trade_loss',
        `Position closed: ${position.pnl > 0 ? '+' : ''}${position.pnl.toFixed(2)} USDT`
      )

      // Trade saved to Realtime Database only for optimal performance

    } catch (error) {
      console.error('Error closing position in Firebase:', error)
      // Re-add position to local state if Firebase operation failed
      this.state.positions.push(position)
      this.updateAccountInfo()
      this.notifySubscribers()
      throw error
    }
  }

  // Cancel order
  async cancelOrder(orderId: string): Promise<void> {
    if (!this.userId) {
      throw new Error('User not authenticated')
    }

    try {
      // Remove from Realtime Database
      await remove(ref(realtimeDb, `users/${this.userId}/orders/${orderId}`))

      // Update local state
      this.state.orders = this.state.orders.filter(order => order.id !== orderId)
      this.notifySubscribers()

      console.log('Order canceled successfully:', orderId)
    } catch (error) {
      console.error('Error canceling order:', error)
      throw error
    }
  }

  // Get current state
  getState(): TradingState {
    return { ...this.state }
  }

  // Get market data for a symbol
  getMarketData(symbol: string): MarketData | null {
    return this.state.marketData[symbol] || null
  }

  // Get current market price with real-time fetching for market orders
  private async getCurrentMarketPrice(symbol: string): Promise<number> {
    // First try to get from local market data
    const localPrice = this.state.marketData[symbol]?.price
    const localTimestamp = this.state.marketData[symbol]?.timestamp
    const now = Date.now()

    // If we have recent data (less than 5 seconds old), use it
    if (localPrice && localTimestamp && (now - localTimestamp) < 5000) {
      console.log('Using cached market price:', localPrice, 'for', symbol, 'age:', now - localTimestamp, 'ms')
      return localPrice
    }

    // Otherwise, fetch fresh price from Binance API
    console.log('Fetching fresh market price for', symbol)
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 3000) // 3 second timeout

      const response = await fetch(`https://fapi.binance.com/fapi/v1/ticker/price?symbol=${symbol}`, {
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      const freshPrice = parseFloat(data.price)

      if (freshPrice && freshPrice > 0) {
        // Update local market data with fresh price
        this.updateMarketData(symbol, {
          price: freshPrice,
          timestamp: now
        })

        console.log('Fresh market price fetched:', freshPrice, 'for', symbol)
        return freshPrice
      } else {
        throw new Error('Invalid price data received')
      }
    } catch (error) {
      console.warn('Failed to fetch fresh price for', symbol, ':', error.message)

      // Fallback to local price if available, even if old
      if (localPrice && localPrice > 0) {
        console.log('Using fallback local price:', localPrice, 'for', symbol)
        return localPrice
      }

      throw new Error(`Unable to get current market price for ${symbol}. Please check your connection and try again.`)
    }
  }

  private cleanup() {
    console.log('Cleaning up realtime trading service')

    // Clean up listeners
    this.cleanupListeners()

    // Clear any pending timeouts
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout)
      this.updateTimeout = null
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    // Reset state
    this.userId = null
    this.isInitializing = false
    this.initializationPromise = null

    this.state = {
      positions: [],
      orders: [],
      trades: [],
      accountInfo: null,
      marketData: {},
      isLoading: false,
      error: null,
      connectionStatus: 'disconnected'
    }

    this.notifySubscribers()
    console.log('Realtime trading service cleanup completed')
  }

  // Public method to force reconnection
  async forceReconnect(): Promise<void> {
    console.log('Force reconnect requested')
    const currentUserId = this.userId

    if (currentUserId) {
      this.cleanup()
      await this.initializeForUser(currentUserId)
    } else {
      console.log('No user to reconnect for')
    }
  }

  // Public method to get connection status
  getConnectionStatus(): string {
    return this.state.connectionStatus
  }
}

// Export singleton instance
export const realtimeTradingService = new RealtimeTradingService()
export default realtimeTradingService
