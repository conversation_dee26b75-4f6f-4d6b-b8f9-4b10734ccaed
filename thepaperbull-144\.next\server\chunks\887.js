exports.id=887,exports.ids=[887],exports.modules={38004:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a}),r(37413);var s=r(31658);let i={runtime:"edge",dynamic:"force-static",alt:"Cryptocurrency Trading Dashboard",size:{width:1200,height:630},contentType:"image/png"};async function a(e){let{__metadata_id__:t,...r}=await e.params,a=(0,s.fillMetadataSegment)(".",r,"opengraph-image"),{generateImageMetadata:o}=i;function n(e,t){let r={alt:e.alt,type:e.contentType||"image/png",url:a+(t?"/"+t:"")+"?53e13af1384a38e6"},{size:s}=e;return s&&(r.width=s.width,r.height=s.height),r}return o?(await o({params:r})).map((e,t)=>{let r=(e.id||t)+"";return n(e,r)}):[n(i,"")]}},47155:(e,t,r)=>{"use strict";r.d(t,{rM:()=>h,fx:()=>p});var s=r(60687),i=r(43210),a=r(90555),o=r(80043),n=r(21643),l=r(70695);class c{constructor(){this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null,connectionStatus:"disconnected"},this.subscribers=[],this.userId=null,this.unsubscribeFunctions=[],this.lastOrderTime=0,this.ORDER_COOLDOWN=1e3,this.updateTimeout=null,this.reconnectTimeout=null,this.initializationPromise=null,this.isInitializing=!1,n.A.subscribe(e=>{console.log("Realtime Trading Service - User state changed:",{hasUser:!!e,userId:e?.id,currentUserId:this.userId,isInitializing:this.isInitializing,userStructure:e?Object.keys(e):null}),e&&e.id?this.userId!==e.id?(console.log("Initializing realtime trading service for user:",e.id),this.initializeForUser(e.id)):this.userId===e.id&&"disconnected"===this.state.connectionStatus&&(console.log("Reconnecting realtime trading service for existing user:",e.id),this.reconnectForUser(e.id)):(console.log("User logged out, cleaning up realtime trading service"),this.cleanup())}),this.checkForExistingUser()}async checkForExistingUser(){let e=0,t=async()=>{e++;let t=n.A.getUser(),r=n.A.getFirebaseUser();return(console.log(`User check attempt ${e}:`,{currentUser:t?{id:t.id,email:t.email}:null,firebaseUser:r?{uid:r.uid,email:r.email}:null,hasUserId:!!this.userId}),t&&t.id&&!this.userId)?(console.log("Found existing user on startup, initializing:",t.id),await this.initializeForUser(t.id),!0):!r||this.userId||t?!!this.userId:(console.log("Firebase user exists, waiting for user service to load..."),!1)};if(!await t()){for(let e=1;e<5;e++)if(await new Promise(t=>setTimeout(t,500*e)),await t())return;console.log("No user found after all attempts, service will initialize when user logs in")}}subscribe(e){return this.subscribers.push(e),e(this.state),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.state))}notifySubscribersDebounced(){this.updateTimeout&&clearTimeout(this.updateTimeout),this.updateTimeout=setTimeout(()=>{this.notifySubscribers()},100)}async initializeForUser(e){if(this.isInitializing&&this.initializationPromise)return console.log("Already initializing, waiting for completion..."),this.initializationPromise;this.isInitializing=!0,this.initializationPromise=this._doInitializeForUser(e);try{await this.initializationPromise}finally{this.isInitializing=!1,this.initializationPromise=null}}async _doInitializeForUser(e){console.log("Starting initialization for user:",e),this.userId&&this.userId!==e&&(console.log("Cleaning up previous user connections"),this.cleanup()),this.userId=e,this.state.isLoading=!0,this.state.connectionStatus="connecting",this.state.error=null,this.notifySubscribers();try{await this.initializeAccountInfo(),this.notifySubscribers(),await this.setupRealtimeListeners(),this.state.connectionStatus="connected",this.state.error=null,this.notifySubscribers(),console.log("Realtime trading service initialized successfully for user:",e)}catch(t){throw console.error("Error initializing realtime trading service:",t),this.state.isLoading=!1,this.state.connectionStatus="error",this.state.error="Failed to initialize trading service",this.notifySubscribers(),this.scheduleReconnect(e),t}}async reconnectForUser(e){console.log("Reconnecting for user:",e),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null);try{await this.initializeForUser(e)}catch(e){console.error("Reconnection failed:",e)}}scheduleReconnect(e,t=5e3){this.reconnectTimeout&&clearTimeout(this.reconnectTimeout),console.log(`Scheduling reconnect in ${t}ms for user:`,e),this.reconnectTimeout=setTimeout(()=>{this.userId===e&&(console.log("Attempting scheduled reconnect for user:",e),this.reconnectForUser(e))},t)}async initializeAccountInfo(){if(!this.userId)return;let e=n.A.getUserBalance();this.state.accountInfo={totalWalletBalance:e,totalUnrealizedProfit:0,totalMarginBalance:e,totalPositionInitialMargin:0,totalOpenOrderInitialMargin:0,availableBalance:e,maxWithdrawAmount:e,updateTime:Date.now()}}async setupRealtimeListeners(){if(!this.userId)throw Error("Cannot setup listeners without user ID");console.log("Setting up realtime listeners for user:",this.userId),this.cleanupListeners();try{let e=(0,a.KR)(o.Ye,".info/connected"),t=(0,a.Zy)(e,e=>{let t=e.val();console.log("Firebase connection status:",t),t?(this.state.connectionStatus="connected",this.state.error=null):(this.state.connectionStatus="disconnected",this.state.error="Connection lost",this.scheduleReconnect(this.userId,2e3)),this.notifySubscribers()}),r=(0,a.KR)(o.Ye,`users/${this.userId}/positions`),s=(0,a.Zy)(r,e=>{try{let t=e.val();console.log("Positions data received:",t?Object.keys(t).length:0,"positions"),this.state.positions=t?Object.entries(t).map(([e,t])=>{let r={id:e,...t,timestamp:t.timestamp||Date.now(),entryPrice:Number(t.entryPrice)||0,markPrice:Number(t.markPrice)||Number(t.entryPrice)||0,size:Number(t.size)||0,margin:Number(t.margin)||0,leverage:Number(t.leverage)||10,pnl:Number(t.pnl)||0,pnlPercent:Number(t.pnlPercent)||0,liquidationPrice:Number(t.liquidationPrice)||0},s=this.state.marketData[r.symbol]?.price;return s&&s>0?this.updatePositionPnL(r,s):r}):[],this.state.isLoading=!1,this.updateAccountInfo(),this.notifySubscribers()}catch(e){console.error("Error processing positions data:",e),this.state.isLoading=!1,this.state.error="Failed to process positions data",this.notifySubscribers()}},e=>{console.error("Error listening to positions:",e),this.state.error="Failed to load positions",this.state.connectionStatus="error",this.state.isLoading=!1,this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)}),i=(0,a.KR)(o.Ye,`users/${this.userId}/orders`),n=(0,a.Zy)(i,e=>{try{let t=e.val();console.log("Orders data received:",t?Object.keys(t).length:0,"orders"),this.state.orders=t?Object.entries(t).map(([e,t])=>({id:e,...t,timestamp:t.timestamp||Date.now()})):[],this.updateAccountInfo(),this.notifySubscribers()}catch(e){console.error("Error processing orders data:",e)}},e=>{console.error("Error listening to orders:",e),this.state.error="Failed to load orders",this.state.connectionStatus="error",this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)}),l=(0,a.KR)(o.Ye,`users/${this.userId}/trades`),c=(0,a.Zy)(l,e=>{try{let t=e.val();console.log("Trades data received:",t?Object.keys(t).length:0,"trades"),this.state.trades=t?Object.entries(t).map(([e,t])=>({id:e,...t,timestamp:t.timestamp||Date.now()})).sort((e,t)=>t.timestamp-e.timestamp):[],this.notifySubscribers()}catch(e){console.error("Error processing trades data:",e)}},e=>{console.error("Error listening to trades:",e),this.state.error="Failed to load trade history",this.state.connectionStatus="error",this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)});this.unsubscribeFunctions=[t,()=>(0,a.AU)(r,"value",s),()=>(0,a.AU)(i,"value",n),()=>(0,a.AU)(l,"value",c)],console.log("Realtime listeners setup completed for user:",this.userId)}catch(e){throw console.error("Error setting up realtime listeners:",e),e}}cleanupListeners(){console.log("Cleaning up existing listeners"),this.unsubscribeFunctions.forEach(e=>{try{e()}catch(e){console.error("Error during listener cleanup:",e)}}),this.unsubscribeFunctions=[]}updateAccountInfo(){if(!this.state.accountInfo)return;let e=n.A.getUserBalance(),t=this.state.positions.reduce((e,t)=>e+(t.pnl||0),0),r=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),s=this.state.orders.filter(e=>"NEW"===e.status).reduce((e,t)=>{let r=this.state.marketData[t.symbol]?.price||t.price;return e+t.origQty*r/(t.leverage||10)},0),i=r+s,a=Math.max(0,e-i);this.state.accountInfo.totalWalletBalance=e,this.state.accountInfo.totalUnrealizedProfit=t,this.state.accountInfo.totalPositionInitialMargin=r,this.state.accountInfo.totalOpenOrderInitialMargin=s,this.state.accountInfo.totalMarginBalance=e+t,this.state.accountInfo.availableBalance=a,this.state.accountInfo.maxWithdrawAmount=a,this.state.accountInfo.updateTime=Date.now(),console.log("Account Info Updated:",{userBalance:e,totalMargin:r,totalOrderMargin:s,totalUsedMargin:i,availableBalance:a,positionsCount:this.state.positions.length,ordersCount:this.state.orders.filter(e=>"NEW"===e.status).length})}updateMarketData(e,t){let r={...t,timestamp:t.timestamp||Date.now()};if(this.state.marketData[e]={...this.state.marketData[e],...r},t.price){let r=!1;this.state.positions=this.state.positions.map(s=>{if(s.symbol===e){let e=this.updatePositionPnL(s,t.price);return(e.pnl!==s.pnl||e.markPrice!==s.markPrice)&&(r=!0),e}return s}),r&&(this.updateAccountInfo(),this.notifySubscribers())}}calculatePnL(e,t){if(!e||!t||t<=0||e.entryPrice<=0||e.size<=0)return{pnl:0,pnlPercent:0};let r=("LONG"===e.side?t-e.entryPrice:e.entryPrice-t)*e.size,s=e.margin>0?r/e.margin*100:0,i=isFinite(r)?r:0,a=isFinite(s)?s:0;return{pnl:Number(i.toFixed(2)),pnlPercent:Number(a.toFixed(2))}}updatePositionPnL(e,t){let{pnl:r,pnlPercent:s}=this.calculatePnL(e,t);return{...e,markPrice:t,pnl:r,pnlPercent:s}}calculateLiquidationPrice(e,t,r){let s=.995-1/r;return"LONG"===t?e*s:e*(2-s)}async placeOrder(e){let t;if(!this.userId){let e=n.A.getFirebaseUser(),t=n.A.getUser();if(console.log("Authentication check in placeOrder:",{realtimeServiceUserId:this.userId,firebaseUser:e?{uid:e.uid,email:e.email}:null,userServiceUser:t?{id:t.id,email:t.email}:null}),e)console.log("Using Firebase user ID as fallback:",e.uid),this.userId=e.uid,await this.initializeForUser(e.uid);else if(t&&t.id)console.log("Using user service ID as fallback:",t.id),this.userId=t.id,await this.initializeForUser(t.id);else throw console.error("Authentication failed - no user ID available"),Error("User not authenticated. Please sign in and try again.")}let r=Date.now();if(r-this.lastOrderTime<this.ORDER_COOLDOWN){let e=this.ORDER_COOLDOWN-(r-this.lastOrderTime);throw Error(`Please wait ${Math.ceil(e/1e3)} second(s) before placing another order`)}if("MARKET"===e.type?console.log("Market order - using real-time price:",t=await this.getCurrentMarketPrice(e.symbol),"for symbol:",e.symbol):console.log("Limit order - using specified price:",t=e.price||this.state.marketData[e.symbol]?.price||0,"for symbol:",e.symbol),t<=0)throw Error("Unable to get current market price. Please check your connection and try again.");let s=e.quantity*t,i=s/(e.leverage||10),c=.001*s,d=n.A.getUserBalance(),u=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),m=this.state.orders.filter(e=>"NEW"===e.status).reduce((e,t)=>{let r=this.state.marketData[t.symbol]?.price||t.price;return e+t.origQty*r/(t.leverage||10)},0),h=u+m,p=d-h,g=i+c;if(console.log("Balance Validation:",{userBalance:d,currentMargin:u,pendingOrderMargin:m,totalUsedMargin:h,availableBalance:p,requiredMargin:i,commission:c,totalRequired:g,orderValue:s,leverage:e.leverage||10}),g>p)throw Error(`Insufficient balance. Required: ${g.toFixed(2)} USDT, Available: ${p.toFixed(2)} USDT`);if(p<10)throw Error("Insufficient balance. Minimum 10 USDT required for trading.");let x=`ord_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,b={id:x,symbol:e.symbol,side:e.side,type:e.type,origQty:e.quantity,executedQty:0,price:e.price||t,status:"NEW",timestamp:Date.now(),leverage:e.leverage||10};this.lastOrderTime=r;try{if("MARKET"===e.type){console.log("Processing MARKET order - executing immediately"),await this.executeMarketOrder(b,t);let e=(0,a.KR)(o.Ye,`users/${this.userId}/executed_orders/${x}`);await (0,a.hZ)(e,{...b,status:"FILLED",executedQty:b.origQty,executionPrice:t,createdAt:(0,a.O5)(),executedAt:(0,a.O5)()})}else{console.log("Processing LIMIT order - adding to open orders"),this.state.orders.push(b),this.updateAccountInfo(),this.notifySubscribers();let e=(0,a.KR)(o.Ye,`users/${this.userId}/orders/${x}`);await (0,a.hZ)(e,{...b,createdAt:(0,a.O5)()})}return l.l.createTradeNotification(this.userId,"order_placed",{symbol:e.symbol,side:e.side,type:e.type,price:e.price||t,quantity:e.quantity}),x}catch(e){throw this.state.orders=this.state.orders.filter(e=>e.id!==x),this.updateAccountInfo(),this.notifySubscribers(),e}}async executeMarketOrder(e,t){if(!this.userId)return;let r=e.origQty*t*.001,s=e.origQty*t/e.leverage,i=n.A.getUser()?.balance?.current||n.A.getUserBalance(),c=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),d=i-c,u=s+r;if(console.log("Market Order Execution Balance Check:",{userBalance:i,currentMargin:c,availableBalance:d,margin:s,commission:r,totalRequired:u,orderId:e.id}),u>d)throw Error(`Market order execution failed: Insufficient balance. Required: ${u.toFixed(2)} USDT, Available: ${d.toFixed(2)} USDT`);let m=`pos_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,h={id:m,symbol:e.symbol,side:"BUY"===e.side?"LONG":"SHORT",entryPrice:t,markPrice:t,size:e.origQty,margin:s,leverage:e.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(t,"BUY"===e.side?"LONG":"SHORT",e.leverage),timestamp:Date.now(),orderId:e.id};this.state.positions.push(h),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_opened",{symbol:e.symbol,side:h.side,size:e.origQty,entryPrice:t});try{let s=(0,a.KR)(o.Ye,`users/${this.userId}/positions/${m}`);await (0,a.hZ)(s,{...h,createdAt:(0,a.O5)()});let i=n.A.getUserBalance();await n.A.updateBalance(i-r,"commission",`Trading commission: ${r.toFixed(2)} USDT`),console.log("Market order executed successfully:",{orderId:e.id,positionId:m,symbol:e.symbol,side:h.side,size:e.origQty,entryPrice:t})}catch(e){throw console.error("Error saving market order execution to Firebase:",e),this.state.positions=this.state.positions.filter(e=>e.id!==m),this.updateAccountInfo(),this.notifySubscribers(),e}}async executeOrder(e,t,r){if(!this.userId)return;let s=t.origQty*r*.001,i=t.origQty*r/t.leverage,c=n.A.getUser()?.balance?.current||n.A.getUserBalance(),d=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),u=c-d,m=i+s;if(console.log("Limit Order Execution Balance Check:",{userBalance:c,currentMargin:d,availableBalance:u,margin:i,commission:s,totalRequired:m,orderId:e}),m>u)throw this.state.orders=this.state.orders.filter(t=>t.id!==e),this.updateAccountInfo(),this.notifySubscribers(),Error(`Limit order execution failed: Insufficient balance. Required: ${m.toFixed(2)} USDT, Available: ${u.toFixed(2)} USDT`);let h=`pos_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,p={id:h,symbol:t.symbol,side:"BUY"===t.side?"LONG":"SHORT",entryPrice:r,markPrice:r,size:t.origQty,margin:i,leverage:t.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(r,"BUY"===t.side?"LONG":"SHORT",t.leverage),timestamp:Date.now(),orderId:e};this.state.positions.push(p),this.state.orders=this.state.orders.filter(t=>t.id!==e),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_opened",{symbol:t.symbol,side:p.side,size:t.origQty,entryPrice:r});try{let i=(0,a.KR)(o.Ye,`users/${this.userId}/positions/${h}`),l=(0,a.KR)(o.Ye,`users/${this.userId}/orders/${e}`);await Promise.all([(0,a.hZ)(i,{...p,createdAt:(0,a.O5)()}),(0,a.TF)(l)]);let c=n.A.getUserBalance();await n.A.updateBalance(c-s,"commission",`Trading commission: ${s.toFixed(2)} USDT`),console.log("Limit order executed successfully:",{orderId:e,positionId:h,symbol:t.symbol,side:p.side,size:t.origQty,entryPrice:r})}catch(e){console.error("Error saving position to Firebase:",e)}}async closePosition(e){let t;if(!this.userId)return;let r=this.state.positions.findIndex(t=>t.id===e);if(-1===r)return;let s=this.state.positions[r];try{t=await this.getCurrentMarketPrice(s.symbol),console.log("Position close - using real-time price:",t,"for symbol:",s.symbol)}catch(e){console.warn("Failed to get real-time price for position close, using fallback:",e.message),t=this.state.marketData[s.symbol]?.price||s.markPrice}let i=s.size*t*.001;this.state.positions.splice(r,1),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_closed",{symbol:s.symbol,side:s.side,pnl:s.pnl,closePrice:t});try{let r=`trade_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,l={id:r,symbol:s.symbol,side:"LONG"===s.side?"SELL":"BUY",price:t,quantity:s.size,commission:i,realizedPnl:s.pnl,timestamp:Date.now(),leverage:s.leverage,orderId:s.orderId||"",positionId:e};this.state.trades.unshift(l),this.notifySubscribers();let c=(0,a.KR)(o.Ye,`users/${this.userId}/positions/${e}`),d=(0,a.KR)(o.Ye,`users/${this.userId}/trades/${r}`);await Promise.all([(0,a.TF)(c),(0,a.hZ)(d,{...l,createdAt:(0,a.O5)()})]);let u=n.A.getUserBalance();await n.A.updateBalance(u+s.pnl-i,s.pnl>0?"trade_profit":"trade_loss",`Position closed: ${s.pnl>0?"+":""}${s.pnl.toFixed(2)} USDT`)}catch(e){throw console.error("Error closing position in Firebase:",e),this.state.positions.push(s),this.updateAccountInfo(),this.notifySubscribers(),e}}async cancelOrder(e){if(!this.userId)throw Error("User not authenticated");try{await (0,a.TF)((0,a.KR)(o.Ye,`users/${this.userId}/orders/${e}`)),this.state.orders=this.state.orders.filter(t=>t.id!==e),this.notifySubscribers(),console.log("Order canceled successfully:",e)}catch(e){throw console.error("Error canceling order:",e),e}}getState(){return{...this.state}}getMarketData(e){return this.state.marketData[e]||null}async getCurrentMarketPrice(e){let t=this.state.marketData[e]?.price,r=this.state.marketData[e]?.timestamp,s=Date.now();if(t&&r&&s-r<5e3)return console.log("Using cached market price:",t,"for",e,"age:",s-r,"ms"),t;console.log("Fetching fresh market price for",e);try{let t=new AbortController,r=setTimeout(()=>t.abort(),3e3),i=await fetch(`https://fapi.binance.com/fapi/v1/ticker/price?symbol=${e}`,{signal:t.signal});if(clearTimeout(r),!i.ok)throw Error(`HTTP error! status: ${i.status}`);let a=await i.json(),o=parseFloat(a.price);if(o&&o>0)return this.updateMarketData(e,{price:o,timestamp:s}),console.log("Fresh market price fetched:",o,"for",e),o;throw Error("Invalid price data received")}catch(r){if(console.warn("Failed to fetch fresh price for",e,":",r.message),t&&t>0)return console.log("Using fallback local price:",t,"for",e),t;throw Error(`Unable to get current market price for ${e}. Please check your connection and try again.`)}}cleanup(){console.log("Cleaning up realtime trading service"),this.cleanupListeners(),this.updateTimeout&&(clearTimeout(this.updateTimeout),this.updateTimeout=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.userId=null,this.isInitializing=!1,this.initializationPromise=null,this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null,connectionStatus:"disconnected"},this.notifySubscribers(),console.log("Realtime trading service cleanup completed")}async forceReconnect(){console.log("Force reconnect requested");let e=this.userId;e?(this.cleanup(),await this.initializeForUser(e)):console.log("No user to reconnect for")}getConnectionStatus(){return this.state.connectionStatus}}let d=new c;class u{constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(e=>{this.simulators.set(e.symbol,{symbol:e.symbol,basePrice:e.basePrice,volatility:e.volatility,trend:(Math.random()-.5)*.001,lastPrice:e.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},1e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(e=>{let t=Date.now(),r=(t-e.lastUpdate)/1e3,s=(Math.random()-.5)*e.volatility*r,i=e.trend*r,a=e.lastPrice*(1+(s+i)),o=a-e.lastPrice,n=o/e.lastPrice*100;.01>Math.random()&&(e.trend=(Math.random()-.5)*.001),e.lastPrice=a,e.lastUpdate=t;let l={symbol:e.symbol,price:a,priceChange:o,priceChangePercent:n,volume:1e6*Math.random(),timestamp:t};this.notifySubscribers(e.symbol,l)})}subscribe(e){return this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(e,t){this.subscribers.forEach(r=>r(e,t))}getCurrentPrice(e){let t=this.simulators.get(e);return t?t.lastPrice:null}addSymbol(e,t,r=.03){this.simulators.has(e)||this.simulators.set(e,{symbol:e,basePrice:t,volatility:r,trend:(Math.random()-.5)*.001,lastPrice:t,lastUpdate:Date.now()})}removeSymbol(e){this.simulators.delete(e)}}new u;let m=(0,i.createContext)(void 0);function h({children:e}){let[t,r]=(0,i.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null}),a=(0,i.useCallback)(async e=>{try{return await d.placeOrder(e)}catch(e){throw console.error("Failed to place order:",e),e}},[]),o=(0,i.useCallback)(async e=>{try{return console.log("Cancel order not implemented yet:",e),!0}catch(e){throw console.error("Failed to cancel order:",e),e}},[]),n=(0,i.useCallback)(async e=>{try{return await d.closePosition(e),!0}catch(e){throw console.error("Failed to close position:",e),e}},[]),l=(0,i.useCallback)(async e=>{try{return console.log("Position update not implemented in Firebase service yet:",e),!0}catch(e){throw console.error("Failed to update position:",e),e}},[]),c=(0,i.useCallback)((e,t)=>{d.updateMarketData(e,t)},[]),u=(0,i.useCallback)(e=>{console.log("Account info update not needed with Realtime service:",e)},[]),h=(0,i.useCallback)(()=>{r(e=>({...e,error:null}))},[]),p=(0,i.useCallback)(e=>d.getMarketData(e),[]),g=(0,i.useCallback)(e=>t.positions.find(t=>t.symbol===e)||null,[t.positions]),x=(0,i.useCallback)(()=>t.accountInfo,[t.accountInfo]),b=(0,i.useCallback)(()=>t.positions.reduce((e,t)=>e+t.pnl,0),[t.positions]),f=(0,i.useCallback)(()=>t.positions.reduce((e,t)=>e+t.margin,0),[t.positions]),y=(0,i.useCallback)(()=>t.accountInfo?.availableBalance||0,[t.accountInfo]),v={positions:t.positions,orders:t.orders,trades:t.trades,marketData:t.marketData,accountInfo:t.accountInfo,isLoading:t.isLoading,error:t.error,state:t,placeOrder:a,cancelOrder:o,closePosition:n,updatePosition:l,updateMarketData:c,updateAccountInfo:u,clearError:h,getMarketData:p,getPositionBySymbol:g,getAccountInfo:x,getTotalPnL:b,getTotalMargin:f,getAvailableBalance:y};return(0,s.jsx)(m.Provider,{value:v,children:e})}function p(){let e=(0,i.useContext)(m);if(void 0===e)throw Error("useTrading must be used within a TradingProvider");return e}},50579:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>B});var s=r(60687),i=r(43210),a=r(16189),o=r(85814),n=r.n(o),l=r(32192),c=r(20835),d=r(3589),u=r(78272),m=r(35583),h=r(53411),p=r(58887),g=r(84027),x=r(40083),b=r(25878),f=r(21134),y=r(363);function v(){let{theme:e,setTheme:t}=(0,b.D)();return(0,s.jsx)("button",{onClick:()=>t("dark"===e?"light":"dark"),className:"relative p-2.5 rounded-xl bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 hover:from-primary/20 hover:via-primary/10 hover:to-primary/20 border border-primary/20 hover:border-primary/30 transition-all duration-300 group shadow-md hover:shadow-lg","aria-label":"dark"===e?"Switch to light theme":"Switch to dark theme",children:(0,s.jsx)("div",{className:"relative",children:"dark"===e?(0,s.jsx)(f.A,{className:"h-4 w-4 text-yellow-500 group-hover:scale-110 group-hover:rotate-180 transition-all duration-300"}):(0,s.jsx)(y.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300"})})})}function w({onCloseMobile:e}){let t=(0,a.usePathname)(),[o,b]=(0,i.useState)("trading"),f=e=>{b(o===e?null:e)},y=()=>{e&&e()};return(0,s.jsxs)("div",{className:"h-full w-full flex flex-col bg-gradient-to-br from-card via-card/98 to-muted/20 text-card-foreground",children:[(0,s.jsx)("div",{className:"flex-1 overflow-auto py-4",children:(0,s.jsxs)("nav",{className:"px-4 space-y-3",children:[(0,s.jsx)("div",{className:"px-2 mb-4",children:(0,s.jsx)("h3",{className:"text-2xs font-bold text-muted-foreground uppercase tracking-wider",children:"Navigation"})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/trade",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/trade"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/trade"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Trade"}),"/trade"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("button",{className:"group w-full flex items-center justify-between px-4 py-3.5 text-sm rounded-2xl text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg transition-all duration-300",onClick:()=>f("trading"),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"mr-3 p-1.5 rounded-lg group-hover:bg-primary/10 transition-all duration-300",children:(0,s.jsx)(c.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Trading"})]}),"trading"===o?(0,s.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground"}):(0,s.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground"})]}),"trading"===o&&(0,s.jsxs)("div",{className:"mt-3 ml-8 space-y-2 animate-in slide-in-from-top-3 duration-300",children:[(0,s.jsxs)(n(),{href:"/trade",className:`group flex items-center justify-between px-4 py-3 text-sm rounded-xl transition-all duration-300 ${"/trade"===t?"bg-gradient-to-r from-primary/30 via-primary/25 to-primary/20 text-primary font-bold border border-primary/40 shadow-lg shadow-primary/20 scale-105":"text-foreground hover:text-primary hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5 hover:border hover:border-primary/20 hover:shadow-md hover:scale-102 font-semibold"}`,onClick:y,children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/trade"===t?"bg-primary/20 text-primary":"bg-primary/10 text-primary group-hover:bg-primary/15"}`,children:(0,s.jsx)(c.A,{className:"h-3.5 w-3.5"})}),(0,s.jsx)("span",{children:"Futures"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:["/trade"===t&&(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-emerald-500/20 to-emerald-600/20 text-emerald-600 dark:text-emerald-400 px-2.5 py-1 rounded-lg font-bold border border-emerald-500/30 shadow-sm",children:"LIVE"})]})]}),(0,s.jsxs)(n(),{href:"#",className:"group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300",onClick:y,children:[(0,s.jsx)("span",{children:"Margin"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20",children:"Soon"})]}),(0,s.jsxs)(n(),{href:"#",className:"group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300",onClick:y,children:[(0,s.jsx)("span",{children:"Options"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20",children:"Soon"})]})]})]}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/wallet",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/wallet"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/wallet"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Wallet"}),"/wallet"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/statistics",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/statistics"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/statistics"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Statistics"}),"/statistics"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/chat",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/chat"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/chat"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"AI Chat"}),"/chat"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/settings",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/settings"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/settings"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Settings"}),"/settings"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})})]})}),(0,s.jsx)("div",{className:"p-4 border-t border-border/30 bg-gradient-to-br from-muted/30 via-muted/20 to-background/50 backdrop-blur-sm",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-2xl bg-gradient-to-r from-background/60 to-muted/40 border border-border/30",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-responsive-xs font-semibold text-foreground",children:"Theme"})]}),(0,s.jsx)(v,{})]}),(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-primary/25 via-primary/20 to-primary/15 text-primary px-3 py-1.5 sm:px-4 sm:py-2 rounded-2xl text-2xs sm:text-xs font-bold border border-primary/30 shadow-lg shadow-primary/10",children:"BETA VERSION"}),(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full border-2 border-background animate-bounce"})]})}),(0,s.jsxs)("button",{onClick:async()=>{try{let{signOutUser:e}=await Promise.resolve().then(r.bind(r,95216));await e(),localStorage.removeItem("rememberedUsername"),window.location.href="/login"}catch(e){console.error("Logout error:",e),window.location.href="/login"}},className:"touch-target group flex items-center justify-center px-4 py-3.5 text-responsive-xs rounded-2xl bg-gradient-to-r from-red-500/15 via-red-500/10 to-red-600/15 text-red-600 dark:text-red-400 hover:from-red-500/25 hover:via-red-500/20 hover:to-red-600/25 border border-red-500/25 hover:border-red-500/40 transition-all duration-300 w-full shadow-lg hover:shadow-xl hover:scale-105",children:[(0,s.jsx)("div",{className:"mr-3 p-1 rounded-lg bg-red-500/20 group-hover:bg-red-500/30 transition-all duration-300",children:(0,s.jsx)(x.A,{className:"h-4 w-4 group-hover:scale-110 transition-transform duration-300"})}),(0,s.jsx)("span",{className:"font-semibold",children:"Logout"})]})]})})]})}var j=r(93613),N=r(86356),k=r(85778),I=r(65668),S=r(47155),P=r(6955);function A(){let[e,t]=(0,i.useState)(!1),[r,o]=(0,i.useState)(null),[n,l]=(0,i.useState)(!0),[c,d]=(0,i.useState)(null),u=(0,i.useRef)(null),h=(0,a.useRouter)(),{user:p,signOut:b}=(0,P.A)(),{accountInfo:f,getTotalPnL:y,getAvailableBalance:v}=(0,S.fx)(),w=(()=>{let e=r?.balance?.current||1e4,t=f?.totalWalletBalance||1e4,s=e||t||1e4,i=v();if(null==i||0===i){let e=y()||0;i=Math.max(0,s-(f?.totalPositionInitialMargin||0)+e)}return{totalBalance:s,availableBalance:i,totalPnL:y()||0,currency:r?.balance?.currency||"USDT"}})(),A=async()=>{try{await b(),h.push("/login")}catch(e){console.error("Sign out error:",e)}},T=(e="sm")=>n?(0,s.jsx)("div",{className:`${"sm"===e?"w-10 h-10":"w-16 h-16"} bg-muted rounded-full animate-pulse`}):(r?.avatar,(0,s.jsx)("div",{className:`${{sm:"w-10 h-10 text-sm",lg:"w-16 h-16 text-xl"}["sm"===e?"sm":"lg"]} rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-md border-2 border-white dark:border-gray-700 transition-all duration-200 hover:shadow-lg`,children:(0,s.jsx)("span",{className:"select-none",children:"\uD83D\uDC02"})}));return(0,s.jsxs)("div",{className:"relative",ref:u,children:[(0,s.jsx)("button",{className:`rounded-full transition-all duration-200 ${e?"ring-2 ring-primary/30":"hover:scale-105"} ${n?"opacity-75":""}`,onClick:()=>t(!e),disabled:n,children:T("sm")}),e&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-56 rounded-md shadow-xl bg-card ring-1 ring-black/5 dark:ring-white/10 z-[100] animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,s.jsxs)("div",{className:"py-2 px-3 border-b border-border",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[T("xl"),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:r?.name||p?.displayName||"User"}),r?.subscription?.type&&"free"!==r.subscription.type&&(0,s.jsx)("span",{className:"text-xs bg-primary/20 text-primary px-1.5 py-0.5 rounded-md uppercase",children:r.subscription.type})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:r?.email||p?.email||"<EMAIL>"})]})]}),c&&(0,s.jsx)("div",{className:"mt-3 pt-2 border-t border-border/50",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-red-500",children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-xs",children:c})]})}),!c&&(0,s.jsxs)("div",{className:"mt-3 pt-2 border-t border-border/50",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Total Balance"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:"text-sm font-medium",children:[w.totalBalance.toFixed(2)," ",w.currency]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Available"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:"text-sm font-medium",children:[w.availableBalance.toFixed(2)," ",w.currency]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Unrealized PnL"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:`text-sm font-medium ${w.totalPnL>=0?"text-emerald-500":"text-red-500"}`,children:[w.totalPnL>=0?"+":"",w.totalPnL.toFixed(2)," ",w.currency]})]}),r?.balance?.lastUpdated&&(0,s.jsx)("div",{className:"mt-2 pt-1 border-t border-border/30",children:(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Updated: ",new Date(r.balance.lastUpdated).toLocaleTimeString()]})})]})]}),(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),h.push("/settings")},children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"My Profile"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),h.push("/wallet")},children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Wallet"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),h.push("/settings?tab=payment")},children:[(0,s.jsx)(k.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Billing"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),h.push("/settings")},children:[(0,s.jsx)(g.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Settings"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),window.open("mailto:<EMAIL>","_blank")},children:[(0,s.jsx)(I.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Help Center"]})]}),(0,s.jsx)("div",{className:"py-1 border-t border-border",children:(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-muted transition-colors duration-150",onClick:A,children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})})]})]})}r(21643);var T=r(11860),U=r(12941);function D({onToggle:e,isOpen:t}){return(0,s.jsx)("button",{className:"lg:hidden touch-target rounded-md hover:bg-muted transition-colors",onClick:()=>e(!t),"aria-label":t?"Close sidebar":"Open sidebar",children:t?(0,s.jsx)(T.A,{className:"h-6 w-6 text-foreground"}):(0,s.jsx)(U.A,{className:"h-6 w-6 text-foreground"})})}var C=r(25541),z=r(5336),F=r(43649),O=r(96882),$=r(97051),L=r(13964),R=r(70695),E=r(26512);function M({isOpen:e,onToggle:t,onClose:r}){let{user:a}=(0,P.A)(),[o,n]=(0,i.useState)([]),[l,c]=(0,i.useState)(0),d=(0,i.useRef)(null),u=async e=>{await R.A.markAsRead(e)},m=async()=>{await R.A.markAllAsRead()},h=e=>{switch(e){case"trade":return(0,s.jsx)(C.A,{className:"h-4 w-4 text-blue-500"});case"success":return(0,s.jsx)(z.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,s.jsx)(F.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,s.jsx)(F.A,{className:"h-4 w-4 text-red-500"});default:return(0,s.jsx)(O.A,{className:"h-4 w-4 text-blue-500"})}},p=e=>{if(!e)return"Just now";try{let t=e.toDate?e.toDate():new Date(e);return(0,E.m)(t,{addSuffix:!0})}catch(e){return"Just now"}};return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:t,className:"touch-target relative text-muted-foreground hover:text-foreground transition-colors","aria-label":"Notifications",children:[(0,s.jsx)($.A,{className:"h-5 w-5"}),l>0&&(0,s.jsx)("span",{className:"absolute -top-0.5 -right-0.5 bg-primary text-primary-foreground text-2xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse font-semibold",children:l>99?"99+":l})]}),e&&(0,s.jsxs)("div",{ref:d,className:"absolute right-0 mt-2 w-80 max-w-[calc(100vw-2rem)] bg-card rounded-lg shadow-xl border border-border z-[100] animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,s.jsx)("div",{className:"p-3 border-b border-border",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-semibold text-responsive-sm",children:"Notifications"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[l>0&&(0,s.jsx)("button",{onClick:m,className:"text-2xs text-primary hover:text-primary/90 cursor-pointer font-medium",children:"Mark all as read"}),(0,s.jsx)("button",{onClick:r,className:"text-muted-foreground hover:text-foreground p-1 rounded-md hover:bg-muted transition-colors",children:(0,s.jsx)(T.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)("div",{className:"max-h-[400px] overflow-y-auto",children:0===o.length?(0,s.jsxs)("div",{className:"p-6 text-center",children:[(0,s.jsx)($.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-responsive-xs text-muted-foreground",children:"No notifications yet"}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1",children:"You'll see trading updates and system notifications here"})]}):(0,s.jsx)("div",{className:"divide-y divide-border",children:o.slice(0,10).map(e=>(0,s.jsx)("div",{className:`p-3 hover:bg-muted/50 transition-colors ${e.read?"":"bg-primary/5 border-l-2 border-l-primary"}`,children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:h(e.type)}),(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-responsive-xs font-medium text-foreground",children:e.title}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1 line-clamp-2",children:e.message}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1",children:p(e.createdAt)})]}),!e.read&&(0,s.jsx)("button",{onClick:()=>u(e.id),className:"ml-2 p-1 text-muted-foreground hover:text-foreground rounded-md hover:bg-muted transition-colors",title:"Mark as read",children:(0,s.jsx)(L.A,{className:"h-3 w-3"})})]})})]})},e.id))})}),o.length>0&&(0,s.jsx)("div",{className:"p-2 border-t border-border text-center",children:(0,s.jsx)("button",{className:"text-responsive-xs text-primary hover:text-primary/90 font-medium",children:"View all notifications"})})]})]})}function B({children:e}){(0,a.useRouter)();let[t,r]=(0,i.useState)(!0),[o,n]=(0,i.useState)(!1),[l,c]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!0),[m,h]=(0,i.useState)(!1),{user:p,loading:g}=(0,P.A)(),x=(0,i.useRef)(null);return t||g?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("svg",{className:"animate-spin h-12 w-12 text-primary mx-auto",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("p",{className:"mt-4 text-lg font-medium text-foreground",children:"Loading..."})]})}):(0,s.jsx)(S.rM,{children:(0,s.jsx)("div",{className:"min-h-screen w-full bg-background text-foreground",children:(0,s.jsx)("div",{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"bg-card shadow-lg h-full",children:[(0,s.jsxs)("header",{className:"relative flex items-center justify-between px-4 py-2 border-b border-border bg-card/50 backdrop-blur-sm z-50",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"lg:hidden",children:(0,s.jsx)(D,{onToggle:n,isOpen:o})}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h1",{className:"text-primary font-bold text-xl tracking-tight bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent",children:"ThePaperBull"}),(0,s.jsx)("span",{className:"ml-2 text-xs bg-gradient-to-r from-primary/20 to-primary/30 text-primary px-2 py-0.5 rounded-full font-semibold border border-primary/20",children:"BETA"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-5 relative z-50",ref:x,children:[(0,s.jsx)(M,{isOpen:l,onToggle:()=>c(!l),onClose:()=>c(!1)}),(0,s.jsx)(A,{})]})]}),(0,s.jsxs)("div",{className:"relative h-[calc(100vh-60px)]",children:[(0,s.jsx)("main",{className:"w-full h-full overflow-auto bg-background",children:(0,s.jsx)(i.Suspense,{fallback:(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("svg",{className:"animate-spin h-8 w-8 text-primary mx-auto",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading..."})]})}),children:e})}),o&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden",onClick:()=>n(!1)}),d&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm z-40 hidden lg:block",onClick:()=>u(!1)}),(0,s.jsx)("div",{className:`fixed inset-y-0 left-0 z-50 w-80 bg-background border-r border-border shadow-2xl transform transition-transform duration-300 ease-in-out ${o||d?"translate-x-0":"-translate-x-full"}`,style:{top:"60px",height:"calc(100vh - 60px)"},children:(0,s.jsx)(w,{onCloseMobile:()=>{n(!1),u(!1)}})}),(0,s.jsx)("button",{onClick:()=>{window.innerWidth>=1024?u(!d):n(!o)},className:"fixed top-1/2 transform -translate-y-1/2 z-[60] bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-r-xl p-3 shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-105 hover:animate-bounce active:scale-95",style:{left:d||o?"320px":"0px",transition:"left 0.3s ease-in-out, transform 0.2s ease-in-out, box-shadow 0.3s ease-in-out"},"aria-label":d||o?"Hide sidebar":"Show sidebar",children:d||o?(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"group-hover:scale-110 group-hover:rotate-12 transition-all duration-300",children:(0,s.jsx)("path",{d:"m15 18-6-6 6-6"})}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300",children:(0,s.jsx)("path",{d:"m9 18 6-6-6-6"})})})]})]})})})})}},56777:(e,t,r)=>{Promise.resolve().then(r.bind(r,50579))},71934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\(dashboard)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\layout.tsx","default")},75870:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a}),r(37413);var s=r(31658);let i={runtime:"edge",dynamic:"force-static",alt:"Cryptocurrency Trading Dashboard",size:{width:1200,height:630},contentType:"image/png"};async function a(e){let{__metadata_id__:t,...r}=await e.params,a=(0,s.fillMetadataSegment)(".",r,"twitter-image"),{generateImageMetadata:o}=i;function n(e,t){let r={alt:e.alt,type:e.contentType||"image/png",url:a+(t?"/"+t:"")+"?c783d68783a9fc8b"},{size:s}=e;return s&&(r.width=s.width,r.height=s.height),r}return o?(await o({params:r})).map((e,t)=>{let r=(e.id||t)+"";return n(e,r)}):[n(i,"")]}},91625:(e,t,r)=>{Promise.resolve().then(r.bind(r,71934))}};