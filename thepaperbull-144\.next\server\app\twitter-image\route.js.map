{"version": 3, "file": "app/twitter-image/route.js", "mappings": "qFAAA,uDCAA,qIIAA,4aHGO,IAAMA,EAAU,OAAM,EACN,eAAc,EAGlB,mCAAkC,EACjC,CAClBC,MAAO,KACPC,OAAQ,GACV,EAAC,EAE0B,YAAW,eAGRC,IAC5B,OAAO,IAAIC,EAAAA,CAAaA,CACtB,WAACC,MAAAA,CACCC,MAAO,CACLC,SAAU,IACVC,WAAY,QACZP,MAAO,OACPC,OAAQ,OACRO,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,SAAU,UACZ,YAGA,UAACC,MAAAA,CACCC,IAAI,qIACJC,IAAI,2BACJV,MAAO,CACLO,SAAU,WACVI,IAAK,EACLC,KAAM,EACNjB,MAAO,OACPC,OAAQ,OACRiB,UAAW,OACb,IAIF,WAACd,MAAAA,CACCC,MAAO,CACLO,SAAU,WACVO,OAAQ,EACRF,KAAM,EACNjB,MAAO,OACPC,OAAQ,MACRM,WAAY,gDACZC,QAAS,OACTC,cAAe,SACfE,eAAgB,WAChBS,QAAS,MACX,YAEA,UAAChB,MAAAA,CAAIC,MAAO,CAAEC,SAAU,OAAQe,MAAO,QAASC,WAAY,MAAO,WAAG,qBACtE,UAAClB,MAAAA,CAAIC,MAAO,CAAEC,SAAU,OAAQe,MAAO,QAASE,UAAW,MAAO,WAAG,iDAKzE,CAAE,GAAGC,CAAI,EAEb,CChEA,OAAsB,GAAG,CAAQ,EAEjC,YACA,0BAGA,wBACA,yGAMO,0BAKP,EAJA,qBACA,iBAAU,QAA2B,MACrC,aAIA,MACA,eAAwD,SAAoB,EAS5E,SARA,aAMA,kBAXA,IAYK,IAEL,WAAiB,IAAY,cAC7B,UACA,CAAO,CAEP,CAEA,UAAmB,cAAwB,CAC3C,CCnCA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,4BACA,0BACA,yBACA,oCACA,CAAK,CACL,2KACA,iBAVA,SAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,qEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,4BACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,SAAW,MAcL,aAdK,GAA0B,aAAe,kDAAwD,wOAAuQ,4RAAwT,kBAAkB,QAAQ,uEAA+E,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAiI,uBAAyB,8FAA0G,aAAiB,WAAa,sFAA+F,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,0EAA8E,kDAA2D,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,0CAA4C,iNAAoQ,qBAAyB,k/CAA2gD,4ZAiBjuJ,CAAC,CAAC,EAAC", "sources": ["webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./app/twitter-image.tsx", "webpack://_N_E/?63ff", "webpack://_N_E/./app/twitter-image.tsx?cc01", "webpack://_N_E/?174b"], "sourcesContent": ["module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { ImageResponse } from \"next/og\"\n\n// Route segment config\nexport const runtime = \"edge\"\nexport const dynamic = \"force-static\"\n\n// Image metadata\nexport const alt = \"Cryptocurrency Trading Dashboard\"\nexport const size = {\n  width: 1200,\n  height: 630,\n}\n\nexport const contentType = \"image/png\"\n\n// Image generation - reusing the same image for Twitter\nexport default async function Image() {\n  return new ImageResponse(\n    <div\n      style={{\n        fontSize: 128,\n        background: \"white\",\n        width: \"100%\",\n        height: \"100%\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        position: \"relative\",\n      }}\n    >\n      {/* Background image */}\n      <img\n        src=\"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png\"\n        alt=\"Cryptocurrency Dashboard\"\n        style={{\n          position: \"absolute\",\n          top: 0,\n          left: 0,\n          width: \"100%\",\n          height: \"100%\",\n          objectFit: \"cover\",\n        }}\n      />\n\n      {/* Semi-transparent overlay for better text visibility */}\n      <div\n        style={{\n          position: \"absolute\",\n          bottom: 0,\n          left: 0,\n          width: \"100%\",\n          height: \"30%\",\n          background: \"linear-gradient(transparent, rgba(0,0,0,0.7))\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"flex-end\",\n          padding: \"40px\",\n        }}\n      >\n        <div style={{ fontSize: \"48px\", color: \"white\", fontWeight: \"bold\" }}>Crypto Dashboard</div>\n        <div style={{ fontSize: \"24px\", color: \"white\", marginTop: \"10px\" }}>\n          Modern cryptocurrency trading platform\n        </div>\n      </div>\n    </div>,\n    { ...size },\n  )\n}\n", "/* dynamic image route */\nimport { NextResponse } from 'next/server'\nimport * as userland from \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\twitter-image.tsx\"\n\nconst imageModule = { ...userland }\n\nconst handler = imageModule.default\nconst generateImageMetadata = imageModule.generateImageMetadata\n\n\n  if (typeof handler !== 'function') {\n    throw new Error('Default export is missing in \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\twitter-image.tsx\"')\n  }\n  \nexport { runtime, dynamic, alt, size, contentType } from \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\twitter-image.tsx\"\n\n\nexport async function GET(_, ctx) {\n  const params = await ctx.params\n  const { __metadata_id__, ...rest } = params || {}\n  const restParams = params ? rest : undefined\n  const targetId = __metadata_id__\n  let id = undefined\n  \n  if (generateImageMetadata) {\n    const imageMetadata = await generateImageMetadata({ params: restParams })\n    id = imageMetadata.find((item) => {\n      if (process.env.NODE_ENV !== 'production') {\n        if (item?.id == null) {\n          throw new Error('id property is required for every item returned from generateImageMetadata')\n        }\n      }\n      return item.id.toString() === targetId\n    })?.id\n    if (id == null) {\n      return new NextResponse('Not Found', {\n        status: 404,\n      })\n    }\n  }\n\n  return handler({ params: restParams, id })\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"next-metadata-route-loader?filePath=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp%5Ctwitter-image.tsx&isDynamicRouteExtension=1!?__next_metadata_route__\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"export\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/twitter-image/route\",\n        pathname: \"/twitter-image\",\n        filename: \"twitter-image\",\n        bundlePath: \"app/twitter-image/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?filePath=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp%5Ctwitter-image.tsx&isDynamicRouteExtension=1!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Ftwitter-image%2Froute&page=%2Ftwitter-image%2Froute&pagePath=private-next-app-dir%2Ftwitter-image.tsx&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&appPaths=%2Ftwitter-image&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/twitter-image.tsx?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":true,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image/\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[],\"unoptimized\":true},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":true,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"output\":\"export\",\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"D:\\\\step-by-step\\\\thepaperbull-144\",\"experimental\":{\"allowedDevOrigins\":[],\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":15,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"D:\\\\step-by-step\\\\thepaperbull-144\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"D:\\\\step-by-step\\\\thepaperbull-144\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/twitter-image/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/twitter-image/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map"], "names": ["runtime", "width", "height", "Image", "ImageResponse", "div", "style", "fontSize", "background", "display", "flexDirection", "alignItems", "justifyContent", "position", "img", "src", "alt", "top", "left", "objectFit", "bottom", "padding", "color", "fontWeight", "marginTop", "size"], "sourceRoot": "", "ignoreList": []}