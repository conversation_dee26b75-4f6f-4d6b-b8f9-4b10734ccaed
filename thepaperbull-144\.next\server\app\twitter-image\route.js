(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[846],{5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},6487:()=>{},8024:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>I,default:()=>z});var r,a={};i.r(a),i.d(a,{alt:()=>b,contentType:()=>v,default:()=>w,dynamic:()=>x,runtime:()=>h,size:()=>y});var s={};i.r(s),i.d(s,{GET:()=>D,alt:()=>b,contentType:()=>v,dynamic:()=>x,runtime:()=>h,size:()=>y});var o={};i.r(o),i.d(o,{patchFetch:()=>R,routeModule:()=>P,serverHooks:()=>E,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>M});var n=i(8429),c=i(9874),l=i(8836),p=i(6567),f=i(4144),u=i(5421),d=i(9079),m=i(8953),g=i(2004);let h="edge",x="force-static",b="Cryptocurrency Trading Dashboard",y={width:1200,height:630},v="image/png";async function w(){return new g.f((0,m.jsxs)("div",{style:{fontSize:128,background:"white",width:"100%",height:"100%",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",position:"relative"},children:[(0,m.jsx)("img",{src:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png",alt:"Cryptocurrency Dashboard",style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",objectFit:"cover"}}),(0,m.jsxs)("div",{style:{position:"absolute",bottom:0,left:0,width:"100%",height:"30%",background:"linear-gradient(transparent, rgba(0,0,0,0.7))",display:"flex",flexDirection:"column",justifyContent:"flex-end",padding:"40px"},children:[(0,m.jsx)("div",{style:{fontSize:"48px",color:"white",fontWeight:"bold"},children:"Crypto Dashboard"}),(0,m.jsx)("div",{style:{fontSize:"24px",color:"white",marginTop:"10px"},children:"Modern cryptocurrency trading platform"})]})]}),{...y})}let S={...a},C=S.default,k=S.generateImageMetadata;if("function"!=typeof C)throw Error('Default export is missing in "D:\\step-by-step\\thepaperbull-144\\app\\twitter-image.tsx"');async function D(e,t){let i;let r=await t.params,{__metadata_id__:a,...s}=r||{},o=r?s:void 0;if(k){let e=await k({params:o});if(null==(i=e.find(e=>e.id.toString()===a)?.id))return new d.Rp("Not Found",{status:404})}return C({params:o,id:i})}let P=new p.AppRouteRouteModule({definition:{kind:f.A.APP_ROUTE,page:"/twitter-image/route",pathname:"/twitter-image",filename:"twitter-image",bundlePath:"app/twitter-image/route"},resolvedPagePath:"next-metadata-route-loader?filePath=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp%5Ctwitter-image.tsx&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"export",userland:s}),{workAsyncStorage:T,workUnitAsyncStorage:M,serverHooks:E}=P;function R(){return(0,u.V5)({workAsyncStorage:T,workUnitAsyncStorage:M})}let _=null==(r=self.__RSC_MANIFEST)?void 0:r["/twitter-image/route"],q=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);_&&q&&(0,c.fQ)({page:"/twitter-image/route",clientReferenceManifest:_,serverActionsManifest:q,serverModuleMap:(0,n.e)({serverActionsManifest:q})});let I=o,z=l.s.wrap(P,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!0,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[],unoptimized:!0},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!0,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,output:"export",modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"D:\\step-by-step\\thepaperbull-144",experimental:{allowedDevOrigins:[],nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:15,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"D:\\step-by-step\\thepaperbull-144"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview",bundlePagesRouterDependencies:!1,configFile:"D:\\step-by-step\\thepaperbull-144\\next.config.mjs",configFileName:"next.config.mjs"}})},8335:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[790],()=>t(8024));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/twitter-image/route"]=i}]);
//# sourceMappingURL=route.js.map