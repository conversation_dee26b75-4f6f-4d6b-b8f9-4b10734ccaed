(()=>{var e={};e.id=170,e.ids=[170],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44825:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\(dashboard)\\\\wallet\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\wallet\\page.tsx","default")},48750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var n=r(65239),s=r(48088),a=r(88170),i=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["(dashboard)",{children:["wallet",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44825)),"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\wallet\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,75870))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"D:\\step-by-step\\thepaperbull-144\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,99766)),"D:\\step-by-step\\thepaperbull-144\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,75870))).default(e)],manifest:void 0}}]}.children,c=["D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\wallet\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/wallet/page",pathname:"/wallet",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},59821:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(60687);r(43210);var s=r(24224),a=r(96241);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,a.cn)(i({variant:t}),e),...r})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67651:(e,t,r)=>{Promise.resolve().then(r.bind(r,90579))},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84683:(e,t,r)=>{Promise.resolve().then(r.bind(r,44825))},90579:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>t_});var n,s,a=r(60687),i=r(43210),o=r(35583),d=r(31158),l=r(78122),c=r(62688);let u=(0,c.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),m=(0,c.A)("ArrowRightLeft",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]),x=(0,c.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var f=r(13861),p=r(12597);let h=(0,c.A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var g=r(25541),b=r(12640),v=r(47155),y=r(21643),j=r(59821);function N(){let[e,t]=(0,i.useState)(!0),[r,n]=(0,i.useState)("1W"),[s,o]=(0,i.useState)(null),{accountInfo:d,positions:l,getTotalPnL:c,getTotalMargin:u,getAvailableBalance:m}=(0,v.fx)(),y=s?.balance?.current||1e4,N=d?.totalWalletBalance||y,w=c()||0,E=u()||0,C=m();(null==C||0===C)&&(C=Math.max(0,N-E+w));let A=l.length,S=N>0?w/N*100:0,k=w>=0?"up":"down",T=[{name:"Futures Trading",type:"FUTURES",icon:"₿",iconColor:"bg-orange-500",balance:N.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),totalPnL:w>=0?`+${w.toFixed(2)}`:w.toFixed(2),dailyPnL:w>=0?`+${(.6*w).toFixed(2)}`:(.6*w).toFixed(2),change:`${w>=0?"+":""}${S.toFixed(2)}%`,changeType:k,positions:A,margin:E.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),isActive:!0},{name:"Margin Trading",type:"MARGIN (Coming Soon)",icon:"Ξ",iconColor:"bg-gray-400",balance:"0.00",totalPnL:"—",dailyPnL:"—",change:"—",changeType:"neutral",positions:0,margin:"0.00",isActive:!1},{name:"Options Trading",type:"OPTIONS (Coming Soon)",icon:"\xd8",iconColor:"bg-gray-400",balance:"0.00",totalPnL:"—",dailyPnL:"—",change:"—",changeType:"neutral",positions:0,margin:"0.00",isActive:!1}],P=T.filter(e=>e.isActive),L=P.reduce((e,t)=>e+parseFloat(t.balance.replace(/,/g,"")),0),D=P.reduce((e,t)=>"—"===t.totalPnL?e:e+parseFloat(t.totalPnL.replace(/[+,]/g,"")),0);return(0,a.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-foreground",children:"Paper Trading Balance"}),s&&"free"!==s.subscription.type&&(0,a.jsxs)(j.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,a.jsx)(x,{className:"h-3 w-3"}),s.subscription.type.charAt(0).toUpperCase()+s.subscription.type.slice(1)]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(!e),className:"p-1 rounded-md hover:bg-muted","aria-label":e?"Hide balance":"Show balance",children:e?(0,a.jsx)(f.A,{className:"h-5 w-5 text-muted-foreground"}):(0,a.jsx)(p.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,a.jsx)("button",{className:"p-1 rounded-md hover:bg-muted",children:(0,a.jsx)(h,{className:"h-5 w-5 text-muted-foreground"})})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Virtual Balance"}),s&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Limit: $",s.balance.maximum.toLocaleString()]})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-foreground",children:e?`$${L.toLocaleString()}`:"••••••••"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[D>=0?(0,a.jsx)(g.A,{className:"h-4 w-4 text-emerald-500 mr-1"}):(0,a.jsx)(b.A,{className:"h-4 w-4 text-red-500 mr-1"}),(0,a.jsxs)("span",{className:`text-sm font-medium ${D>=0?"text-emerald-500":"text-red-500"}`,children:[D>=0?"+":"","$",D.toFixed(2)," (",L>0?(D/L*100).toFixed(2):"0.00","%)"]}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground ml-2",children:"Total P&L"})]}),s&&s.balance.current>.8*s.balance.maximum&&(0,a.jsxs)("div",{className:"mt-2 text-xs text-amber-600 dark:text-amber-400",children:["⚠️ Approaching balance limit (",(s.balance.current/s.balance.maximum*100).toFixed(1),"% used)"]})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-4",children:[(0,a.jsx)("h3",{className:"font-medium text-foreground",children:"Trading Accounts"}),(0,a.jsx)("div",{className:"flex space-x-1",children:["24H","1W","1M","1Y","ALL"].map(e=>(0,a.jsx)("button",{className:`px-2 py-1 text-xs rounded ${r===e?"bg-muted text-foreground":"hover:bg-muted text-muted-foreground"}`,onClick:()=>n(e),children:e},e))})]}),(0,a.jsx)("div",{className:"overflow-x-auto -mx-4 px-4",children:(0,a.jsxs)("table",{className:"min-w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"text-xs text-muted-foreground border-b border-border",children:[(0,a.jsx)("th",{className:"pb-2 text-left",children:"ACCOUNT"}),(0,a.jsx)("th",{className:"pb-2 text-right",children:"BALANCE"}),(0,a.jsx)("th",{className:"pb-2 text-right",children:"P&L"}),(0,a.jsx)("th",{className:"pb-2 text-right",children:"POSITIONS"}),(0,a.jsx)("th",{className:"pb-2 text-right",children:"MARGIN"})]})}),(0,a.jsx)("tbody",{children:T.map((t,r)=>(0,a.jsxs)("tr",{className:`border-b border-border last:border-0 ${t.isActive?"":"opacity-50"}`,children:[(0,a.jsx)("td",{className:"py-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:`${t.iconColor} rounded-full h-8 w-8 flex items-center justify-center mr-3`,children:(0,a.jsx)("span",{className:"text-white text-xs",children:t.icon})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-foreground",children:t.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:t.type})]})]})}),(0,a.jsxs)("td",{className:"py-3 text-right",children:[(0,a.jsx)("div",{className:"font-medium text-foreground",children:t.isActive?e?`$${t.balance}`:"•••••":"—"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"USD"})]}),(0,a.jsxs)("td",{className:"py-3 text-right",children:[(0,a.jsx)("div",{className:`font-medium ${t.isActive?"up"===t.changeType?"text-emerald-500":"down"===t.changeType?"text-red-500":"text-muted-foreground":"text-muted-foreground"}`,children:t.isActive?e?t.totalPnL:"•••••":"—"}),(0,a.jsx)("div",{className:`text-xs ${t.isActive?"up"===t.changeType?"text-emerald-500":"down"===t.changeType?"text-red-500":"text-muted-foreground":"text-muted-foreground"}`,children:t.change})]}),(0,a.jsxs)("td",{className:"py-3 text-right",children:[(0,a.jsx)("div",{className:"font-medium text-foreground",children:t.isActive?t.positions:"—"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"open"})]}),(0,a.jsxs)("td",{className:"py-3 text-right",children:[(0,a.jsx)("div",{className:"font-medium text-foreground",children:t.isActive?e?`$${t.margin}`:"•••••":"—"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"used"})]})]},r))})]})}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-border",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Available"}),(0,a.jsx)("div",{className:"font-medium text-emerald-600",children:e?`$${C.toLocaleString()}`:"•••••"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Margin Used"}),(0,a.jsx)("div",{className:"font-medium text-primary",children:e?`$${E.toLocaleString()}`:"•••••"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Positions"}),(0,a.jsx)("div",{className:"font-medium text-foreground",children:A})]})]})})]})}var w=r(84027),E=r(93613),C=r(70569),A=r(98599),S=r(11273),k=r(96963),T=r(65551),P=r(14163),L=r(13495),D="dismissableLayer.update",R=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),F=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:s,onPointerDownOutside:o,onFocusOutside:d,onInteractOutside:l,onDismiss:c,...u}=e,m=i.useContext(R),[x,f]=i.useState(null),p=x?.ownerDocument??globalThis?.document,[,h]=i.useState({}),g=(0,A.s)(t,e=>f(e)),b=Array.from(m.layers),[v]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),y=b.indexOf(v),j=x?b.indexOf(x):-1,N=m.layersWithOutsidePointerEventsDisabled.size>0,w=j>=y,E=function(e,t=globalThis?.document){let r=(0,L.c)(e),n=i.useRef(!1),s=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){M("dismissableLayer.pointerDownOutside",r,a,{discrete:!0})},a={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",s.current),s.current=n,t.addEventListener("click",s.current,{once:!0})):n()}else t.removeEventListener("click",s.current);n.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",e),t.removeEventListener("click",s.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...m.branches].some(e=>e.contains(t));!w||r||(o?.(e),l?.(e),e.defaultPrevented||c?.())},p),S=function(e,t=globalThis?.document){let r=(0,L.c)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&M("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...m.branches].some(e=>e.contains(t))||(d?.(e),l?.(e),e.defaultPrevented||c?.())},p);return function(e,t=globalThis?.document){let r=(0,L.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{j===m.layers.size-1&&(s?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},p),i.useEffect(()=>{if(x)return r&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(n=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(x)),m.layers.add(x),$(),()=>{r&&1===m.layersWithOutsidePointerEventsDisabled.size&&(p.body.style.pointerEvents=n)}},[x,p,r,m]),i.useEffect(()=>()=>{x&&(m.layers.delete(x),m.layersWithOutsidePointerEventsDisabled.delete(x),$())},[x,m]),i.useEffect(()=>{let e=()=>h({});return document.addEventListener(D,e),()=>document.removeEventListener(D,e)},[]),(0,a.jsx)(P.sG.div,{...u,ref:g,style:{pointerEvents:N?w?"auto":"none":void 0,...e.style},onFocusCapture:(0,C.m)(e.onFocusCapture,S.onFocusCapture),onBlurCapture:(0,C.m)(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:(0,C.m)(e.onPointerDownCapture,E.onPointerDownCapture)})});function $(){let e=new CustomEvent(D);document.dispatchEvent(e)}function M(e,t,r,{discrete:n}){let s=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),n?(0,P.hO)(s,a):s.dispatchEvent(a)}F.displayName="DismissableLayer",i.forwardRef((e,t)=>{let r=i.useContext(R),n=i.useRef(null),s=(0,A.s)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,a.jsx)(P.sG.div,{...e,ref:s})}).displayName="DismissableLayerBranch";var O="focusScope.autoFocusOnMount",I="focusScope.autoFocusOnUnmount",B={bubbles:!1,cancelable:!0},U=i.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:s,onUnmountAutoFocus:o,...d}=e,[l,c]=i.useState(null),u=(0,L.c)(s),m=(0,L.c)(o),x=i.useRef(null),f=(0,A.s)(t,e=>c(e)),p=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(n){let e=function(e){if(p.paused||!l)return;let t=e.target;l.contains(t)?x.current=t:z(x.current,{select:!0})},t=function(e){if(p.paused||!l)return;let t=e.relatedTarget;null===t||l.contains(t)||z(x.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&z(l)});return l&&r.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,l,p.paused]),i.useEffect(()=>{if(l){q.add(p);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(O,B);l.addEventListener(O,u),l.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(z(n,{select:t}),document.activeElement!==r)return}(W(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&z(l))}return()=>{l.removeEventListener(O,u),setTimeout(()=>{let t=new CustomEvent(I,B);l.addEventListener(I,m),l.dispatchEvent(t),t.defaultPrevented||z(e??document.body,{select:!0}),l.removeEventListener(I,m),q.remove(p)},0)}}},[l,u,m,p]);let h=i.useCallback(e=>{if(!r&&!n||p.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,s=document.activeElement;if(t&&s){let t=e.currentTarget,[n,a]=function(e){let t=W(e);return[_(t,e),_(t.reverse(),e)]}(t);n&&a?e.shiftKey||s!==a?e.shiftKey&&s===n&&(e.preventDefault(),r&&z(a,{select:!0})):(e.preventDefault(),r&&z(n,{select:!0})):s===t&&e.preventDefault()}},[r,n,p.paused]);return(0,a.jsx)(P.sG.div,{tabIndex:-1,...d,ref:f,onKeyDown:h})});function W(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function _(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function z(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}U.displayName="FocusScope";var q=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=G(e,t)).unshift(t)},remove(t){e=G(e,t),e[0]?.resume()}}}();function G(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var V=r(51215),H=r(66156),X=i.forwardRef((e,t)=>{let{container:r,...n}=e,[s,o]=i.useState(!1);(0,H.N)(()=>o(!0),[]);let d=r||s&&globalThis?.document?.body;return d?V.createPortal((0,a.jsx)(P.sG.div,{...n,ref:t}),d):null});X.displayName="Portal";var Y=r(46059),K=0;function Z(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Q=r(4363),J="right-scroll-bar-position",ee="width-before-scroll-bar";function et(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var er="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,en=new WeakMap;function es(e){return e}var ea=function(e){void 0===e&&(e={});var t,r,n,s,a=(t=null,void 0===r&&(r=es),n=[],s=!1,{read:function(){if(s)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,s);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(s=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){s=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var a=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return a.options=(0,Q.Cl)({async:!0,ssr:!1},e),a}(),ei=function(){},eo=i.forwardRef(function(e,t){var r,n,s,a,o=i.useRef(null),d=i.useState({onScrollCapture:ei,onWheelCapture:ei,onTouchMoveCapture:ei}),l=d[0],c=d[1],u=e.forwardProps,m=e.children,x=e.className,f=e.removeScrollBar,p=e.enabled,h=e.shards,g=e.sideCar,b=e.noRelative,v=e.noIsolation,y=e.inert,j=e.allowPinchZoom,N=e.as,w=e.gapMode,E=(0,Q.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(r=[o,t],n=function(e){return r.forEach(function(t){return et(t,e)})},(s=(0,i.useState)(function(){return{value:null,callback:n,facade:{get current(){return s.value},set current(value){var e=s.value;e!==value&&(s.value=value,s.callback(value,e))}}}})[0]).callback=n,a=s.facade,er(function(){var e=en.get(a);if(e){var t=new Set(e),n=new Set(r),s=a.current;t.forEach(function(e){n.has(e)||et(e,null)}),n.forEach(function(e){t.has(e)||et(e,s)})}en.set(a,r)},[r]),a),A=(0,Q.Cl)((0,Q.Cl)({},E),l);return i.createElement(i.Fragment,null,p&&i.createElement(g,{sideCar:ea,removeScrollBar:f,shards:h,noRelative:b,noIsolation:v,inert:y,setCallbacks:c,allowPinchZoom:!!j,lockRef:o,gapMode:w}),u?i.cloneElement(i.Children.only(m),(0,Q.Cl)((0,Q.Cl)({},A),{ref:C})):i.createElement(void 0===N?"div":N,(0,Q.Cl)({},A,{className:x,ref:C}),m))});eo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},eo.classNames={fullWidth:ee,zeroRight:J};var ed=function(e){var t=e.sideCar,r=(0,Q.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return i.createElement(n,(0,Q.Cl)({},r))};ed.isSideCarExport=!0;var el=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=s||r.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ec=function(){var e=el();return function(t,r){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},eu=function(){var e=ec();return function(t){return e(t.styles,t.dynamic),null}},em={left:0,top:0,right:0,gap:0},ex=function(e){return parseInt(e||"",10)||0},ef=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],s=t["padding"===e?"paddingRight":"marginRight"];return[ex(r),ex(n),ex(s)]},ep=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return em;var t=ef(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},eh=eu(),eg="data-scroll-locked",eb=function(e,t,r,n){var s=e.left,a=e.top,i=e.right,o=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(o,"px ").concat(n,";\n  }\n  body[").concat(eg,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(s,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(o,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(o,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(J," {\n    right: ").concat(o,"px ").concat(n,";\n  }\n  \n  .").concat(ee," {\n    margin-right: ").concat(o,"px ").concat(n,";\n  }\n  \n  .").concat(J," .").concat(J," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(ee," .").concat(ee," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(eg,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(o,"px;\n  }\n")},ev=function(){var e=parseInt(document.body.getAttribute(eg)||"0",10);return isFinite(e)?e:0},ey=function(){i.useEffect(function(){return document.body.setAttribute(eg,(ev()+1).toString()),function(){var e=ev()-1;e<=0?document.body.removeAttribute(eg):document.body.setAttribute(eg,e.toString())}},[])},ej=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,s=void 0===n?"margin":n;ey();var a=i.useMemo(function(){return ep(s)},[s]);return i.createElement(eh,{styles:eb(a,!t,s,r?"":"!important")})},eN=!1;if("undefined"!=typeof window)try{var ew=Object.defineProperty({},"passive",{get:function(){return eN=!0,!0}});window.addEventListener("test",ew,ew),window.removeEventListener("test",ew,ew)}catch(e){eN=!1}var eE=!!eN&&{passive:!1},eC=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},eA=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),eS(e,n)){var s=ek(e,n);if(s[1]>s[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},eS=function(e,t){return"v"===e?eC(t,"overflowY"):eC(t,"overflowX")},ek=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eT=function(e,t,r,n,s){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),o=i*n,d=r.target,l=t.contains(d),c=!1,u=o>0,m=0,x=0;do{var f=ek(e,d),p=f[0],h=f[1]-f[2]-i*p;(p||h)&&eS(e,d)&&(m+=h,x+=p),d=d.parentNode.host||d.parentNode}while(!l&&d!==document.body||l&&(t.contains(d)||t===d));return u&&(s&&1>Math.abs(m)||!s&&o>m)?c=!0:!u&&(s&&1>Math.abs(x)||!s&&-o>x)&&(c=!0),c},eP=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eL=function(e){return[e.deltaX,e.deltaY]},eD=function(e){return e&&"current"in e?e.current:e},eR=0,eF=[];let e$=(ea.useMedium(function(e){var t=i.useRef([]),r=i.useRef([0,0]),n=i.useRef(),s=i.useState(eR++)[0],a=i.useState(eu)[0],o=i.useRef(e);i.useEffect(function(){o.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var t=(0,Q.fX)([e.lockRef.current],(e.shards||[]).map(eD),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var d=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!o.current.allowPinchZoom;var s,a=eP(e),i=r.current,d="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],c=e.target,u=Math.abs(d)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===u&&"range"===c.type)return!1;var m=eA(u,c);if(!m)return!0;if(m?s=u:(s="v"===u?"h":"v",m=eA(u,c)),!m)return!1;if(!n.current&&"changedTouches"in e&&(d||l)&&(n.current=s),!s)return!0;var x=n.current||s;return eT(x,t,e,"h"===x?d:l,!0)},[]),l=i.useCallback(function(e){if(eF.length&&eF[eF.length-1]===a){var r="deltaY"in e?eL(e):eP(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var s=(o.current.shards||[]).map(eD).filter(Boolean).filter(function(t){return t.contains(e.target)});(s.length>0?d(e,s[0]):!o.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,r,n,s){var a={name:e,delta:r,target:n,should:s,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),u=i.useCallback(function(e){r.current=eP(e),n.current=void 0},[]),m=i.useCallback(function(t){c(t.type,eL(t),t.target,d(t,e.lockRef.current))},[]),x=i.useCallback(function(t){c(t.type,eP(t),t.target,d(t,e.lockRef.current))},[]);i.useEffect(function(){return eF.push(a),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:x}),document.addEventListener("wheel",l,eE),document.addEventListener("touchmove",l,eE),document.addEventListener("touchstart",u,eE),function(){eF=eF.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,eE),document.removeEventListener("touchmove",l,eE),document.removeEventListener("touchstart",u,eE)}},[]);var f=e.removeScrollBar,p=e.inert;return i.createElement(i.Fragment,null,p?i.createElement(a,{styles:"\n  .block-interactivity-".concat(s," {pointer-events: none;}\n  .allow-interactivity-").concat(s," {pointer-events: all;}\n")}):null,f?i.createElement(ej,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),ed);var eM=i.forwardRef(function(e,t){return i.createElement(eo,(0,Q.Cl)({},e,{ref:t,sideCar:e$}))});eM.classNames=eo.classNames;var eO=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eI=new WeakMap,eB=new WeakMap,eU={},eW=0,e_=function(e){return e&&(e.host||e_(e.parentNode))},ez=function(e,t,r,n){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=e_(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eU[r]||(eU[r]=new WeakMap);var a=eU[r],i=[],o=new Set,d=new Set(s),l=function(e){!(!e||o.has(e))&&(o.add(e),l(e.parentNode))};s.forEach(l);var c=function(e){!(!e||d.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(o.has(e))c(e);else try{var t=e.getAttribute(n),s=null!==t&&"false"!==t,d=(eI.get(e)||0)+1,l=(a.get(e)||0)+1;eI.set(e,d),a.set(e,l),i.push(e),1===d&&s&&eB.set(e,!0),1===l&&e.setAttribute(r,"true"),s||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),o.clear(),eW++,function(){i.forEach(function(e){var t=eI.get(e)-1,s=a.get(e)-1;eI.set(e,t),a.set(e,s),t||(eB.has(e)||e.removeAttribute(n),eB.delete(e)),s||e.removeAttribute(r)}),--eW||(eI=new WeakMap,eI=new WeakMap,eB=new WeakMap,eU={})}},eq=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),s=t||eO(e);return s?(n.push.apply(n,Array.from(s.querySelectorAll("[aria-live], script"))),ez(n,s,r,"aria-hidden")):function(){return null}},eG=r(8730),eV="Dialog",[eH,eX]=(0,S.A)(eV),[eY,eK]=eH(eV),eZ=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:s,onOpenChange:o,modal:d=!0}=e,l=i.useRef(null),c=i.useRef(null),[u=!1,m]=(0,T.i)({prop:n,defaultProp:s,onChange:o});return(0,a.jsx)(eY,{scope:t,triggerRef:l,contentRef:c,contentId:(0,k.B)(),titleId:(0,k.B)(),descriptionId:(0,k.B)(),open:u,onOpenChange:m,onOpenToggle:i.useCallback(()=>m(e=>!e),[m]),modal:d,children:r})};eZ.displayName=eV;var eQ="DialogTrigger";i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=eK(eQ,r),i=(0,A.s)(t,s.triggerRef);return(0,a.jsx)(P.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":to(s.open),...n,ref:i,onClick:(0,C.m)(e.onClick,s.onOpenToggle)})}).displayName=eQ;var eJ="DialogPortal",[e0,e1]=eH(eJ,{forceMount:void 0}),e4=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:s}=e,o=eK(eJ,t);return(0,a.jsx)(e0,{scope:t,forceMount:r,children:i.Children.map(n,e=>(0,a.jsx)(Y.C,{present:r||o.open,children:(0,a.jsx)(X,{asChild:!0,container:s,children:e})}))})};e4.displayName=eJ;var e2="DialogOverlay",e5=i.forwardRef((e,t)=>{let r=e1(e2,e.__scopeDialog),{forceMount:n=r.forceMount,...s}=e,i=eK(e2,e.__scopeDialog);return i.modal?(0,a.jsx)(Y.C,{present:n||i.open,children:(0,a.jsx)(e3,{...s,ref:t})}):null});e5.displayName=e2;var e3=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=eK(e2,r);return(0,a.jsx)(eM,{as:eG.DX,allowPinchZoom:!0,shards:[s.contentRef],children:(0,a.jsx)(P.sG.div,{"data-state":to(s.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),e6="DialogContent",e8=i.forwardRef((e,t)=>{let r=e1(e6,e.__scopeDialog),{forceMount:n=r.forceMount,...s}=e,i=eK(e6,e.__scopeDialog);return(0,a.jsx)(Y.C,{present:n||i.open,children:i.modal?(0,a.jsx)(e9,{...s,ref:t}):(0,a.jsx)(e7,{...s,ref:t})})});e8.displayName=e6;var e9=i.forwardRef((e,t)=>{let r=eK(e6,e.__scopeDialog),n=i.useRef(null),s=(0,A.s)(t,r.contentRef,n);return i.useEffect(()=>{let e=n.current;if(e)return eq(e)},[]),(0,a.jsx)(te,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,C.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,C.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,C.m)(e.onFocusOutside,e=>e.preventDefault())})}),e7=i.forwardRef((e,t)=>{let r=eK(e6,e.__scopeDialog),n=i.useRef(!1),s=i.useRef(!1);return(0,a.jsx)(te,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(s.current=!0));let a=t.target;r.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),te=i.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:s,onCloseAutoFocus:o,...d}=e,l=eK(e6,r),c=i.useRef(null),u=(0,A.s)(t,c);return i.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Z()),document.body.insertAdjacentElement("beforeend",e[1]??Z()),K++,()=>{1===K&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),K--}},[]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(U,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:s,onUnmountAutoFocus:o,children:(0,a.jsx)(F,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":to(l.open),...d,ref:u,onDismiss:()=>l.onOpenChange(!1)})}),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tu,{titleId:l.titleId}),(0,a.jsx)(tm,{contentRef:c,descriptionId:l.descriptionId})]})]})}),tt="DialogTitle",tr=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=eK(tt,r);return(0,a.jsx)(P.sG.h2,{id:s.titleId,...n,ref:t})});tr.displayName=tt;var tn="DialogDescription",ts=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=eK(tn,r);return(0,a.jsx)(P.sG.p,{id:s.descriptionId,...n,ref:t})});ts.displayName=tn;var ta="DialogClose",ti=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=eK(ta,r);return(0,a.jsx)(P.sG.button,{type:"button",...n,ref:t,onClick:(0,C.m)(e.onClick,()=>s.onOpenChange(!1))})});function to(e){return e?"open":"closed"}ti.displayName=ta;var td="DialogTitleWarning",[tl,tc]=(0,S.q)(td,{contentName:e6,titleName:tt,docsSlug:"dialog"}),tu=({titleId:e})=>{let t=tc(td),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},tm=({contentRef:e,descriptionId:t})=>{let r=tc("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return i.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(n)},[n,e,t]),null},tx=r(11860),tf=r(96241);let tp=i.forwardRef(({className:e,...t},r)=>(0,a.jsx)(e5,{ref:r,className:(0,tf.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));tp.displayName=e5.displayName;let th=i.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(e4,{children:[(0,a.jsx)(tp,{}),(0,a.jsxs)(e8,{ref:n,className:(0,tf.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,a.jsxs)(ti,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(tx.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));th.displayName=e8.displayName;let tg=({className:e,...t})=>(0,a.jsx)("div",{className:(0,tf.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});tg.displayName="DialogHeader";let tb=i.forwardRef(({className:e,...t},r)=>(0,a.jsx)(tr,{ref:r,className:(0,tf.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));tb.displayName=tr.displayName,i.forwardRef(({className:e,...t},r)=>(0,a.jsx)(ts,{ref:r,className:(0,tf.cn)("text-sm text-muted-foreground",e),...t})).displayName=ts.displayName;var tv=r(24224);let ty=(0,tv.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),tj=i.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...s},i)=>{let o=n?eG.DX:"button";return(0,a.jsx)(o,{className:(0,tf.cn)(ty({variant:t,size:r,className:e})),ref:i,...s})});tj.displayName="Button";let tN=i.forwardRef(({className:e,type:t,...r},n)=>(0,a.jsx)("input",{type:t,className:(0,tf.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...r}));tN.displayName="Input";var tw=i.forwardRef((e,t)=>(0,a.jsx)(P.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));tw.displayName="Label";let tE=(0,tv.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),tC=i.forwardRef(({className:e,...t},r)=>(0,a.jsx)(tw,{ref:r,className:(0,tf.cn)(tE(),e),...t}));tC.displayName=tw.displayName;let tA=(0,tv.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),tS=i.forwardRef(({className:e,variant:t,...r},n)=>(0,a.jsx)("div",{ref:n,role:"alert",className:(0,tf.cn)(tA({variant:t}),e),...r}));tS.displayName="Alert",i.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h5",{ref:r,className:(0,tf.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let tk=i.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,tf.cn)("text-sm [&_p]:leading-relaxed",e),...t}));tk.displayName="AlertDescription";var tT="horizontal",tP=["horizontal","vertical"],tL=i.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=tT,...i}=e,o=(r=s,tP.includes(r))?s:tT;return(0,a.jsx)(P.sG.div,{"data-orientation":o,...n?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...i,ref:t})});tL.displayName="Separator";let tD=i.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...n},s)=>(0,a.jsx)(tL,{ref:s,decorative:r,orientation:t,className:(0,tf.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...n}));tD.displayName=tL.displayName;var tR=r(45583),tF=r(52581),t$=r(38239),tM=r(55183);function tO({isOpen:e,onClose:t,currentBalance:r,maxBalance:n,subscriptionType:s}){let[o,d]=(0,i.useState)(""),[l,c]=(0,i.useState)(!1),[m,f]=(0,i.useState)(!1),p=(0,tM.E$)(),h=n-r,g=parseFloat(o)||0,b=async()=>{if(!o||g<=0){tF.oR.error("Please enter a valid amount");return}c(!0);try{let e=await y.A.addFunds({amount:g,method:"virtual"});e.success?(tF.oR.success(e.message,{duration:3e3,action:{label:"View Balance",onClick:()=>console.log("View balance clicked")}}),p.success(e.message,{title:"Funds Added Successfully",duration:4e3,position:"top-right"}),d(""),t()):e.requires_subscription?(f(!0),p.warning("Balance limit reached",{title:"Upgrade Required",duration:5e3})):(tF.oR.error(e.message),p.error(e.message,{title:"Failed to Add Funds",duration:5e3}))}catch(e){tF.oR.error("Failed to add funds")}finally{c(!1)}},v=async e=>{c(!0);try{if(await y.A.upgradeSubscription(e)){let r=`Successfully upgraded to ${e} plan!`;tF.oR.success(r,{duration:4e3,action:{label:"View Features",onClick:()=>console.log("View features clicked")}}),p.success(r,{title:"Subscription Upgraded",duration:6e3,position:"top-right"}),f(!1);let n=await y.A.addFunds({amount:g,method:"subscription_upgrade"});n.success&&(tF.oR.success(n.message),p.success(n.message,{title:"Funds Added",duration:4e3}),d(""),t())}else tF.oR.error("Failed to upgrade subscription"),p.error("Failed to upgrade subscription",{title:"Upgrade Failed",duration:5e3})}catch(e){tF.oR.error("Failed to upgrade subscription")}finally{c(!1)}};return m?(0,a.jsx)(eZ,{open:e,onOpenChange:t,children:(0,a.jsxs)(th,{className:"sm:max-w-md",children:[(0,a.jsx)(tg,{children:(0,a.jsxs)(tb,{className:"flex items-center gap-2",children:[(0,a.jsx)(x,{className:"h-5 w-5 text-yellow-500"}),"Upgrade Required"]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(tS,{children:[(0,a.jsx)(E.A,{className:"h-4 w-4"}),(0,a.jsxs)(tk,{children:["You've reached your current balance limit of ",n.toLocaleString()," USDT. Upgrade your subscription to add more funds."]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsx)("span",{className:"font-medium",children:"Premium Plan"})]}),(0,a.jsxs)(j.E,{variant:"secondary",children:["$",t$.ND.premium.price,"/month"]})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Up to ",t$.ND.premium.maxBalance.toLocaleString()," USDT balance"]}),(0,a.jsx)(tj,{onClick:()=>v("premium"),disabled:l,className:"w-full",children:"Upgrade to Premium"})]}),(0,a.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tR.A,{className:"h-4 w-4 text-purple-500"}),(0,a.jsx)("span",{className:"font-medium",children:"Pro Plan"})]}),(0,a.jsxs)(j.E,{variant:"secondary",children:["$",t$.ND.pro.price,"/month"]})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Up to ",t$.ND.pro.maxBalance.toLocaleString()," USDT balance + API access"]}),(0,a.jsx)(tj,{onClick:()=>v("pro"),disabled:l,variant:"outline",className:"w-full",children:"Upgrade to Pro"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(tj,{variant:"outline",onClick:()=>f(!1),className:"flex-1",children:"Back"}),(0,a.jsx)(tj,{variant:"outline",onClick:t,className:"flex-1",children:"Cancel"})]})]})]})}):(0,a.jsx)(eZ,{open:e,onOpenChange:t,children:(0,a.jsxs)(th,{className:"sm:max-w-md",children:[(0,a.jsx)(tg,{children:(0,a.jsxs)(tb,{className:"flex items-center gap-2",children:[(0,a.jsx)(u,{className:"h-5 w-5"}),"Add Virtual Funds"]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4 space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Current Balance:"}),(0,a.jsxs)("span",{className:"font-medium",children:[r.toLocaleString()," USDT"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Available to Add:"}),(0,a.jsxs)("span",{className:"font-medium text-green-600",children:[h.toLocaleString()," USDT"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Plan Limit:"}),(0,a.jsxs)("span",{className:"font-medium",children:[n.toLocaleString()," USDT"]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(tC,{htmlFor:"amount",children:"Amount (USDT)"}),(0,a.jsx)(tN,{id:"amount",type:"number",placeholder:"Enter amount",value:o,onChange:e=>d(e.target.value),min:"0",max:h})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(tC,{children:"Quick Add"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[1e3,5e3,1e4,25e3].map(e=>(0,a.jsx)(tj,{variant:"outline",size:"sm",onClick:()=>d(e.toString()),disabled:e>h,children:e.toLocaleString()},e))})]}),g>h&&(0,a.jsxs)(tS,{children:[(0,a.jsx)(E.A,{className:"h-4 w-4"}),(0,a.jsxs)(tk,{children:["Amount exceeds available funds. Maximum you can add: ",h.toLocaleString()," USDT"]})]}),(0,a.jsx)(tD,{}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(tj,{variant:"outline",onClick:t,className:"flex-1",children:"Cancel"}),(0,a.jsx)(tj,{onClick:b,disabled:l||!o||g<=0||g>h,className:"flex-1",children:l?"Adding...":"Add Funds"})]}),"free"===s&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Need more than ",n.toLocaleString()," USDT?"," ",(0,a.jsx)("button",{onClick:()=>f(!0),className:"text-primary hover:underline",children:"Upgrade your plan"})]})})]})]})})}function tI(){let[e,t]=(0,i.useState)("futures"),[r,n]=(0,i.useState)(null),[s,o]=(0,i.useState)(!1),{accountInfo:d,positions:c,getTotalPnL:x,getTotalMargin:f,getAvailableBalance:p,trades:h}=(0,v.fx)(),g=r?.balance?.current||1e4,b=d?.totalWalletBalance||g,j=x()||0,N=f()||0,C=p();(null==C||0===C)&&(C=Math.max(0,b-N+j));let A=c.length,S=h.length,k=h.filter(e=>e.realizedPnl>0).length,T=S>0?k/S*100:0,P=h.length>0?Math.max(...h.map(e=>e.realizedPnl)):0,L=h.length>0?Math.min(...h.map(e=>e.realizedPnl)):0,D={futures:{name:"Futures Trading",balance:b,available:C,margin:N,pnl:j,positions:A,color:"orange"},margin:{name:"Margin Trading",balance:0,available:0,margin:0,pnl:0,positions:0,color:"blue"},options:{name:"Options Trading",balance:0,available:0,margin:0,pnl:0,positions:0,color:"purple"}}[e];return(0,a.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-foreground",children:"Account Manager"}),(0,a.jsx)("button",{className:"p-1 rounded-md hover:bg-muted",children:(0,a.jsx)(w.A,{className:"h-5 w-5 text-muted-foreground"})})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Select Account"}),(0,a.jsxs)("select",{value:e,onChange:e=>t(e.target.value),className:"w-full border border-border rounded-md px-3 py-2 bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",children:[(0,a.jsx)("option",{value:"futures",children:"Futures Trading"}),(0,a.jsx)("option",{value:"margin",disabled:!0,children:"Margin Trading (Coming Soon)"}),(0,a.jsx)("option",{value:"options",disabled:!0,children:"Options Trading (Coming Soon)"})]})]}),"futures"===e?(0,a.jsxs)("div",{className:"bg-muted rounded-lg p-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h3",{className:"font-medium text-foreground",children:D.name}),(0,a.jsxs)("div",{className:`px-2 py-1 rounded text-xs font-medium ${D.pnl>=0?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400":"bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"}`,children:[D.pnl>=0?"+":"","$",D.pnl.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Balance"}),(0,a.jsxs)("div",{className:"font-medium text-foreground",children:["$",D.balance.toLocaleString()]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Available"}),(0,a.jsxs)("div",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:["$",D.available.toLocaleString()]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Margin Used"}),(0,a.jsxs)("div",{className:"font-medium text-primary",children:["$",D.margin.toLocaleString()]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:"Positions"}),(0,a.jsx)("div",{className:"font-medium text-foreground",children:D.positions})]})]}),(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,a.jsx)("span",{children:"Margin Usage"}),(0,a.jsxs)("span",{children:[(D.margin/D.balance*100).toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-muted rounded-full h-2",children:(0,a.jsx)("div",{className:`h-2 rounded-full ${D.margin/D.balance>.8?"bg-red-500":D.margin/D.balance>.6?"bg-yellow-500":"bg-emerald-500"}`,style:{width:`${D.margin/D.balance*100}%`}})})]})]}):(0,a.jsx)("div",{className:"bg-muted rounded-lg p-4 mb-4",children:(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-muted-foreground mb-2",children:"\uD83D\uDEA7"}),(0,a.jsx)("h3",{className:"font-medium text-foreground mb-2",children:"margin"===e?"Margin Trading":"Options Trading"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Coming Soon"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This trading type is currently under development and will be available in a future update."})]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("button",{onClick:()=>o(!0),className:`w-full py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center ${"futures"===e?"bg-primary text-primary-foreground hover:bg-primary/90":"bg-muted text-muted-foreground cursor-not-allowed"}`,disabled:"futures"!==e,children:[(0,a.jsx)(u,{className:"h-4 w-4 mr-2"}),"Add Funds"]}),(0,a.jsxs)("button",{className:`w-full py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center ${"futures"===e?"bg-card border border-border text-foreground hover:bg-muted":"bg-muted border border-border text-muted-foreground cursor-not-allowed"}`,disabled:"futures"!==e,children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-2"}),"Transfer"]}),(0,a.jsxs)("button",{onClick:()=>{"futures"===e&&confirm("Are you sure you want to reset your account? This will restore your balance to $10,000 and clear all trading history.")&&y.A.resetUser()},className:`w-full py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center ${"futures"===e?"bg-card border border-border text-foreground hover:bg-muted":"bg-muted border border-border text-muted-foreground cursor-not-allowed"}`,disabled:"futures"!==e,children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Reset Account"]})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md dark:bg-yellow-900/20 dark:border-yellow-800",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(E.A,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-xs text-yellow-800 dark:text-yellow-200",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Paper Trading Notice"}),(0,a.jsx)("p",{children:"This is virtual money for educational purposes. No real funds are at risk."})]})]})}),"futures"===e&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-border",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2 text-foreground",children:"Performance Summary"}),(0,a.jsxs)("div",{className:"space-y-2 text-xs",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Win Rate"}),(0,a.jsxs)("span",{className:"font-medium text-foreground",children:[T.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Trades"}),(0,a.jsx)("span",{className:"font-medium text-foreground",children:S})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Best Trade"}),(0,a.jsxs)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:[P>=0?"+":"","$",P.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Worst Trade"}),(0,a.jsxs)("span",{className:"font-medium text-red-600 dark:text-red-400",children:[L>=0?"+":"","$",L.toFixed(2)]})]})]})]}),r&&(0,a.jsx)(tO,{isOpen:s,onClose:()=>o(!1),currentBalance:r.balance.current,maxBalance:r.balance.maximum,subscriptionType:r.subscription.type})]})}let tB=(0,c.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var tU=r(99270);function tW(){let[e,t]=(0,i.useState)("all"),[r,n]=(0,i.useState)(""),{trades:s,accountInfo:o}=(0,v.fx)(),l=[{id:"TXN_INIT",type:"deposit",account:"Futures Trading",description:"Initial virtual deposit",amount:`+$${(o?.totalWalletBalance||5e4).toLocaleString()}.00`,balance:`$${(o?.totalWalletBalance||5e4).toLocaleString()}.00`,timestamp:new Date(Date.now()-6048e5).toISOString(),status:"completed"},...s.map((e,t)=>({id:e.id,type:0===e.realizedPnl?"trade":"pnl",account:"Futures Trading",description:0===e.realizedPnl?`${e.symbol} ${e.side} Position ${"BUY"===e.side?"Opened":"Closed"}`:`${e.symbol} Position P&L`,amount:0===e.realizedPnl?`-$${(e.quantity*e.price).toLocaleString()}`:`${e.realizedPnl>=0?"+":""}$${e.realizedPnl.toFixed(2)}`,balance:`$${(o?.totalWalletBalance||5e4).toLocaleString()}.00`,timestamp:new Date(e.timestamp).toISOString(),status:"completed"}))].sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()),c=e=>{switch(e){case"deposit":return"↓";case"trade":return"⚡";case"pnl":return"\uD83D\uDCC8";case"transfer":return"↔";default:return"•"}},u=e=>{switch(e){case"deposit":return"bg-emerald-100 text-emerald-700";case"trade":return"bg-blue-100 text-blue-700";case"pnl":return"bg-purple-100 text-purple-700";case"transfer":return"bg-orange-100 text-orange-700";default:return"bg-gray-100 text-gray-700"}},m=l.filter(t=>("all"===e||t.type===e)&&(!r||!!t.description.toLowerCase().includes(r.toLowerCase())));return(0,a.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-foreground",children:"Transaction History"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"p-2 rounded-md hover:bg-muted",children:(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,a.jsx)("button",{className:"p-2 rounded-md hover:bg-muted",children:(0,a.jsx)(tB,{className:"h-4 w-4 text-muted-foreground"})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(tU.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)("input",{type:"text",placeholder:"Search transactions...",value:r,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-border rounded-md bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"})]}),(0,a.jsx)("div",{className:"flex space-x-1",children:["all","deposit","trade","pnl","transfer"].map(r=>(0,a.jsx)("button",{className:`px-3 py-2 text-xs font-medium rounded-md capitalize ${e===r?"bg-primary text-primary-foreground":"bg-muted text-foreground hover:bg-muted/80"}`,onClick:()=>t(r),children:r},r))})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"text-xs text-muted-foreground border-b border-border",children:[(0,a.jsx)("th",{className:"pb-2 text-left",children:"TYPE"}),(0,a.jsx)("th",{className:"pb-2 text-left",children:"DESCRIPTION"}),(0,a.jsx)("th",{className:"pb-2 text-left",children:"ACCOUNT"}),(0,a.jsx)("th",{className:"pb-2 text-right",children:"AMOUNT"}),(0,a.jsx)("th",{className:"pb-2 text-right",children:"BALANCE"}),(0,a.jsx)("th",{className:"pb-2 text-right",children:"TIME"})]})}),(0,a.jsx)("tbody",{children:m.map(e=>(0,a.jsxs)("tr",{className:"border-b border-border last:border-0 hover:bg-muted",children:[(0,a.jsx)("td",{className:"py-3",children:(0,a.jsx)("div",{className:`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ${u(e.type)}`,children:c(e.type)})}),(0,a.jsxs)("td",{className:"py-3",children:[(0,a.jsx)("div",{className:"font-medium text-sm text-foreground",children:e.description}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.id})]}),(0,a.jsx)("td",{className:"py-3",children:(0,a.jsx)("div",{className:"text-sm text-foreground",children:e.account})}),(0,a.jsx)("td",{className:"py-3 text-right",children:(0,a.jsx)("div",{className:`font-medium ${e.amount.startsWith("+")?"text-emerald-600 dark:text-emerald-400":"text-red-600 dark:text-red-400"}`,children:e.amount})}),(0,a.jsx)("td",{className:"py-3 text-right",children:(0,a.jsx)("div",{className:"font-medium text-foreground",children:e.balance})}),(0,a.jsxs)("td",{className:"py-3 text-right",children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleDateString()}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleTimeString()})]})]},e.id))})]})}),0===m.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-muted-foreground mb-2",children:"No transactions found"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Try adjusting your search or filter criteria"})]}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-border",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Deposits"}),(0,a.jsxs)("div",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:["$",(o?.totalWalletBalance||5e4).toLocaleString(),".00"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Trades"}),(0,a.jsx)("div",{className:"font-medium text-primary",children:s.length})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Net P&L"}),(0,a.jsxs)("div",{className:`font-medium ${s.reduce((e,t)=>e+t.realizedPnl,0)>=0?"text-emerald-600 dark:text-emerald-400":"text-red-600 dark:text-red-400"}`,children:[s.reduce((e,t)=>e+t.realizedPnl,0)>=0?"+":"","$",s.reduce((e,t)=>e+t.realizedPnl,0).toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Transactions"}),(0,a.jsx)("div",{className:"font-medium text-foreground",children:l.length})]})]})})]})}function t_(){let[e,t]=(0,i.useState)("overview"),[r,n]=(0,i.useState)(null),[s,c]=(0,i.useState)(!1);return(0,tM.E$)(),(0,a.jsxs)("div",{className:"p-3 sm:p-4",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-4 sm:mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3 md:mb-0",children:[(0,a.jsx)("div",{className:"bg-primary/10 rounded-full p-2 mr-3",children:(0,a.jsx)(o.A,{className:"h-5 w-5 sm:h-6 sm:w-6 text-primary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-foreground",children:"Paper Trading Wallet"}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs sm:text-sm",children:"Manage your virtual trading funds"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:space-x-2 sm:gap-0",children:[(0,a.jsxs)("button",{className:"px-3 sm:px-4 py-2 bg-card border border-border rounded-md text-xs sm:text-sm font-medium flex items-center justify-center hover:bg-muted text-foreground",children:[(0,a.jsx)(d.A,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Export Report"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Export"})]}),(0,a.jsxs)("button",{className:"px-3 sm:px-4 py-2 bg-primary text-primary-foreground rounded-md text-xs sm:text-sm font-medium hover:bg-primary/90 flex items-center justify-center",children:[(0,a.jsx)(l.A,{className:"h-3 w-3 sm:h-4 sm:w-4 inline-block mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Reset Balances"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Reset"})]})]})]}),(0,a.jsx)("div",{className:"mb-4 sm:mb-6",children:(0,a.jsxs)("div",{className:"flex overflow-x-auto pb-2 -mx-1 gap-1",children:[(0,a.jsx)("button",{className:`px-3 sm:px-4 py-2 mx-1 rounded-md text-xs sm:text-sm font-medium whitespace-nowrap ${"overview"===e?"bg-primary text-primary-foreground":"bg-card border border-border text-foreground hover:bg-muted"}`,onClick:()=>t("overview"),children:"Overview"}),(0,a.jsxs)("button",{className:`px-3 sm:px-4 py-2 mx-1 rounded-md text-xs sm:text-sm font-medium whitespace-nowrap flex items-center ${"add-funds"===e?"bg-primary text-primary-foreground":"bg-card border border-border text-foreground hover:bg-muted"}`,onClick:()=>t("add-funds"),children:[(0,a.jsx)(u,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Add Funds"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Add"})]}),(0,a.jsxs)("button",{className:`px-3 sm:px-4 py-2 mx-1 rounded-md text-xs sm:text-sm font-medium whitespace-nowrap flex items-center ${"transfer"===e?"bg-primary text-primary-foreground":"bg-card border border-border text-foreground hover:bg-muted"}`,onClick:()=>t("transfer"),children:[(0,a.jsx)(m,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1"}),"Transfer"]}),(0,a.jsx)("button",{className:`px-3 sm:px-4 py-2 mx-1 rounded-md text-xs sm:text-sm font-medium whitespace-nowrap ${"history"===e?"bg-primary text-primary-foreground":"bg-card border border-border text-foreground hover:bg-muted"}`,onClick:()=>t("history"),children:"History"})]})}),"overview"===e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 mb-4 sm:mb-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsx)(N,{})}),(0,a.jsx)("div",{children:(0,a.jsx)(tI,{})})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:gap-6",children:(0,a.jsx)(tW,{})})]}),"add-funds"===e&&(0,a.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4 sm:p-6",children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-3 sm:mb-4 text-foreground",children:"Add Virtual Funds"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",children:"Add virtual funds to your paper trading accounts"}),r&&(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-muted rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-foreground",children:"Current Balance:"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-foreground",children:["$",r.balance.current.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-foreground",children:"Maximum Balance:"}),(0,a.jsxs)("span",{className:"text-sm text-foreground",children:["$",r.balance.maximum.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-foreground",children:"Available to Add:"}),(0,a.jsxs)("span",{className:"text-sm text-green-600 dark:text-green-400",children:["$",(r.balance.maximum-r.balance.current).toLocaleString()]})]})]}),(0,a.jsx)("div",{className:"bg-primary/10 border border-primary/20 rounded-md p-4",children:(0,a.jsxs)("div",{className:"text-sm text-primary",children:[(0,a.jsx)("p",{className:"font-medium mb-2",children:"Paper Trading Info:"}),(0,a.jsxs)("ul",{className:"list-disc pl-5 space-y-1 text-primary/80",children:[(0,a.jsx)("li",{children:"Virtual funds are for simulation purposes only"}),(0,a.jsx)("li",{children:"No real money is involved in paper trading"}),(0,a.jsx)("li",{children:"Perfect for learning and strategy testing"}),(0,a.jsx)("li",{children:"Reset balances anytime from the account manager"}),"free"===r.subscription.type&&(0,a.jsx)("li",{children:"Upgrade your plan to increase balance limits"})]})]})}),(0,a.jsx)("button",{onClick:()=>c(!0),className:"w-full bg-primary text-primary-foreground py-2 rounded-md font-medium hover:bg-primary/90",disabled:r.balance.current>=r.balance.maximum,children:r.balance.current>=r.balance.maximum?"Balance Limit Reached":"Add Virtual Funds"})]})]}),"transfer"===e&&(0,a.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4 sm:p-6",children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-3 sm:mb-4 text-foreground",children:"Transfer Between Accounts"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",children:"Transfer virtual funds between your paper trading accounts"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-foreground mb-1",children:"From Account"}),(0,a.jsxs)("select",{className:"w-full border border-border rounded-md px-3 py-2 bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",children:[(0,a.jsx)("option",{children:"Futures Trading ($50,000.00)"}),(0,a.jsx)("option",{disabled:!0,children:"Margin Trading (Coming Soon)"}),(0,a.jsx)("option",{disabled:!0,children:"Options Trading (Coming Soon)"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-foreground mb-1",children:"To Account"}),(0,a.jsxs)("select",{className:"w-full border border-border rounded-md px-3 py-2 bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",children:[(0,a.jsx)("option",{disabled:!0,children:"Margin Trading (Coming Soon)"}),(0,a.jsx)("option",{disabled:!0,children:"Futures Trading (Same Account)"}),(0,a.jsx)("option",{disabled:!0,children:"Options Trading (Coming Soon)"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-foreground mb-1",children:"Amount (USD)"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("input",{type:"number",placeholder:"1000.00",className:"w-full border border-border rounded-l-md px-3 py-2 bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"}),(0,a.jsx)("button",{className:"bg-muted text-foreground px-4 rounded-r-md border-t border-r border-b border-border hover:bg-muted/80",children:"MAX"})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-1",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Available: $50,000.00"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"No transfer fees for paper trading"})]})]})]}),(0,a.jsxs)("div",{className:"bg-primary/10 border border-primary/20 rounded-md p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-primary",children:"Transfer Amount"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary",children:"$1,000.00"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-primary",children:"Transfer Fee"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary",children:"$0.00"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-primary",children:"You Will Receive"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary",children:"$1,000.00"})]})]}),(0,a.jsx)("button",{className:"w-full bg-primary text-primary-foreground py-2 rounded-md font-medium hover:bg-primary/90",children:"Transfer Funds"})]}),"history"===e&&(0,a.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4 sm:p-6",children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-3 sm:mb-4 text-foreground",children:"Account History"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",children:"View your paper trading account transactions and balance changes"}),(0,a.jsx)(tW,{})]}),r&&(0,a.jsx)(tO,{isOpen:s,onClose:()=>c(!1),currentBalance:r.balance.current,maxBalance:r.balance.maximum,subscriptionType:r.subscription.type})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[128,658,817,452,277,895,887],()=>r(48750));module.exports=n})();